!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[HeaderContainer_DefaultWithFillLayers]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[HeaderContainer_DefaultWithFillLayers]"]=t(require("react")):e["rb_wixui.thunderbolt[HeaderContainer_DefaultWithFillLayers]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={96114:function(e,t,i){var n;!function(t){"use strict";var a=function(){},r=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.msRequestAnimationFrame||function(e){return setTimeout(e,16)};function o(){var e=this;e.reads=[],e.writes=[],e.raf=r.bind(t),a("initialized",e)}function s(e){e.scheduled||(e.scheduled=!0,e.raf(c.bind(null,e)),a("flush scheduled"))}function c(e){a("flush");var t,i=e.writes,n=e.reads;try{a("flushing reads",n.length),e.runTasks(n),a("flushing writes",i.length),e.runTasks(i)}catch(e){t=e}if(e.scheduled=!1,(n.length||i.length)&&s(e),t){if(a("task errored",t.message),!e.catch)throw t;e.catch(t)}}function d(e,t){var i=e.indexOf(t);return!!~i&&!!e.splice(i,1)}o.prototype={constructor:o,runTasks:function(e){var t;for(a("run tasks");t=e.shift();)t()},measure:function(e,t){a("measure");var i=t?e.bind(t):e;return this.reads.push(i),s(this),i},mutate:function(e,t){a("mutate");var i=t?e.bind(t):e;return this.writes.push(i),s(this),i},clear:function(e){return a("clear",e),d(this.reads,e)||d(this.writes,e)},extend:function(e){if(a("extend",e),"object"!=typeof e)throw new Error("expected object");var t=Object.create(this);return function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])}(t,e),t.fastdom=this,t.initialize&&t.initialize(),t},catch:null};var l=t.fastdom=t.fastdom||new o;void 0===(n=function(){return l}.call(l,i,l,e))||(e.exports=n)}("undefined"!=typeof window?window:void 0!==this?this:globalThis)},5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)({}).hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},i={};function n(e){var a=i[e];if(void 0!==a)return a.exports;var r=i[e]={exports:{}};return t[e].call(r.exports,r,r.exports,n),r.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};return function(){"use strict";n.r(a),n.d(a,{components:function(){return ni}});var e={};n.r(e),n.d(e,{STATIC_MEDIA_URL:function(){return qe},ph:function(){return De}});var t=n(448),i=n.n(t),r=n(5329),o=n.n(r);function s(e){var t,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(i=s(e[t]))&&(n&&(n+=" "),n+=i);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}var c=function(){for(var e,t,i=0,n="";i<arguments.length;)(e=arguments[i++])&&(t=s(e))&&(n&&(n+=" "),n+=t);return n};var d=e=>{let{id:t,className:n,skinClassName:a,tagName:r="div",transition:s,transitionEnded:d,eventHandlers:l,skinStyles:u,children:h,tabIndex:g,lang:m}=e;const f=r;return o().createElement(f,i()({id:t,className:c(a,s&&u[s],d&&u.transitionEnded,n),tabIndex:g,lang:m},l),h)},l="jhxvbR";const u="v1",h=2,g=1920,m=1920,f=1e3,p=1e3,_={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},T={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},I={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},E={[I.CENTER]:{x:.5,y:.5},[I.TOP_LEFT]:{x:0,y:0},[I.TOP_RIGHT]:{x:1,y:0},[I.TOP]:{x:.5,y:0},[I.BOTTOM_LEFT]:{x:0,y:1},[I.BOTTOM_RIGHT]:{x:1,y:1},[I.BOTTOM]:{x:.5,y:1},[I.RIGHT]:{x:1,y:.5},[I.LEFT]:{x:0,y:.5}},L={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},w={BG:"bg",IMG:"img",SVG:"svg"},b={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},A={classic:1,super:2},y={radius:"0.66",amount:"1.00",threshold:"0.01"},O={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},R=25e6,v=[1.5,2,4],M={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},C={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},G={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},F={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},S={AVIF:"AVIF",PAVIF:"PAVIF"};F.JPG,F.JPEG,F.JPE,F.PNG,F.GIF,F.WEBP;function N(e,...t){return function(...i){const n=i[i.length-1]||{},a=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?i[t]:n[t];a.push(o,e[r+1])})),a.join("")}}function P(e){return e[e.length-1]}const x=[F.PNG,F.JPEG,F.JPG,F.JPE,F.WIX_ICO_MP,F.WIX_MP,F.WEBP,F.AVIF],k=[F.JPEG,F.JPG,F.JPE];function H(e,t,i){return i&&t&&!(!(n=t.id)||!n.trim()||"none"===n.toLowerCase())&&Object.values(_).includes(e);var n}function B(e,t,i){return function(e,t,i=!1){return!((U(e)||j(e))&&t&&!i)}(e,t,i)&&(function(e){return x.includes(Z(e))}(e)||function(e,t=!1){return $(e)&&t}(e,i))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function Y(e){return Z(e)===F.PNG}function U(e){return Z(e)===F.WEBP}function $(e){return Z(e)===F.GIF}function j(e){return Z(e)===F.AVIF}const z=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),V=["\\.","\\*"],D="_";function W(e){return function(e){return k.includes(Z(e))}(e)?F.JPG:Y(e)?F.PNG:U(e)?F.WEBP:$(e)?F.GIF:j(e)?F.AVIF:F.UNRECOGNIZED}function Z(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function q(e,t,i,n,a){let r;return r=a===T.FILL?function(e,t,i,n){return Math.max(i/e,n/t)}(e,t,i,n):a===T.FIT?function(e,t,i,n){return Math.min(i/e,n/t)}(e,t,i,n):1,r}function J(e,t,i,n,a,r){e=e||n.width,t=t||n.height;const{scaleFactor:o,width:s,height:c}=function(e,t,i,n,a){let r,o=i,s=n;if(r=q(e,t,i,n,a),a===T.FIT&&(o=e*r,s=t*r),o&&s&&o*s>R){const i=Math.sqrt(R/(o*s));o*=i,s*=i,r=q(e,t,o,s,a)}return{scaleFactor:r,width:o,height:s}}(e,t,n.width*a,n.height*a,i);return function(e,t,i,n,a,r,o){const{optimizedScaleFactor:s,upscaleMethodValue:c,forceUSM:d}=function(e,t,i,n){if("auto"===n)return function(e,t){const i=ee(e,t);return{optimizedScaleFactor:M[i].maxUpscale,upscaleMethodValue:A.classic,forceUSM:!1}}(e,t);if("super"===n)return function(e){return{optimizedScaleFactor:P(v),upscaleMethodValue:A.super,forceUSM:!(v.includes(e)||e>P(v))}}(i);return function(e,t){const i=ee(e,t);return{optimizedScaleFactor:M[i].maxUpscale,upscaleMethodValue:A.classic,forceUSM:!1}}(e,t)}(e,t,r,a);let l=i,u=n;if(r<=s)return{width:l,height:u,scaleFactor:r,upscaleMethodValue:c,forceUSM:d,cssUpscaleNeeded:!1};switch(o){case T.FILL:l=i*(s/r),u=n*(s/r);break;case T.FIT:l=e*s,u=t*s}return{width:l,height:u,scaleFactor:s,upscaleMethodValue:c,forceUSM:d,cssUpscaleNeeded:!0}}(e,t,s,c,r,o,i)}function X(e,t,i,n){const a=Q(i)||function(e=I.CENTER){return E[e]}(n);return{x:Math.max(0,Math.min(e.width-t.width,a.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,a.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function K(e){return e.alignment&&L[e.alignment]||L[I.CENTER]}function Q(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:te(Math.max(0,Math.min(100,e.x))/100,2),y:te(Math.max(0,Math.min(100,e.y))/100,2)}),t}function ee(e,t){const i=e*t;return i>M[C.HIGH].size?C.HIGH:i>M[C.MEDIUM].size?C.MEDIUM:i>M[C.LOW].size?C.LOW:C.TINY}function te(e,t){const i=Math.pow(10,t||0);return(e*i/i).toFixed(t)}function ie(e){return e&&e.upscaleMethod&&b[e.upscaleMethod.toUpperCase()]||b.AUTO}function ne(e,t){const i=U(e)||j(e);return Z(e)===F.GIF||i&&t}const ae={isMobile:!1},re=function(e){return ae[e]};function oe(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,i=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&i,ae["isMobile"]=e}var e}function se(e,t){const i={css:{container:{}}},{css:n}=i,{fittingType:a}=e;switch(a){case _.ORIGINAL_SIZE:case _.LEGACY_ORIGINAL_SIZE:case _.LEGACY_STRIP_ORIGINAL_SIZE:n.container.backgroundSize="auto",n.container.backgroundRepeat="no-repeat";break;case _.SCALE_TO_FIT:case _.LEGACY_STRIP_SCALE_TO_FIT:n.container.backgroundSize="contain",n.container.backgroundRepeat="no-repeat";break;case _.STRETCH:n.container.backgroundSize="100% 100%",n.container.backgroundRepeat="no-repeat";break;case _.SCALE_TO_FILL:case _.LEGACY_STRIP_SCALE_TO_FILL:n.container.backgroundSize="cover",n.container.backgroundRepeat="no-repeat";break;case _.TILE_HORIZONTAL:case _.LEGACY_STRIP_TILE_HORIZONTAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-x";break;case _.TILE_VERTICAL:case _.LEGACY_STRIP_TILE_VERTICAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-y";break;case _.TILE:case _.LEGACY_STRIP_TILE:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat";break;case _.LEGACY_STRIP_FIT_AND_TILE:n.container.backgroundSize="contain",n.container.backgroundRepeat="repeat";break;case _.FIT_AND_TILE:case _.LEGACY_BG_FIT_AND_TILE:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat";break;case _.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-x";break;case _.LEGACY_BG_FIT_AND_TILE_VERTICAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="repeat-y";break;case _.LEGACY_BG_NORMAL:n.container.backgroundSize="auto",n.container.backgroundRepeat="no-repeat"}switch(t.alignment){case I.CENTER:n.container.backgroundPosition="center center";break;case I.LEFT:n.container.backgroundPosition="left center";break;case I.RIGHT:n.container.backgroundPosition="right center";break;case I.TOP:n.container.backgroundPosition="center top";break;case I.BOTTOM:n.container.backgroundPosition="center bottom";break;case I.TOP_RIGHT:n.container.backgroundPosition="right top";break;case I.TOP_LEFT:n.container.backgroundPosition="left top";break;case I.BOTTOM_RIGHT:n.container.backgroundPosition="right bottom";break;case I.BOTTOM_LEFT:n.container.backgroundPosition="left bottom"}return i}const ce={[I.CENTER]:"center",[I.TOP]:"top",[I.TOP_LEFT]:"top left",[I.TOP_RIGHT]:"top right",[I.BOTTOM]:"bottom",[I.BOTTOM_LEFT]:"bottom left",[I.BOTTOM_RIGHT]:"bottom right",[I.LEFT]:"left",[I.RIGHT]:"right"},de={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function le(e,t){const i={css:{container:{},img:{}}},{css:n}=i,{fittingType:a}=e,r=t.alignment;switch(n.container.position="relative",a){case _.ORIGINAL_SIZE:case _.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=e.src.width,n.img.height=e.src.height);break;case _.SCALE_TO_FIT:case _.LEGACY_FIT_WIDTH:case _.LEGACY_FIT_HEIGHT:case _.LEGACY_FULL:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="contain",n.img.objectPosition=ce[r]||"unset";break;case _.LEGACY_BG_NORMAL:n.img.width="100%",n.img.height="100%",n.img.objectFit="none",n.img.objectPosition=ce[r]||"unset";break;case _.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="fill";break;case _.SCALE_TO_FILL:n.img.width=t.width,n.img.height=t.height,n.img.objectFit="cover"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){const e=Math.round((t.height-n.img.height)/2),i=Math.round((t.width-n.img.width)/2);Object.assign(n.img,de,function(e,t,i){return{[I.TOP_LEFT]:{top:0,left:0},[I.TOP_RIGHT]:{top:0,right:0},[I.TOP]:{top:0,left:t},[I.BOTTOM_LEFT]:{bottom:0,left:0},[I.BOTTOM_RIGHT]:{bottom:0,right:0},[I.BOTTOM]:{bottom:0,left:t},[I.RIGHT]:{top:e,right:0},[I.LEFT]:{top:e,left:0},[I.CENTER]:{width:i.width,height:i.height,objectFit:"none"}}}(e,i,t)[r])}return i}function ue(e,t){const i={css:{container:{}},attr:{container:{},img:{}}},{css:n,attr:a}=i,{fittingType:r}=e,o=t.alignment,{width:s,height:c}=e.src;let d;switch(n.container.position="relative",r){case _.ORIGINAL_SIZE:case _.LEGACY_ORIGINAL_SIZE:case _.TILE:e.parts&&e.parts.length?(a.img.width=e.parts[0].width,a.img.height=e.parts[0].height):(a.img.width=s,a.img.height=c),a.img.preserveAspectRatio="xMidYMid slice";break;case _.SCALE_TO_FIT:case _.LEGACY_FIT_WIDTH:case _.LEGACY_FIT_HEIGHT:case _.LEGACY_FULL:a.img.width="100%",a.img.height="100%",a.img.transform="",a.img.preserveAspectRatio="";break;case _.STRETCH:a.img.width=t.width,a.img.height=t.height,a.img.x=0,a.img.y=0,a.img.transform="",a.img.preserveAspectRatio="none";break;case _.SCALE_TO_FILL:B(e.src.id)?(a.img.width=t.width,a.img.height=t.height):(d=function(e,t,i,n,a){const r=q(e,t,i,n,a);return{width:Math.round(e*r),height:Math.round(t*r)}}(s,c,t.width,t.height,T.FILL),a.img.width=d.width,a.img.height=d.height),a.img.x=0,a.img.y=0,a.img.transform="",a.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof a.img.width&&"number"==typeof a.img.height&&(a.img.width!==t.width||a.img.height!==t.height)){let e,i,n=0,s=0;r===_.TILE?(e=t.width%a.img.width,i=t.height%a.img.height):(e=t.width-a.img.width,i=t.height-a.img.height);const c=Math.round(e/2),d=Math.round(i/2);switch(o){case I.TOP_LEFT:n=0,s=0;break;case I.TOP:n=c,s=0;break;case I.TOP_RIGHT:n=e,s=0;break;case I.LEFT:n=0,s=d;break;case I.CENTER:n=c,s=d;break;case I.RIGHT:n=e,s=d;break;case I.BOTTOM_LEFT:n=0,s=i;break;case I.BOTTOM:n=c,s=i;break;case I.BOTTOM_RIGHT:n=e,s=i}a.img.x=n,a.img.y=s}return a.container.width=t.width,a.container.height=t.height,a.container.viewBox=[0,0,t.width,t.height].join(" "),i}function he(e,t,i){let n;switch(t.crop&&(n=function(e,t){const i=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),n=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return i&&n&&(e.width!==i||e.height!==n)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:i,height:n}:null}(t,t.crop),n&&(e.src.width=n.width,e.src.height=n.height,e.src.isCropped=!0,e.parts.push(me(n)))),e.fittingType){case _.SCALE_TO_FIT:case _.LEGACY_FIT_WIDTH:case _.LEGACY_FIT_HEIGHT:case _.LEGACY_FULL:case _.FIT_AND_TILE:case _.LEGACY_BG_FIT_AND_TILE:case _.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case _.LEGACY_BG_FIT_AND_TILE_VERTICAL:case _.LEGACY_BG_NORMAL:e.parts.push(ge(e,i));break;case _.SCALE_TO_FILL:e.parts.push(function(e,t){const i=J(e.src.width,e.src.height,T.FILL,t,e.devicePixelRatio,e.upscaleMethod),n=Q(e.focalPoint);return{transformType:n?T.FILL_FOCAL:T.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:K(t),focalPointX:n&&n.x,focalPointY:n&&n.y,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}(e,i));break;case _.STRETCH:e.parts.push(function(e,t){const i=q(e.src.width,e.src.height,t.width,t.height,T.FILL),n={...t};return n.width=e.src.width*i,n.height=e.src.height*i,ge(e,n)}(e,i));break;case _.TILE_HORIZONTAL:case _.TILE_VERTICAL:case _.TILE:case _.LEGACY_ORIGINAL_SIZE:case _.ORIGINAL_SIZE:n=X(e.src,i,e.focalPoint,i.alignment),e.src.isCropped?(Object.assign(e.parts[0],n),e.src.width=n.width,e.src.height=n.height):e.parts.push(me(n));break;case _.LEGACY_STRIP_TILE_HORIZONTAL:case _.LEGACY_STRIP_TILE_VERTICAL:case _.LEGACY_STRIP_TILE:case _.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:T.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:K(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case _.LEGACY_STRIP_SCALE_TO_FIT:case _.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:T.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case _.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:T.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:K(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i))}}function ge(e,t){const i=J(e.src.width,e.src.height,T.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?T.FIT:T.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:L.center,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}function me(e){return{transformType:T.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function fe(e,t){t=t||{},e.quality=function(e,t){const i=e.fileType===F.PNG,n=e.fileType===F.JPG,a=e.fileType===F.WEBP,r=e.fileType===F.AVIF,o=n||i||a||r;if(o){const n=P(e.parts),a=(s=n.width,c=n.height,M[ee(s,c)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:a;return r=i?r+5:r,r}var s,c;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,i="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,n="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&i&&n}(t.unsharpMask))return{radius:te(t.unsharpMask?.radius,2),amount:te(t.unsharpMask?.amount,2),threshold:te(t.unsharpMask?.threshold,2)};if(("number"!=typeof(i=(i=t.unsharpMask)||{}).radius||isNaN(i.radius)||0!==i.radius||"number"!=typeof i.amount||isNaN(i.amount)||0!==i.amount||"number"!=typeof i.threshold||isNaN(i.threshold)||0!==i.threshold)&&function(e){const t=P(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===T.FIT}(e))return y;var i;return}(e,t),e.filters=function(e){const t=e.filters||{},i={};pe(t[G.CONTRAST],-100,100)&&(i[G.CONTRAST]=t[G.CONTRAST]);pe(t[G.BRIGHTNESS],-100,100)&&(i[G.BRIGHTNESS]=t[G.BRIGHTNESS]);pe(t[G.SATURATION],-100,100)&&(i[G.SATURATION]=t[G.SATURATION]);pe(t[G.HUE],-180,180)&&(i[G.HUE]=t[G.HUE]);pe(t[G.BLUR],0,100)&&(i[G.BLUR]=t[G.BLUR]);return i}(t)}function pe(e,t,i){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=i}function _e(e,t,i,n){const a=function(e){return e?.isSEOBot??!1}(n),r=W(t.id),o=function(e,t){const i=/\.([^.]*)$/,n=new RegExp(`(${z.concat(V).join("|")})`,"g");if(t&&t.length){let e=t;const a=t.match(i);return a&&x.includes(a[1])&&(e=t.replace(i,"")),encodeURIComponent(e).replace(n,D)}const a=e.match(/\/(.*?)$/);return(a?a[1]:e).replace(i,"")}(t.id,t.name),s=a?1:function(e){return Math.min(e.pixelAspectRatio||1,h)}(i),c=Z(t.id),d=c,l=B(t.id,n?.hasAnimation,n?.allowAnimatedTransform),u={fileName:o,fileExtension:c,fileType:r,fittingType:e,preferredExtension:d,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:ne(t.id,n?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:s,quality:0,upscaleMethod:ie(n),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:l};return l&&(he(u,t,i),fe(u,n)),u}function Te(e,t,i){const n={...i},a=re("isMobile");switch(e){case _.LEGACY_BG_FIT_AND_TILE:case _.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case _.LEGACY_BG_FIT_AND_TILE_VERTICAL:case _.LEGACY_BG_NORMAL:const e=a?f:g,i=a?p:m;n.width=Math.min(e,t.width),n.height=Math.min(i,Math.round(n.width/(t.width/t.height))),n.pixelAspectRatio=1}return n}const Ie=N`fit/w_${"width"},h_${"height"}`,Ee=N`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Le=N`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,we=N`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,be=N`crop/w_${"width"},h_${"height"},al_${"alignment"}`,Ae=N`fill/w_${"width"},h_${"height"},al_${"alignment"}`,ye=N`,lg_${"upscaleMethodValue"}`,Oe=N`,q_${"quality"}`,Re=N`,quality_auto`,ve=N`,usm_${"radius"}_${"amount"}_${"threshold"}`,Me=N`,bl`,Ce=N`,wm_${"watermark"}`,Ge={[G.CONTRAST]:N`,con_${"contrast"}`,[G.BRIGHTNESS]:N`,br_${"brightness"}`,[G.SATURATION]:N`,sat_${"saturation"}`,[G.HUE]:N`,hue_${"hue"}`,[G.BLUR]:N`,blur_${"blur"}`},Fe=N`,enc_auto`,Se=N`,enc_avif`,Ne=N`,enc_pavif`,Pe=N`,pstr`;function xe(e,t,i,n={},a){if(B(t.id,n?.hasAnimation,n?.allowAnimatedTransform)){if(U(t.id)||j(t.id)){const{alignment:r,...o}=i;t.focalPoint={x:void 0,y:void 0},delete t?.crop,a=_e(e,t,o,n)}else a=a||_e(e,t,i,n);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case T.CROP:t.push(we(e));break;case T.LEGACY_CROP:t.push(be(e));break;case T.LEGACY_FILL:let i=Ae(e);e.upscale&&(i+=ye(e)),t.push(i);break;case T.FIT:let n=Ie(e);e.upscale&&(n+=ye(e)),t.push(n);break;case T.FILL:let a=Ee(e);e.upscale&&(a+=ye(e)),t.push(a);break;case T.FILL_FOCAL:let r=Le(e);e.upscale&&(r+=ye(e)),t.push(r)}}));let i=t.join("/");return e.quality&&(i+=Oe(e)),e.unsharpMask&&(i+=ve(e.unsharpMask)),e.progressive||(i+=Me(e)),e.watermark&&(i+=Ce(e)),e.filters&&(i+=Object.keys(e.filters).map((t=>Ge[t](e.filters))).join("")),e.fileType!==F.GIF&&(e.encoding===S.AVIF?(i+=Se(e),i+=Re(e)):e.encoding===S.PAVIF?(i+=Ne(e),i+=Re(e)):e.autoEncode&&(i+=Fe(e))),e.src?.isAnimated&&e.transformed&&(i+=Pe(e)),`${e.src.id}/${u}/${i}/${e.fileName}.${e.preferredExtension}`}(a)}return t.id}const ke={[I.CENTER]:"50% 50%",[I.TOP_LEFT]:"0% 0%",[I.TOP_RIGHT]:"100% 0%",[I.TOP]:"50% 0%",[I.BOTTOM_LEFT]:"0% 100%",[I.BOTTOM_RIGHT]:"100% 100%",[I.BOTTOM]:"50% 100%",[I.RIGHT]:"100% 50%",[I.LEFT]:"0% 50%"},He=Object.entries(ke).reduce(((e,[t,i])=>(e[i]=t,e)),{}),Be=[_.TILE,_.TILE_HORIZONTAL,_.TILE_VERTICAL,_.LEGACY_BG_FIT_AND_TILE,_.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,_.LEGACY_BG_FIT_AND_TILE_VERTICAL],Ye=[_.LEGACY_ORIGINAL_SIZE,_.ORIGINAL_SIZE,_.LEGACY_BG_NORMAL];function Ue(e,t,{width:i,height:n}){return e===_.TILE&&t.width>i&&t.height>n}function $e(e,{width:t,height:i}){if(!t||!i){const n=t||Math.min(980,e.width),a=n/e.width;return{width:n,height:i||e.height*a}}return{width:t,height:i}}function je(e,t,i,n="center"){const a={img:{},container:{}};if(e===_.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return He[t]||""}(t.focalPoint),r=e||n;t.focalPoint&&!e?a.img={objectPosition:ze(t,i,t.focalPoint)}:a.img={objectPosition:ke[r]}}else[_.LEGACY_ORIGINAL_SIZE,_.ORIGINAL_SIZE].includes(e)?a.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:Be.includes(e)&&(a.container={backgroundSize:`${t.width}px ${t.height}px`});return a}function ze(e,t,i){const{width:n,height:a}=e,{width:r,height:o}=t,{x:s,y:c}=i;if(!r||!o)return`${s}% ${c}%`;const d=Math.max(r/n,o/a),l=n*d,u=a*d,h=Math.max(0,Math.min(l-r,l*(s/100)-r/2)),g=Math.max(0,Math.min(u-o,u*(c/100)-o/2));return`${h&&Math.floor(h/(l-r)*100)}% ${g&&Math.floor(g/(u-o)*100)}%`}const Ve={width:"100%",height:"100%"};function De(e,t,i,n={}){const{autoEncode:a=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:s,allowAnimatedTransform:c,encoding:d}=n;if(!H(e,t,i))return O;const l=void 0===c||c,u=B(t.id,s,l);if(!u||o)return We(e,t,i,{...n,autoEncode:a,useSrcset:u});const h={...i,...$e(t,i)},{alignment:g,htmlTag:m}=h,f=Ue(e,t,h),p=function(e,t,{width:i,height:n},a=!1){if(a)return{width:i,height:n};const r=!Ye.includes(e),o=Ue(e,t,{width:i,height:n}),s=!o&&Be.includes(e),c=s?t.width:i,d=s?t.height:n,l=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(c,Y(t.id)):1;return{width:o?1920:c*l,height:d*l}}(e,t,h,r),T=function(e,t,i){return i?0:Be.includes(t)?1:e>200?2:3}(h.width,e,r),I=function(e,t){const i=Be.includes(e)&&!t;return e===_.SCALE_TO_FILL||i?_.SCALE_TO_FIT:e}(e,f),E=je(e,t,i,g),{uri:L}=We(I,t,{...p,alignment:g,htmlTag:m},{autoEncode:a,filters:T?{blur:T}:{},hasAnimation:s,allowAnimatedTransform:l,encoding:d}),{attr:w={},css:b}=We(e,t,{...h,alignment:g,htmlTag:m},{});return b.img=b.img||{},b.container=b.container||{},Object.assign(b.img,E.img,Ve),Object.assign(b.container,E.container),{uri:L,css:b,attr:w,transformed:!0}}function We(e,t,i,n){let a={};if(H(e,t,i)){const r=Te(e,t,i),o=_e(e,t,r,n);a.uri=xe(e,t,r,n,o),n?.useSrcset&&(a.srcset=function(e,t,i,n,a){const r=i.pixelAspectRatio||1;return{dpr:[`${1===r?a.uri:xe(e,t,{...i,pixelAspectRatio:1},n)} 1x`,`${2===r?a.uri:xe(e,t,{...i,pixelAspectRatio:2},n)} 2x`]}}(e,t,r,n,a)),Object.assign(a,function(e,t){let i;return i=t.htmlTag===w.BG?se:t.htmlTag===w.SVG?ue:le,i(e,t)}(o,r),{transformed:o.transformed})}else a=O;return a}const Ze="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;oe();oe();const qe=Ze,{STATIC_MEDIA_URL:Je}=e,Xe=({fittingType:e,src:t,target:i,options:n})=>{const a=De(e,t,i,{...n,autoEncode:!0});return a?.uri&&!/^[a-z]+:/.test(a.uri)&&(a.uri=`${Je}${a.uri}`),a},Ke=/^[a-z]+:/,Qe=e=>{const{id:t,containerId:i,uri:n,alt:a,name:o="",role:s,width:c,height:d,displayMode:u,devicePixelRatio:h,quality:g,alignType:m,bgEffectName:f="",focalPoint:p,upscaleMethod:_,className:T="",crop:I,imageStyles:E={},targetWidth:L,targetHeight:w,targetScale:b,onLoad:A=()=>{},onError:y=()=>{},shouldUseLQIP:O,containerWidth:R,containerHeight:v,getPlaceholder:M,isInFirstFold:C,placeholderTransition:G,socialAttrs:F,isSEOBot:S,skipMeasure:N,hasAnimation:P,encoding:x}=e,k=r.useRef(null);let H="";const B="blur"===G,Y=r.useRef(null);if(!Y.current)if(M||O||C||S){const e={upscaleMethod:_,...g||{},shouldLoadHQImage:C,isSEOBot:S,hasAnimation:P,encoding:x};Y.current=(M||Xe)({fittingType:u,src:{id:n,width:c,height:d,crop:I,name:o,focalPoint:p},target:{width:R,height:v,alignment:m,htmlTag:"img"},options:e}),H=!Y.current.transformed||C||S?"":"true"}else Y.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const U=!S&&(M||O)&&!C&&Y.current.transformed,$=r.useMemo((()=>JSON.stringify({containerId:i,...i&&{containerId:i},...m&&{alignType:m},...N&&{skipMeasure:!0},displayMode:u,...R&&{targetWidth:R},...v&&{targetHeight:v},...L&&{targetWidth:L},...w&&{targetHeight:w},...b&&{targetScale:b},isLQIP:U,isSEOBot:S,lqipTransition:G,encoding:x,imageData:{width:c,height:d,uri:n,name:o,displayMode:u,hasAnimation:P,...g&&{quality:g},...h&&{devicePixelRatio:h},...p&&{focalPoint:p},...I&&{crop:I},..._&&{upscaleMethod:_}}})),[i,m,N,u,R,v,L,w,b,U,S,G,x,c,d,n,o,P,g,h,p,I,_]),j=Y.current,z=j?.uri,V=j?.srcset,D=j.css?.img,W=`${l} ${T}`;r.useEffect((()=>{const e=k.current;A&&e?.currentSrc&&e?.complete&&A({target:e})}),[]);const Z=j&&!j?.transformed?`max(${c}px, 100%)`:L?`${L}px`:null;return r.createElement("wow-image",{id:t,class:W,"data-image-info":$,"data-motion-part":`BG_IMG ${i}`,"data-bg-effect-name":f,"data-has-ssr-src":H,"data-animate-blur":!S&&U&&B?"":void 0,style:Z?{"--wix-img-max-width":Z}:{}},r.createElement("img",{src:z,ref:k,alt:a||"",role:s,style:{...D,...E},onLoad:A,onError:y,width:R||void 0,height:v||void 0,...F,srcSet:C?V?.dpr?.map((e=>Ke.test(e)?e:`${Je}${e}`)).join(", "):void 0,fetchpriority:C?"high":void 0,loading:!1===C?"lazy":void 0,suppressHydrationWarning:!0}))};var et="Tj01hh";var tt=e=>{var t,n;const{id:a,alt:o,role:s,className:d,imageStyles:l={},targetWidth:u,targetHeight:h,onLoad:g,onError:m,containerWidth:f,containerHeight:p,isInFirstFold:_,socialAttrs:T,skipMeasure:I,responsiveImageProps:E,zoomedImageResponsiveOverride:L,displayMode:w}=e,b=u||f,A=h||p,{fallbackSrc:y,srcset:O,sources:R,css:v}=E||{},{width:M,height:C,...G}=(null==E||null==(t=E.css)?void 0:t.img)||{},F="original_size"===w?null==E||null==(n=E.css)?void 0:n.img:G;var S;return y&&O&&v?r.createElement("img",i()({fetchpriority:_?"high":void 0,loading:!1===_?"lazy":void 0,sizes:b+"px",srcSet:I?null==L?void 0:L.srcset:null==E?void 0:E.srcset,id:a,src:y,alt:o||"",role:s,style:{...l,...I?{...null==L||null==(S=L.css)?void 0:S.img}:{...F}},onLoad:g,onError:m,className:c(d,et),width:b,height:A},T)):y&&R&&v?r.createElement("picture",null,R.map((e=>{let{srcset:t,media:i,sizes:n}=e;return r.createElement("source",{key:i,srcSet:t,media:i,sizes:n})})),r.createElement("img",i()({fetchpriority:_?"high":void 0,loading:!1===_?"lazy":void 0,id:a,src:R[0].fallbackSrc,alt:o||"",role:s,style:{...l,objectFit:R[0].imgStyle.objectFit,objectPosition:R[0].imgStyle.objectPosition},onLoad:g,onError:m,className:c(d,et),width:b,height:A},T))):r.createElement(Qe,e)};var it=e=>{var t,i,n;const{className:a,customIdPrefix:o,getPlaceholder:s,hasAnimation:c,...d}=e,l=r.useMemo((()=>JSON.stringify({containerId:d.containerId,alignType:d.alignType,fittingType:d.displayMode,hasAnimation:c,imageData:{width:d.width,height:d.height,uri:d.uri,name:d.name,...d.quality&&{quality:d.quality},displayMode:d.displayMode}})),[d,c]),u=r.useRef(null);u.current||(u.current=s?s({fittingType:d.displayMode,src:{id:d.uri,width:d.width,height:d.height,name:d.name},target:{width:d.containerWidth,height:d.containerHeight,alignment:d.alignType,htmlTag:"bg"},options:{hasAnimation:c,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const h=u.current,g=null!=(t=null==h?void 0:h.uri)?t:"",m=null!=(i=null==(n=h.css)?void 0:n.container)?i:{},f=Object.assign(g?{backgroundImage:"url("+g+")"}:{},m);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+d.containerId,class:a,style:f,"data-tiled-image-info":l,"data-has-bg-scroll-effect":d.hasBgScrollEffect||"","data-bg-effect-name":d.bgEffectName||"","data-motion-part":"BG_IMG "+d.containerId})};const nt=new RegExp("<%= compId %>","g"),at=(e,t)=>e.replace(nt,t);var rt=e=>null==e?void 0:e.replace(":hover",""),ot="bX9O_S",st="Z_wCwr",ct="Jxk_UL",dt="K8MSra",lt="YTb3b4";const ut={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var ht=e=>{const{id:t,videoRef:n,videoInfo:a,posterImageInfo:o,muted:s,preload:d,loop:l,alt:u,isVideoEnabled:h,getPlaceholder:g,extraClassName:m=""}=e;a.containerId=rt(a.containerId);const f=r.useMemo((()=>JSON.stringify(a)),[a]),p=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+a.containerId,className:lt},r.createElement("defs",{dangerouslySetInnerHTML:{__html:at(o.filterEffectSvgString,a.containerId)}})),r.createElement(tt,i()({key:a.videoId+"_img",id:o.containerId+"_img",className:c(st,ct,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,ut,{getPlaceholder:g})));return h?r.createElement("wix-video",{id:t,"data-video-info":f,"data-motion-part":"BG_IMG "+a.containerId,class:c(ot,"bgVideo",m)},r.createElement("video",{key:a.videoId+"_video",ref:n,id:a.containerId+"_video",className:dt,crossOrigin:"anonymous","aria-label":u,playsInline:!0,preload:d,muted:s,loop:l}),p):p},gt="SUz0WK";var mt=e=>{const{id:t,containerId:i,pageId:n,children:a,bgEffectName:o="",containerSize:s}=e;return r.createElement("wix-bg-media",{id:t,class:gt,"data-container-id":i,"data-container-size":((null==s?void 0:s.width)||0)+", "+((null==s?void 0:s.height)||0),"data-page-id":n,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+i},a)};const ft="bgOverlay";var pt="m4khSP",_t="FNxOn5";var Tt=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":ft,className:pt},t&&r.createElement(it,i()({customIdPrefix:"bgImgOverlay_",className:_t},t)))};const It="bgLayers",Et="colorUnderlay",Lt="mediaPadding",wt="canvas";var bt="MW5IWV",At="N3eg0s",yt="Kv1aVt",Ot="dLPlxY",Rt="VgO9Yg",vt="LWbAav",Mt="yK6aSC",Ct="K_YxMd",Gt="NGjcJN",Ft="mNGsUM",St="I8xA4L";const Nt="bgImage";var Pt=e=>{const{videoRef:t,canvasRef:n,hasBgFullscreenScrollEffect:a,image:o,backgroundImage:s,backgroundMedia:d,video:l,backgroundOverlay:u,shouldPadMedia:h,extraClass:g="",shouldRenderUnderlay:m=!l,reducedMotion:f=!1,getPlaceholder:p,hasCanvasAnimation:_,useWixMediaCanvas:T,onClick:I}=e,{onImageLoad:E}=(e=>{let{onReady:t,image:i}=e;return(0,r.useEffect)((()=>{t&&!i&&t()}),[t,i]),{onImageLoad:e=>{null!=i&&i.onLoad&&i.onLoad(e),t&&t()}}})(e),L=rt(e.containerId),w="img_"+rt(L),b=o&&r.createElement(tt,i()({id:w,className:c(yt,Ot,Ft,Nt),imageStyles:{width:"100%",height:"100%"},getPlaceholder:p},o,{onLoad:E})),A=s&&r.createElement(it,i()({},s,{containerId:L,className:c(yt,Ot,Ft,Nt),getPlaceholder:p})),y=l&&r.createElement(ht,i()({id:"videoContainer_"+L},l,{extraClassName:Mt,reducedMotion:f,videoRef:t,getPlaceholder:p})),O=T&&n||_?r.createElement("wix-media-canvas",{"data-container-id":L,class:_?St:""},b,A,y,r.createElement("canvas",{id:L+"webglcanvas",className:c(Ct,"webglcanvas"),"aria-label":(null==l?void 0:l.alt)||"",role:"presentation","data-testid":wt})):r.createElement(r.Fragment,null,b,A,y,n&&r.createElement("canvas",{id:L+"webglcanvas",ref:n,className:c(Ct,"webglcanvas"),"aria-label":(null==l?void 0:l.alt)||"",role:"presentation","data-testid":wt})),R=d?r.createElement(mt,i()({id:"bgMedia_"+L},d),O):r.createElement("div",{id:"bgMedia_"+L,"data-motion-part":"BG_MEDIA "+L,className:Rt},O),v=u&&r.createElement(Tt,u);return r.createElement("div",{id:It+"_"+L,"data-hook":It,"data-motion-part":"BG_LAYER "+L,className:c(bt,g,{[At]:a}),onClick:I},m&&r.createElement("div",{"data-testid":Et,className:c(vt,yt)}),h?r.createElement("div",{"data-testid":Lt,className:Gt},R,v):r.createElement(r.Fragment,null,R,v))},xt=n(96114),kt=n.n(xt);const Ht=()=>"undefined"!=typeof window;function Bt(){if(!Ht())return{x:0,y:0,isAtPageBottom:!1};const{left:e,top:t}=document.body.getBoundingClientRect();return{x:e,y:t,isAtPageBottom:window.innerHeight+window.scrollY===document.body.scrollHeight}}function Yt(e,t,i){void 0===i&&(i={}),i={waitFor:100,disabled:!1,...i};const n=(0,r.useRef)(Bt());let a=null;const o=()=>{kt().measure((()=>{const t=Bt(),i=n.current;n.current=t,a=null,kt().mutate((()=>e({prevPos:i,currPos:t})))}))};(Ht()?r.useLayoutEffect:r.useEffect)((()=>{if(!Ht())return;const e=()=>{null===a&&(a=window.setTimeout(o,i.waitFor))};return i.disabled?()=>{}:(window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e),a&&window.clearTimeout(a)})}),t)}var Ut={screenWidthBackground:"TMFrcJ",HeaderHideToTop:"dkyyRB",headerHideToTop:"dkyyRB",HeaderHideToTopReverse:"L01Zxk",headerHideToTopReverse:"L01Zxk",HeaderFadeOut:"bFRsbd",headerFadeOut:"bFRsbd",transitionEnded:"CwYhEy",HeaderFadeOutReverse:"u_eaP3",headerFadeOutReverse:"u_eaP3",inlineContent:"kn76TK",centeredContent:"YTbrNX",centeredContentBg:"EwS2PT",DefaultWithFillLayers:"x4zVYf",defaultWithFillLayers:"x4zVYf",scrolled:"zxR1mn",fillLayers:"mTQGgy",veloBackground:"QijXjn"};var $t=e=>{let{wrapperProps:t,fillLayers:n,children:a}=e;const[o,s]=r.useState(!1);return Yt((e=>{let{currPos:t}=e;-1*t.y>=2?o||s(!0):o&&s(!1)}),[o]),r.createElement(d,i()({},t,{skinClassName:Ut.DefaultWithFillLayers,skinStyles:Ut}),r.createElement("div",{className:c(Ut.screenWidthBackground,o&&Ut.scrolled)},n&&r.createElement(Pt,i()({},n,{extraClass:Ut.fillLayers})),r.createElement("div",{className:Ut.veloBackground})),r.createElement("div",{className:Ut.inlineContent},a))};const jt="wixui-",zt=(e,...t)=>{const i=[];return e&&i.push(`${jt}${e}`),t.forEach((e=>{e&&(i.push(`${jt}${e}`),i.push(e))})),i.join(" ")},Vt="mesh-container-content",Dt="inline-content",Wt=e=>o().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),Zt=(e,t)=>{const{id:n,className:a,wedges:r=[],rotatedComponents:s=[],children:d,fixedComponents:l=[],extraClassName:u="",renderRotatedComponents:h=Wt}=e,g=o().Children.toArray(d()),m=[],f=[];g.forEach((e=>l.includes(e.props.id)?m.push(e):f.push(e)));const p=(e=>{const{wedges:t,rotatedComponents:i,childrenArray:n,renderRotatedComponents:a}=e,r=i.reduce(((e,t)=>({...e,[t]:!0})),{});return[...n.map((e=>{return r[(t=e,t.props.id.split("__")[0])]?a(e):e;var t})),...t.map((e=>o().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:f,rotatedComponents:s,wedges:r,renderRotatedComponents:h});return o().createElement("div",i()({},(e=>Object.entries(e).reduce(((e,[t,i])=>(t.includes("data-")&&(e[t]=i),e)),{}))(e),{"data-mesh-id":n+"inlineContent","data-testid":Dt,className:c(a,u),ref:t}),o().createElement("div",{"data-mesh-id":n+"inlineContent-gridContainer","data-testid":Vt},p),m)};var qt=o().forwardRef(Zt);var Jt={root:"header"};const Xt="Reverse",Kt="up",Qt="down";var ei=e=>{const{id:t,skin:n,children:a,animations:s,meshProps:d,className:l,customClassNames:u=[],fillLayers:h,lang:g}=e,[m,f]=(0,r.useState)(""),[p,_]=(0,r.useState)(!1),T=e=>{f(e),_(!1)};(0,r.useEffect)((()=>{window.TransitionEvent||setTimeout((()=>_(!0)),200)}),[m]);const I=m&&!(e=>e.endsWith(Xt))(m),E=()=>{const e=(e=>""+e+Xt)(m);T(e)},L={onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onClick:e.onClick,onDoubleClick:e.onDblClick,onFocus:I?E:void 0,onTransitionEnd:()=>_(!0)};let w=Qt,b=0;Yt((e=>{var t,i;let{currPos:n,prevPos:a}=e;const r=n.y&&-1*n.y,o=a.y&&-1*a.y,c=s[s.length-1],d=null==(t=c.params)||null==(t=t.animations)?void 0:t[c.params.animations.length-1];if(!d)return;const l="mobile"===(null==(i=c.viewMode)?void 0:i.toLowerCase())?1:(e=>{switch(e){case"HeaderFadeOut":return 200;case"HeaderHideToTop":return 400;default:return null}})(d.name);l&&(((e,t)=>{w===Qt&&e<t?(b=t,w=Kt):w===Kt&&e>t&&e>=0&&t>=0&&(b=t,w=Qt)})(r,o),I?(w===Kt&&r+l<b||0===n.y)&&E():w===Qt&&r-b>=l&&T(d.name))}),[m,s],{disabled:!s||!s.length});const A=c(l,zt(Jt.root,...u));return o().createElement(n,{wrapperProps:{id:t,tagName:"header",eventHandlers:L,className:A,transition:m,transitionEnded:p,tabIndex:"-1",lang:g},"data-block-level-container":"HeaderContainer",fillLayers:h},o().createElement(qt,i()({id:t},d,{children:a})))};const ti=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`));var ii;const ni={HeaderContainer_DefaultWithFillLayers:{component:e=>o().createElement(ei,i()({},e,{skin:$t})),controller:(ii=e=>{let{mapperProps:t,controllerUtils:i}=e;const{updateStyles:n}=i,{compId:a,marginTop:o,isMobileView:s,isFixed:c,...d}=t;var l;return l=()=>{var e;const t=((null==(e=window.document.getElementById(a))?void 0:e.clientHeight)||0)>=window.document.body.clientHeight/2;s&&c&&t&&n({position:"relative !important",marginTop:o,top:0})},(0,r.useEffect)(l,[]),d},{useComponentProps:(e,t,i)=>{const n=(e=>({...e,updateStyles:t=>{const i=Object.entries(t).reduce(((e,[t,i])=>{return{...e,[(n=t,n.startsWith("--")?t:ti(t))]:void 0===i?null:i};var n}),{});e.updateStyles(i)}}))(i);return ii({mapperProps:e,stateValues:t,controllerUtils:n})}})}}}(),a}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[HeaderContainer_DefaultWithFillLayers].a9a2af73.bundle.min.js.map