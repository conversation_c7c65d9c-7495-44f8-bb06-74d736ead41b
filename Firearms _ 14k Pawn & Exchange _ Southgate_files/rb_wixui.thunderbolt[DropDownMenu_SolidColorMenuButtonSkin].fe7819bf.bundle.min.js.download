!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("lodash"),require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[DropDownMenu_SolidColorMenuButtonSkin]",["lodash","react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[DropDownMenu_SolidColorMenuButtonSkin]"]=t(require("lodash"),require("react")):e["rb_wixui.thunderbolt[DropDownMenu_SolidColorMenuButtonSkin]"]=t(e._,e.React)}("undefined"!=typeof self?self:this,(function(e,t){return function(){var n={65549:function(e,t,n){var r=n(84457)(n(70441),"DataView");e.exports=r},64438:function(e,t,n){var r=n(84457)(n(70441),"Map");e.exports=r},87076:function(e,t,n){var r=n(84457)(n(70441),"Promise");e.exports=r},69902:function(e,t,n){var r=n(84457)(n(70441),"Set");e.exports=r},54690:function(e,t,n){var r=n(70441).Symbol;e.exports=r},18965:function(e,t,n){var r=n(84457)(n(70441),"WeakMap");e.exports=r},94318:function(e,t,n){var r=n(54690),o=n(47077),a=n(61954),i=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":i&&i in Object(e)?o(e):a(e)}},58520:function(e,t,n){var r=n(94318),o=n(3387);e.exports=function(e){return o(e)&&"[object Arguments]"==r(e)}},62987:function(e,t,n){var r=n(93839),o=n(47275),a=n(85973),i=n(76822),u=/^\[object .+?Constructor\]$/,l=Function.prototype,s=Object.prototype,c=l.toString,d=s.hasOwnProperty,p=RegExp("^"+c.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!a(e)||o(e))&&(r(e)?p:u).test(i(e))}},73749:function(e,t,n){var r=n(94318),o=n(99216),a=n(3387),i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1,e.exports=function(e){return a(e)&&o(e.length)&&!!i[r(e)]}},78803:function(e,t,n){var r=n(65003),o=n(41466),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}},76535:function(e){e.exports=function(e){return function(t){return e(t)}}},38507:function(e,t,n){var r=n(70441)["__core-js_shared__"];e.exports=r},34414:function(e,t,n){var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},84457:function(e,t,n){var r=n(62987),o=n(79741);e.exports=function(e,t){var n=o(e,t);return r(n)?n:void 0}},47077:function(e,t,n){var r=n(54690),o=Object.prototype,a=o.hasOwnProperty,i=o.toString,u=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,u),n=e[u];try{e[u]=void 0;var r=!0}catch(e){}var o=i.call(e);return r&&(t?e[u]=n:delete e[u]),o}},26686:function(e,t,n){var r=n(65549),o=n(64438),a=n(87076),i=n(69902),u=n(18965),l=n(94318),s=n(76822),c="[object Map]",d="[object Promise]",p="[object Set]",f="[object WeakMap]",b="[object DataView]",m=s(r),v=s(o),h=s(a),y=s(i),g=s(u),x=l;(r&&x(new r(new ArrayBuffer(1)))!=b||o&&x(new o)!=c||a&&x(a.resolve())!=d||i&&x(new i)!=p||u&&x(new u)!=f)&&(x=function(e){var t=l(e),n="[object Object]"==t?e.constructor:void 0,r=n?s(n):"";if(r)switch(r){case m:return b;case v:return c;case h:return d;case y:return p;case g:return f}return t}),e.exports=x},79741:function(e){e.exports=function(e,t){return null==e?void 0:e[t]}},47275:function(e,t,n){var r,o=n(38507),a=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";e.exports=function(e){return!!a&&a in e}},65003:function(e){var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},41466:function(e,t,n){var r=n(53717)(Object.keys,Object);e.exports=r},91782:function(e,t,n){e=n.nmd(e);var r=n(34414),o=t&&!t.nodeType&&t,a=o&&e&&!e.nodeType&&e,i=a&&a.exports===o&&r.process,u=function(){try{var e=a&&a.require&&a.require("util").types;return e||i&&i.binding&&i.binding("util")}catch(e){}}();e.exports=u},61954:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},53717:function(e){e.exports=function(e,t){return function(n){return e(t(n))}}},70441:function(e,t,n){var r=n(34414),o="object"==typeof self&&self&&self.Object===Object&&self,a=r||o||Function("return this")();e.exports=a},76822:function(e){var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},86981:function(e,t,n){var r=n(58520),o=n(3387),a=Object.prototype,i=a.hasOwnProperty,u=a.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(e){return o(e)&&i.call(e,"callee")&&!u.call(e,"callee")};e.exports=l},77236:function(e){var t=Array.isArray;e.exports=t},81580:function(e,t,n){var r=n(93839),o=n(99216);e.exports=function(e){return null!=e&&o(e.length)&&!r(e)}},98752:function(e,t,n){e=n.nmd(e);var r=n(70441),o=n(7149),a=t&&!t.nodeType&&t,i=a&&e&&!e.nodeType&&e,u=i&&i.exports===a?r.Buffer:void 0,l=(u?u.isBuffer:void 0)||o;e.exports=l},86834:function(e,t,n){var r=n(78803),o=n(26686),a=n(86981),i=n(77236),u=n(81580),l=n(98752),s=n(65003),c=n(14812),d=Object.prototype.hasOwnProperty;e.exports=function(e){if(null==e)return!0;if(u(e)&&(i(e)||"string"==typeof e||"function"==typeof e.splice||l(e)||c(e)||a(e)))return!e.length;var t=o(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if(s(e))return!r(e).length;for(var n in e)if(d.call(e,n))return!1;return!0}},93839:function(e,t,n){var r=n(94318),o=n(85973);e.exports=function(e){if(!o(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},99216:function(e){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},85973:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},3387:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},14812:function(e,t,n){var r=n(73749),o=n(76535),a=n(91782),i=a&&a.isTypedArray,u=i?o(i):r;e.exports=u},7149:function(e){e.exports=function(){return!1}},60484:function(t){"use strict";t.exports=e},5329:function(e){"use strict";e.exports=t},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={id:e,loaded:!1,exports:{}};return n[e](a,a.exports,o),a.loaded=!0,a.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e};var a={};return function(){"use strict";o.r(a),o.d(a,{components:function(){return ne}});var e=o(448),t=o.n(e),n=o(5329),r=o.n(n);function i(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=i(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var u=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=i(e))&&(r&&(r+=" "),r+=t);return r};const l=(e,t)=>e?{"data-comp":t,"data-aid":t}:{},s="wixui-",c=(e,...t)=>{const n=[];return e&&n.push(`${s}${e}`),t.forEach((e=>{e&&(n.push(`${s}${e}`),n.push(e))})),n.join(" ")},d=new Set(["PointerMenuButtonHorizontalMenuAdaptationSkin","PointerMenuButtonSkin","VerticalRibbonsMenuButtonSkin","RibbonsMenuButtonSkin"]),p="data-dropdown-shown",f="__more__",b="SCROLL_TO_TOP",m="SCROLL_TO_BOTTOM";var v={root:"dropdown-menu",menuItem:"dropdown-menu__item",subMenu:"dropdown-menu__submenu"};const h=13,y=27;function g(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const x=g(32),k=g(h),w=e=>{k(e),x(e)},I=(g(y),["aria-id","aria-metadata","aria-type"]),M=(e,t)=>Object.entries(e).reduce(((e,[n,r])=>(t.includes(n)||(e[n]=r),e)),{}),j=e=>{const{role:t,tabIndex:n,tabindex:r,screenReader:o,lang:a,ariaAttributes:i={}}=e,u=Object.entries(i).reduce(((e,[t,n])=>({...e,[`aria-${t}`.toLowerCase()]:n})),{});return{role:t,tabIndex:n??r,screenReader:o,ariaAttributes:M(u,I),lang:a}},C=e=>e.split("?")[0],S=(e,t)=>e.filter((e=>e.link&&e.link.linkPopupId&&e.link.linkPopupId===t)),E=(e,t,n)=>{const r=n.compId||n.dataId;let o=new Set;return r&&(o=new Set(e.filter((e=>((e,t,n)=>e.link&&(!e.link.href||C(e.link.href)===C(t))&&(e.link.anchorCompId&&e.link.anchorCompId===n.compId||e.link.anchorDataId&&e.link.anchorDataId===n.dataId&&e.link.anchorDataId!==b&&e.link.anchorDataId!==m))(e,t,n))))),o},B=function(e,t,n,r){void 0===e&&(e=[]),void 0===t&&(t=""),void 0===n&&(n={}),void 0===r&&(r="");const o=new Set([...Array.from(E(e,t,n)),...Array.from(S(e,r))]),a=o.size>0;return e.forEach((e=>{const i=e.items&&e.items.length?B(e.items,t,n,r):new Set;!1!==e.selected&&((e.selected||((e,t,n)=>!n&&!(e=>e.link&&(e.link.anchorDataId||e.link.anchorCompId))(e)&&e.link&&e.link.href&&decodeURIComponent(C(e.link.href))===C(t))(e,t,a)||e.link&&Object.keys(e.link).length>0&&i.size>0)&&o.add(e),i.forEach((e=>o.add(e))))})),o};function N(e,t){return""+e+t}function D(e,t){return e.filter(((e,n)=>{var r;const o=N(t,n.toString()),a=document.getElementById(o);return"hidden"===(null==a||null==(r=a.style)?void 0:r.visibility)}))}var P=o(86834),A=o.n(P),O=o(60484);const L=e=>{const r=n.useMemo((()=>B(e.items,e.currentUrl,e.activeAnchor,e.currentPopupId)),[e.items,e.currentUrl,e.activeAnchor,e.currentPopupId]),{ariaAttributes:o}=e,a=(e,t)=>{const{Button:r}=e,o={...{onMouseEnter:e.onItemMouseEnter,onMouseLeave:e.onItemMouseLeave,onDoubleClick:e.onItemDblClick,onClick:e.onItemClick,textAlign:e.alignText,translations:e.translations},...t};return n.createElement(r,o)};function i(e,t,n,r,o,a){return e===t-1?1===t?"dropLonely":n?"bottom":a||"right"===o?r?"left":"right":"center":0===e?n?"top":a||"left"===o?r?"right":"left":"center":n?"dropCenter":"center"}const l=(e,t)=>{let n=e,r=0;for(;t[n]&&r<100;)n+=t[n]++,r++;return t[n]=(t[n]||0)+1,n},s=(e,t)=>{let{items:n=[],compClassName:o,dropdown:u,rtl:s,buttonAlign:c,stretch:d}=t;const{hover:p}=e,f={};return n.map(((t,b)=>{var m,v,y,g;const x=null!=(m=t.hasPopup)?m:(null!=(v=null==t||null==(y=t.items)?void 0:y.length)?v:0)>0,k=(u?"moreContainer":"")+b,w={isContainer:u,isSelected:r.has(t),positionInList:t.positionInList||i(b,n.length,u,s,c,d),id:h(k),index:b,refInParent:k,isDropDownButton:u,...r.has(t)&&{"aria-current":"page"},...x&&{"aria-haspopup":"true","aria-expanded":!(0,O.isNil)(p)&&parseInt(p,10)===b},tagName:"li",direction:s?"rtl":"ltr",parentId:t.parent,dataId:t.id,label:t.label,link:t.link,compClassName:o,key:l(t.label,f),subItems:u||null==(g=t.items)?void 0:g.map((e=>({...e,...r.has(e)&&{"aria-current":"page"}}))),role:x&&A()(t.link)?"button":void 0};return a(e,w)}))};function b(e){const{styles:t,items:n,rtl:r,stretchButtonsToMenuWidth:o,alignButtons:i="center"}=e,l=s(e,{items:n,compClassName:u(t.menuItem,c(v.menuItem)),rtl:r,buttonAlign:i,stretch:o}),d=function(e){const{rtl:t,styles:n,stretchButtonsToMenuWidth:r,alignButtons:o="center",moreButtonLabel:i,onItemMouseEnter:u,onItemMouseLeave:l,onSubMenuKeyDown:s}=e,c=f;let d=t?"left":"right";r||"right"===o||(d="center");const p={label:i||"",isSelected:!1,positionInList:d,id:h(c),index:f,refInParent:c,key:c,onFocus:u,onBlur:l,"aria-haspopup":"true",tagName:"li",onKeyDown:s,isDropDownButton:!1,compClassName:n.moreButton,isMoreButton:!0};return a(e,p)}(e);return d&&l.push(d),l}function m(t){var r;const{alignButtons:o="center",onSubMenuKeyDown:a,hover:i,styles:l}=t,d=function(t){const{items:n,rtl:r,alignButtons:o="center",stretchButtonsToMenuWidth:a,hover:i,styles:u}=t;let l=null,c=[];if(n&&i){const t=parseInt(i,10);Number.isInteger(t)&&n[t]?l=n[t].items:i===f&&(l=function(e){return e.reduce(((e,t)=>{let n=[];return t.items&&(n=t.items.map((e=>({...e,parent:t.id})))),[...e,t,...n]}),[])}(D(n,e.id)))}return l&&(c=s(t,{items:l,compClassName:u.dropdownButton,dropdown:!0,rtl:r,buttonAlign:o,stretch:a})),c}(t),b=h("moreContainer"),m=h("dropWrapper"),y=(null!=(r=null==d?void 0:d.length)?r:0)>0,g=u(l.dropWrapper,{[l.showMore]:y}),x=y,k=function(e){const{hover:t,hoverListPosition:n}=e;return t?n:null}(t);return n.createElement("div",{className:g,id:m,"data-drophposition":k,"data-dropalign":o,[p]:x},n.createElement("ul",{className:u(l.moreContainer,c(v.subMenu)),"data-hover":i,id:b,onKeyDown:a},d))}const h=t=>N(e.id,t),y=j({role:e.role,ariaAttributes:o});return function(e){const{translations:r,styles:o}=e,a=function(e){const{styles:t,skin:r,alignButtons:o="center",marginAllChildren:a,onMenuKeyDown:i}=e,l=b(e),s=h("itemsContainer");let c=n.createElement("ul",{className:u(t.itemsContainer,t[""+o]),id:s,style:{textAlign:o},"data-marginallchildren":a,onKeyDown:i},l);if((()=>{switch(r){case"IndentedMenuButtonSkin":case"ShinyMenuIIButtonSkin":case"SloppyBorderMenuButtonSkin":return!0;default:return!1}})()){const e=h("wrapper");c=n.createElement("div",{className:u(t.itemsContainerWrapper),id:e},c)}return c}(e),i=function(e){const{skin:t,styles:r}=e;let o=null;return d.has(t)&&(o=n.createElement("div",{className:r.utility})),o}(e),l=m(e),s=h("navContainer");return n.createElement("nav",t()({className:u(o.navContainer),id:s,"aria-label":r.ariaLabel},y.ariaAttributes,{role:y.role,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave}),i,a,l,(c=r.subMenuIndication,n.createElement("div",{style:{display:"none"},id:h("navContainer")+"-hiddenA11ySubMenuIndication"},c)));var c}(e)},T={hover:null,hoverListPosition:null};var _=e=>{const[r,o]=n.useState(T),a=n.useRef();let i;const s=t=>{var n;const{hover:o}=r,{id:a,items:i}=e,u=t.getAttribute("data-index")||"-1",l=parseInt(u,10);if((null==t||null==(n=t.parentNode)?void 0:n.id)!==a+"moreContainer")return i[l];if(!o)return null;if(o===f){return D(i,a)[l]}return i[parseInt(o,10)].items[l]},d=t=>{const{onItemMouseIn:n}=e,{currentTarget:r}=t;null==n||n(t,s(r)),p(t)},p=t=>{var n;const{hover:a}=r,{id:u}=e,{currentTarget:l}=t,s=l.getAttribute("data-listposition"),c=l.getAttribute("data-index")||"-1",d=parseInt(c,10);clearTimeout(i);(null==l||null==(n=l.parentNode)?void 0:n.id)!==u+"moreContainer"&&(Number.isInteger(d)&&-1!==d||c.startsWith("__"))&&c!==a&&o({hover:c,hoverListPosition:s})},b=t=>{const{onItemMouseOut:n}=e,{currentTarget:r}=t;null==n||n(t,s(r)),m(t)},m=e=>{e.nativeEvent instanceof MouseEvent?i=setTimeout((()=>{o({hover:null,hoverListPosition:null})}),1e3):o({hover:null,hoverListPosition:null})},h=t=>{const{onItemDblClick:n}=e,{currentTarget:r}=t;null==n||n(t,s(r))},y=t=>{const{hover:n}=r,{currentTarget:o}=t,{items:a,onItemClick:i,isTouchDevice:u}=e;if(null==i||i(t,s(o)),u){var l;const e=o.getAttribute("data-index")||"-1",r="true"===o.getAttribute("data-dropdown"),i=parseInt(e,10),u=a?a[i]:null,s=e===f||(null==u||null==(l=u.items)?void 0:l.length)>0;r?m(t):n?(m(t),s&&n!==e&&(t.preventDefault(),t.stopPropagation(),p(t))):s&&(p(t),t.preventDefault(),t.stopPropagation())}},g=function(t,n){if(void 0===n&&(n=!1),a.current){const{id:o}=e;let i=a.current.querySelector("#"+o+"itemsContainer > li:nth-child("+(t+1)+")");for(;i&&"true"===i.getAttribute("aria-hidden");)i=n?i.previousSibling:i.nextSibling;if(i){var r;const e=i.querySelector("button")||(null==(r=i.childNodes)?void 0:r[0]);if(e)return e.focus(),!0}}return!1},x=t=>{const{hover:n}=r,{items:o}=e,{key:i,shiftKey:u}=t;if(null!==n){const r=n?parseInt(n,10):-1;let l=!1;if("Tab"===i&&!u&&o){const t=o[r];t&&t.items&&(l=(t=>{const{id:n}=e;if(a.current){const e=a.current.querySelector("#"+n+"moreContainer li:nth-child("+(t+1)+") a");if(e)return e.focus(),!0}return!1})(0))}l&&(t.stopPropagation(),t.preventDefault())}},k=t=>{const{hover:n}=r,{items:o}=e,{shiftKey:a,key:i,target:u,currentTarget:l}=t;let s=u;if(u!==l&&"li"!==u.tagName.toLowerCase()&&(s=u.closest("li")),s){const e=s.getAttribute("data-index")||"";let r=!1;if(n){const u=((e,t)=>{const n=parseInt(e,10);return Number.isNaN(n)?t:n})(n,-1);if("Escape"===i&&(r=g(u,a)),"Tab"===i){const n=parseInt(e,10);if(u>=0)if(a)0===n&&(r=g(u,a),b(t));else if(o&&o[u]){const e=o[u];e&&e.items&&e.items.length===n+1&&(r=g(u+1),b(t))}}}r&&(t.stopPropagation(),t.preventDefault())}};function w(e,t){const{hover:n,hoverListPosition:r}=t,{stretchButtonsToMenuWidth:o,sameWidthButtons:a,skinExports:i,alignButtons:u="center",items:s,isQaMode:c,fullNameCompType:d}=e;return{"data-stretch-buttons-to-menu-width":o,"data-same-width-buttons":a,"data-num-items":null==s?void 0:s.length,"data-menuborder-y":i.menuBorderY,"data-menubtn-border":i.menuBtnBorder,"data-ribbon-els":i.ribbonEls,"data-label-pad":i.labelPad,"data-ribbon-extra":i.ribbonExtra,"data-drophposition":r,"data-dropalign":u,"data-hovered-item":n,...l(c,d)}}return function(r,o){const{id:i,className:l,customClassNames:s=[],skin:p,rtl:f,styles:m,lang:g}=r,I={id:i,class:u(m[p],m.wrapper,l,c(v.root,...s),"hidden-during-prewarmup"),ref:a,tabIndex:-1,dir:f?"rtl":"ltr",lang:g,...w(r,o)};return n.createElement("wix-dropdown-menu",I,n.createElement(L,t()({},r,o,{onItemMouseEnter:e.isTouchDevice?void 0:d,onItemMouseLeave:e.isTouchDevice?void 0:b,onItemDoubleClick:e.onItemDblClick?h:void 0,onItemClick:e.isTouchDevice||e.onItemClick?y:void 0,onMenuKeyDown:x,onSubMenuKeyDown:k})))}(e,r)};const F=e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{});const K={root:"linkElement"},W=(e,r)=>{const{href:o,role:a,target:i,rel:u,className:l="",children:s,linkPopupId:c,anchorDataId:d,anchorCompId:p,tabIndex:f,dataTestId:b=K.root,title:m,onClick:v,onDoubleClick:h,onMouseEnter:y,onMouseLeave:g,onFocus:I,onFocusCapture:M,onBlurCapture:j,"aria-live":C,"aria-disabled":S,"aria-label":E,"aria-labelledby":B,"aria-pressed":N,"aria-expanded":D,"aria-describedby":P,"aria-haspopup":A,"aria-current":O,dataPreview:L,dataPart:T}=e,_=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(c);let W;switch(_){case"Enter":W=k;break;case"Space":W=x;break;case"SpaceOrEnter":W=w;break;default:W=void 0}return void 0!==o||c?n.createElement("a",t()({},F(e),{"data-testid":b,"data-popupid":c,"data-anchor":d,"data-anchor-comp-id":p,"data-preview":L,"data-part":T,href:o||void 0,target:i,role:c?"button":a,rel:u,className:l,onKeyDown:W,"aria-live":C,"aria-disabled":S,"aria-label":E,"aria-labelledby":B,"aria-pressed":N,"aria-expanded":D,"aria-haspopup":A,"aria-describedby":P,"aria-current":O,title:m,onClick:v,onMouseEnter:y,onMouseLeave:g,onDoubleClick:h,onFocus:I,onFocusCapture:M,onBlurCapture:j,ref:r,tabIndex:c?0:f}),s):n.createElement("div",t()({},F(e),{"data-testid":b,"data-preview":L,"data-part":T,className:l,tabIndex:f,"aria-label":E,"aria-labelledby":B,"aria-haspopup":A,"aria-disabled":S,"aria-expanded":D,title:m,role:a,onClick:v,onDoubleClick:h,onMouseEnter:y,onMouseLeave:g,ref:r}),s)};var R=n.forwardRef(W);var U=e=>{let{wrapperProps:{ariaHasPopup:n,isMoreButton:o,ariaDescribedBy:a,ariaExpanded:i,ariaCurrent:u,role:l},className:s,children:c,link:d,tabIndex:p}=e;return r().createElement(R,t()({},d,{"aria-haspopup":n,"aria-describedby":a,"aria-current":u,"aria-expanded":i,tabIndex:p||(!o&&d&&d.href?void 0:0),className:s,role:l}),c)};const $=e=>{let{dir:t,textAlign:n,className:o,children:a,tagName:i="p",id:u}=e;return r().createElement(i,{className:o,style:{textAlign:n},dir:t,id:u+"label"},a)};var q=e=>{let{wrapperProps:{dir:t,textAlign:n,id:o},classNames:a,children:i}=e;return r().createElement("div",{className:u(a.bg),style:{textAlign:n}},r().createElement($,{dir:t,textAlign:n,className:a.label,id:o},i))},H=()=>r().createElement("svg",{width:"10",height:"10",viewBox:"0 0 16 11",fill:"black",xmlns:"http://www.w3.org/2000/svg"},r().createElement("path",{d:"M8 10.5L16 1.86193L14.7387 0.5L8 7.77613L1.26133 0.499999L-5.95321e-08 1.86193L8 10.5Z"})),V="_pfxlW",Y="RG3k61";var z=e=>{var r;const{label:o,direction:a="ltr",positionInList:i,parentId:l,dataId:s,isContainer:c,isSelected:d,isHovered:p,link:f,tagName:b="div",id:m,className:v,compClassName:h,onClick:y,onDoubleClick:g,onMouseEnter:x,onMouseLeave:k,index:w,children:I,isDropDownButton:M,subItems:j}=e,[C,S]=n.useState(!1),E=j&&j.length>0,B=e=>e.nativeEvent instanceof MouseEvent,N=e=>{C||(S(!0),null==x||x(e))},D=e=>{C&&(S(!1),null==k||k(e))},P=[c?"drop":"menu",d&&"selected",p&&"over",f&&(f.hasOwnProperty("href")||f.hasOwnProperty("target")||f.hasOwnProperty("rel")||f.hasOwnProperty("linkPopupId"))?"link":"header"],A={...F(e),"data-direction":a,"data-listposition":i,"data-parent-id":l,"data-data-id":s,"data-state":P.join(" "),"data-index":w,"data-dropdown":M},O=e=>e?e.trim():"\xa0",L=E?n.createElement("ul",{"aria-hidden":!0,style:{display:"none"}},j.map(((e,t)=>{const{hasPopup:r,"aria-current":o}=e;return n.createElement("li",{key:e.id||t},n.createElement(U,{wrapperProps:{ariaHasPopup:r,ariaCurrent:o},link:e.link,tabIndex:-1,compClassName:h},O(e.label)))}))):null;return n.createElement(b,t()({id:m},A,{className:u(h,v),onClick:y,onDoubleClick:g,onMouseEnter:N,onMouseLeave:D,onFocus:e=>{B(e)&&N(e)},onBlur:e=>{B(e)?D(e):S(!1)},onKeyDown:e=>{"Escape"===e.key&&(C&&S(!1),null==k||k(e))}}),I(O(o)),E&&n.createElement("button",{className:u(V,{[Y]:C}),onKeyDown:e=>{if("Enter"===e.key||" "===e.key){var t,n;if(!C)S(!0),null==x||x({...e,currentTarget:null==(t=e.currentTarget)?void 0:t.parentNode});if(C)S(!1),null==k||k({...e,currentTarget:null==(n=e.currentTarget)?void 0:n.parentNode})}},"aria-label":(null==e||null==(r=e.translations)||null==(r=r.dropdownButtonAriaLabel)?void 0:r.replace("<%= itemName %>",O(o)))||"More pages"},n.createElement(H,null)),L)};var Q=e=>{const{id:r,"aria-haspopup":o,"aria-describedby":a,"aria-current":i,"aria-expanded":l,isMoreButton:s,dir:c,textAlign:d,positionInList:p,link:f,skinsStyle:b,skin:m,role:v}=e;return n.createElement(z,t()({},e,{className:u(e.className,b[m])}),(e=>n.createElement(U,{wrapperProps:{positionInList:p,ariaHasPopup:o,ariaDescribedBy:a,isMoreButton:s,ariaExpanded:l,ariaCurrent:i,role:v},link:f,className:b.linkElement},n.createElement("div",{className:b.wrapper},n.createElement(q,{wrapperProps:{dir:c,textAlign:d,id:r},classNames:{bg:b.bg,label:b.label}},e)))))},Z={root:"I2NxvL",SolidColorMenuButtonNSkin:"ULfND1",solidColorMenuButtonNSkin:"ULfND1",linkElement:"piclkP",wrapper:"ktxcLB",label:"JghqhY"};var G=e=>n.createElement(Q,t()({},e,{skinsStyle:Z,skin:"SolidColorMenuButtonNSkin"})),X={wrapper:"eK3b7p",navContainer:"dX73bf",itemsContainerWrapper:"ShB2o6",itemsContainer:"LtxuwL",menuItem:"SUHLli",moreButton:"xu0rO4",dropdownButton:"bNFXK4",dropWrapper:"QQFha4",moreContainer:"vZwEg5",showMore:"_0uaYC",utility:"V4qocw",SolidColorMenuButtonSkin:"HYblus",solidColorMenuButtonSkin:"HYblus"};const J=(e,r)=>n.createElement(_,t()({},e,{ref:r,styles:X,Button:G}));const ee=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`));var te;const ne={DropDownMenu_SolidColorMenuButtonSkin:{component:n.forwardRef(J),controller:(te=e=>{let{stateValues:t,mapperProps:n}=e;const{currentUrl:r}=t;return{...n,currentUrl:r}},{useComponentProps:(e,t,n)=>{const r=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(r=t,r.startsWith("--")?t:ee(t))]:void 0===n?null:n};var r}),{});e.updateStyles(n)}}))(n);return te({mapperProps:e,stateValues:t,controllerUtils:r})}})}}}(),a}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[DropDownMenu_SolidColorMenuButtonSkin].fe7819bf.bundle.min.js.map