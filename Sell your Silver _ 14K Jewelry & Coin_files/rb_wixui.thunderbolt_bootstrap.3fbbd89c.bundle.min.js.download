!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt_bootstrap",["react","reactDOM"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt_bootstrap"]=t(require("react"),require("react-dom")):e["rb_wixui.thunderbolt_bootstrap"]=t(e.React,e.ReactDOM)}("undefined"!=typeof self?self:this,(function(e,t){return function(){var n={27232:function(e,t,n){var a=n(82016);e.exports=a.create("StylableButton2545352419",{classes:{root:"StylableButton2545352419__root",label:"StylableButton2545352419__label",link:"StylableButton2545352419__link",container:"StylableButton2545352419__container",icon:"StylableButton2545352419__icon"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},82016:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createRenderable=t.create=void 0;t.create=function(e,t,n,a,r,i){const o={namespace:e,classes:t.classes,keyframes:t.keyframes,layers:t.layers,vars:t.vars,stVars:t.stVars,cssStates:function(e){const t=[];for(const n in e){const a=s(n,e[n]);a&&t.push(a)}return t.join(" ")},style:c,st:c,$id:r,$depth:a,$css:n};function s(t,n){if(!1===n||null==n||n!=n)return"";if(!0===n)return function(t){return`${e}--${t}`}(t);return function(t,n){return`${e}---${t}-${n.length}-${n.replace(/\s/gm,"_")}`}(t,n.toString())}function c(){const e=[];for(let t=0;t<arguments.length;t++){const n=arguments[t];if(n)if("string"==typeof n)e[e.length]=n;else if(1===t)for(const t in n){const a=s(t,n[t]);a&&(e[e.length]=a)}}return e.join(" ")}return i&&i.register(o),o},t.createRenderable=function(e,t,n){return{$css:e,$depth:t,$id:n,$theme:!0}}},96114:function(e,t,n){var a;!function(t){"use strict";var r=function(){},i=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.msRequestAnimationFrame||function(e){return setTimeout(e,16)};function o(){var e=this;e.reads=[],e.writes=[],e.raf=i.bind(t),r("initialized",e)}function s(e){e.scheduled||(e.scheduled=!0,e.raf(c.bind(null,e)),r("flush scheduled"))}function c(e){r("flush");var t,n=e.writes,a=e.reads;try{r("flushing reads",a.length),e.runTasks(a),r("flushing writes",n.length),e.runTasks(n)}catch(e){t=e}if(e.scheduled=!1,(a.length||n.length)&&s(e),t){if(r("task errored",t.message),!e.catch)throw t;e.catch(t)}}function l(e,t){var n=e.indexOf(t);return!!~n&&!!e.splice(n,1)}o.prototype={constructor:o,runTasks:function(e){var t;for(r("run tasks");t=e.shift();)t()},measure:function(e,t){r("measure");var n=t?e.bind(t):e;return this.reads.push(n),s(this),n},mutate:function(e,t){r("mutate");var n=t?e.bind(t):e;return this.writes.push(n),s(this),n},clear:function(e){return r("clear",e),l(this.reads,e)||l(this.writes,e)},extend:function(e){if(r("extend",e),"object"!=typeof e)throw new Error("expected object");var t=Object.create(this);return function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])}(t,e),t.fastdom=this,t.initialize&&t.initialize(),t},catch:null};var u=t.fastdom=t.fastdom||new o;void 0===(a=function(){return u}.call(u,n,u,e))||(e.exports=a)}("undefined"!=typeof window?window:void 0!==this?this:globalThis)},5329:function(t){"use strict";t.exports=e},95561:function(e){"use strict";e.exports=t},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function r(e){var t=a[e];if(void 0!==t)return t.exports;var i=a[e]={id:e,exports:{}};return n[e].call(i.exports,i,i.exports,r),i.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return function(){"use strict";r.r(i),r.d(i,{components:function(){return Xa}});var e={};r.r(e),r.d(e,{STATIC_MEDIA_URL:function(){return ja},ph:function(){return Ha}});var t=r(448),n=r.n(t),a=r(5329),o=r.n(a);function s(e){var t,n,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=s(e[t]))&&(a&&(a+=" "),a+=n);else for(t in e)e[t]&&(a&&(a+=" "),a+=t);return a}var c=function(){for(var e,t,n=0,a="";n<arguments.length;)(e=arguments[n++])&&(t=s(e))&&(a&&(a+=" "),a+=t);return a};const l=()=>"undefined"!=typeof window,u=e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{});const d=13,h=27;function p(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const m=p(32),f=p(d),g=e=>{f(e),m(e)},E=(p(h),["aria-id","aria-metadata","aria-type"]),T=(e,t)=>Object.entries(e).reduce(((e,[n,a])=>(t.includes(n)||(e[n]=a),e)),{}),b=e=>{const t=(e=>{const{role:t,tabIndex:n,tabindex:a,screenReader:r,lang:i,ariaAttributes:o={}}=e,s=Object.entries(o).reduce(((e,[t,n])=>({...e,[`aria-${t}`.toLowerCase()]:n})),{});return{role:t,tabIndex:n??a,screenReader:r,ariaAttributes:T(s,E),lang:i}})(e);return{...t.ariaAttributes,tabIndex:t.tabIndex,screenReader:t.screenReader,lang:t.lang,role:t.role}},v="mesh-container-content",_="inline-content",I=e=>o().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),L=(e,t)=>{const{id:a,className:r,wedges:i=[],rotatedComponents:s=[],children:l,fixedComponents:d=[],extraClassName:h="",renderRotatedComponents:p=I}=e,m=o().Children.toArray(l()),f=[],g=[];m.forEach((e=>d.includes(e.props.id)?f.push(e):g.push(e)));const E=(e=>{const{wedges:t,rotatedComponents:n,childrenArray:a,renderRotatedComponents:r}=e,i=n.reduce(((e,t)=>({...e,[t]:!0})),{});return[...a.map((e=>{return i[(t=e,t.props.id.split("__")[0])]?r(e):e;var t})),...t.map((e=>o().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:g,rotatedComponents:s,wedges:i,renderRotatedComponents:p});return o().createElement("div",n()({},u(e),{"data-mesh-id":a+"inlineContent","data-testid":_,className:c(r,h),ref:t}),o().createElement("div",{"data-mesh-id":a+"inlineContent-gridContainer","data-testid":v},E),f)};var C=o().forwardRef(L),x="J6KGih";const y=(e,t)=>{const{id:r,className:i,meshProps:o,renderSlot:s,children:l,onClick:d,onKeyPress:h,onDblClick:p,onFocus:m,onBlur:f,onMouseEnter:g,onMouseLeave:b,translations:v,hasPlatformClickHandler:_,a11y:I={},ariaAttributes:L={},tabIndex:y,role:k,style:w,lang:A}=e,N=a.useRef(null),{"aria-label-interactions":O,...M}=I;O&&(M["aria-label"]=(null==v?void 0:v.ariaLabel)||"Interactive element, focus to trigger content change");const R={id:r,children:l,...o},S=c(i,{[x]:_});return a.useImperativeHandle(t,(()=>({focus:()=>{var e;null==(e=N.current)||e.focus()},blur:()=>{var e;null==(e=N.current)||e.blur()}}))),a.createElement("div",n()({id:r},u(e),{ref:N,className:S,onClick:d,onKeyDown:e=>{h&&(" "===e.key&&e.preventDefault(),h(e))},onFocus:m,onBlur:f,onDoubleClick:p,onMouseEnter:g,onMouseLeave:b,style:w,lang:A},M,(({role:e,tabIndex:t,tabindex:n,...a}={})=>{const r=Object.entries(a).reduce(((e,[t,n])=>({...e,[`aria-${t}`.toLowerCase()]:n})),{role:e,tabIndex:t??n});return Object.keys(r).forEach((e=>{void 0!==r[e]&&null!==r[e]||delete r[e]})),T(r,E)})({...L,tabIndex:y,role:k})),s({containerChildren:a.createElement(C,R)}))},k=a.forwardRef(y),w=(e,t)=>{e.meshProps||console.warn("Container_NoSkin.skin: Warning! meshProps are missing for component id: "+e.id);const r=e.meshProps||{wedges:e.wedges||[],rotatedComponents:e.rotatedComponents||[]};return a.createElement(k,n()({meshProps:r},e,{ref:t,renderSlot:e=>{let{containerChildren:t}=e;return t}}))};var A=a.forwardRef(w);function N(e,t){if(null==e)return{};var n={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;n[a]=e[a]}return n}function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},O.apply(null,arguments)}function M(e,t){return M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},M(e,t)}function R(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,M(e,t)}var S=o().createContext(null);function P(e,t){var n=Object.create(null);return e&&a.Children.map(e,(function(e){return e})).forEach((function(e){n[e.key]=function(e){return t&&(0,a.isValidElement)(e)?t(e):e}(e)})),n}function F(e,t,n){return null!=n[t]?n[t]:e.props[t]}function G(e,t,n){var r=P(e.children),i=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var a,r=Object.create(null),i=[];for(var o in e)o in t?i.length&&(r[o]=i,i=[]):i.push(o);var s={};for(var c in t){if(r[c])for(a=0;a<r[c].length;a++){var l=r[c][a];s[r[c][a]]=n(l)}s[c]=n(c)}for(a=0;a<i.length;a++)s[i[a]]=n(i[a]);return s}(t,r);return Object.keys(i).forEach((function(o){var s=i[o];if((0,a.isValidElement)(s)){var c=o in t,l=o in r,u=t[o],d=(0,a.isValidElement)(u)&&!u.props.in;!l||c&&!d?l||!c||d?l&&c&&(0,a.isValidElement)(u)&&(i[o]=(0,a.cloneElement)(s,{onExited:n.bind(null,s),in:u.props.in,exit:F(s,"exit",e),enter:F(s,"enter",e)})):i[o]=(0,a.cloneElement)(s,{in:!1}):i[o]=(0,a.cloneElement)(s,{onExited:n.bind(null,s),in:!0,exit:F(s,"exit",e),enter:F(s,"enter",e)})}})),i}var B=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},H=function(e){function t(t,n){var a,r=(a=e.call(this,t,n)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(a));return a.state={contextValue:{isMounting:!0},handleExited:r,firstRender:!0},a}R(t,e);var n=t.prototype;return n.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},n.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(e,t){var n,r,i=t.children,o=t.handleExited;return{children:t.firstRender?(n=e,r=o,P(n.children,(function(e){return(0,a.cloneElement)(e,{onExited:r.bind(null,e),in:!0,appear:F(e,"appear",n),enter:F(e,"enter",n),exit:F(e,"exit",n)})}))):G(e,i,o),firstRender:!1}},n.handleExited=function(e,t){var n=P(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var n=O({},t.children);return delete n[e.key],{children:n}})))},n.render=function(){var e=this.props,t=e.component,n=e.childFactory,a=N(e,["component","childFactory"]),r=this.state.contextValue,i=B(this.state.children).map(n);return delete a.appear,delete a.enter,delete a.exit,null===t?o().createElement(S.Provider,{value:r},i):o().createElement(S.Provider,{value:r},o().createElement(t,a,i))},t}(o().Component);H.propTypes={},H.defaultProps={component:"div",childFactory:function(e){return e}};var D=H;function $(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}var j=r(95561),U=r.n(j),Y=!1,V=function(e){return e.scrollTop},z="unmounted",W="exited",Z="entering",q="entered",J="exiting",K=function(e){function t(t,n){var a;a=e.call(this,t,n)||this;var r,i=n&&!n.isMounting?t.enter:t.appear;return a.appearStatus=null,t.in?i?(r=W,a.appearStatus=Z):r=q:r=t.unmountOnExit||t.mountOnEnter?z:W,a.state={status:r},a.nextCallback=null,a}R(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===z?{status:W}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==Z&&n!==q&&(t=Z):n!==Z&&n!==q||(t=J)}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e,t,n,a=this.props.timeout;return e=t=n=a,null!=a&&"number"!=typeof a&&(e=a.exit,t=a.enter,n=void 0!==a.appear?a.appear:t),{exit:e,enter:t,appear:n}},n.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===Z){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:U().findDOMNode(this);n&&V(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===W&&this.setState({status:z})},n.performEnter=function(e){var t=this,n=this.props.enter,a=this.context?this.context.isMounting:e,r=this.props.nodeRef?[a]:[U().findDOMNode(this),a],i=r[0],o=r[1],s=this.getTimeouts(),c=a?s.appear:s.enter;!e&&!n||Y?this.safeSetState({status:q},(function(){t.props.onEntered(i)})):(this.props.onEnter(i,o),this.safeSetState({status:Z},(function(){t.props.onEntering(i,o),t.onTransitionEnd(c,(function(){t.safeSetState({status:q},(function(){t.props.onEntered(i,o)}))}))})))},n.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),a=this.props.nodeRef?void 0:U().findDOMNode(this);t&&!Y?(this.props.onExit(a),this.safeSetState({status:J},(function(){e.props.onExiting(a),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:W},(function(){e.props.onExited(a)}))}))}))):this.safeSetState({status:W},(function(){e.props.onExited(a)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(a){n&&(n=!1,t.nextCallback=null,e(a))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:U().findDOMNode(this),a=null==e&&!this.props.addEndListener;if(n&&!a){if(this.props.addEndListener){var r=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],i=r[0],o=r[1];this.props.addEndListener(i,o)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===z)return null;var t=this.props,n=t.children,a=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,N(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return o().createElement(S.Provider,{value:null},"function"==typeof n?n(e,a):o().cloneElement(o().Children.only(n),a))},t}(o().Component);function Q(){}K.contextType=S,K.propTypes={},K.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Q,onEntering:Q,onEntered:Q,onExit:Q,onExiting:Q,onExited:Q},K.UNMOUNTED=z,K.EXITED=W,K.ENTERING=Z,K.ENTERED=q,K.EXITING=J;var X=K,ee=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return a=t,void((n=e).classList?n.classList.remove(a):"string"==typeof n.className?n.className=$(n.className,a):n.setAttribute("class",$(n.className&&n.className.baseVal||"",a)));var n,a}))},te=function(e){function t(){for(var t,n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];return(t=e.call.apply(e,[this].concat(a))||this).appliedClasses={appear:{},enter:{},exit:{}},t.onEnter=function(e,n){var a=t.resolveArguments(e,n),r=a[0],i=a[1];t.removeClasses(r,"exit"),t.addClass(r,i?"appear":"enter","base"),t.props.onEnter&&t.props.onEnter(e,n)},t.onEntering=function(e,n){var a=t.resolveArguments(e,n),r=a[0],i=a[1]?"appear":"enter";t.addClass(r,i,"active"),t.props.onEntering&&t.props.onEntering(e,n)},t.onEntered=function(e,n){var a=t.resolveArguments(e,n),r=a[0],i=a[1]?"appear":"enter";t.removeClasses(r,i),t.addClass(r,i,"done"),t.props.onEntered&&t.props.onEntered(e,n)},t.onExit=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"appear"),t.removeClasses(n,"enter"),t.addClass(n,"exit","base"),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var n=t.resolveArguments(e)[0];t.addClass(n,"exit","active"),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"exit"),t.addClass(n,"exit","done"),t.props.onExited&&t.props.onExited(e)},t.resolveArguments=function(e,n){return t.props.nodeRef?[t.props.nodeRef.current,e]:[e,n]},t.getClassNames=function(e){var n=t.props.classNames,a="string"==typeof n,r=a?""+(a&&n?n+"-":"")+e:n[e];return{baseClassName:r,activeClassName:a?r+"-active":n[e+"Active"],doneClassName:a?r+"-done":n[e+"Done"]}},t}R(t,e);var n=t.prototype;return n.addClass=function(e,t,n){var a=this.getClassNames(t)[n+"ClassName"],r=this.getClassNames("enter").doneClassName;"appear"===t&&"done"===n&&r&&(a+=" "+r),"active"===n&&e&&V(e),a&&(this.appliedClasses[t][n]=a,function(e,t){e&&t&&t.split(" ").forEach((function(t){return a=t,void((n=e).classList?n.classList.add(a):function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}(n,a)||("string"==typeof n.className?n.className=n.className+" "+a:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+a)));var n,a}))}(e,a))},n.removeClasses=function(e,t){var n=this.appliedClasses[t],a=n.base,r=n.active,i=n.done;this.appliedClasses[t]={},a&&ee(e,a),r&&ee(e,r),i&&ee(e,i)},n.render=function(){var e=this.props,t=(e.classNames,N(e,["classNames"]));return o().createElement(X,O({},t,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t}(o().Component);te.defaultProps={classNames:""},te.propTypes={};var ne=te,ae="sNF2R0",re="hLoBV3",ie="Rdf41z",oe="ftlZWo";var se="ATGlOr",ce="KQSXD0",le="pagQKE",ue="_6zG5H";var de="BB49uC",he="j9xE1V",pe="ICs7Rs",me="DxijZJ",fe="B5kjYq",ge="cJijIV",Ee="hOxaWM",Te="T9p3fN";var be="qDxYJm",ve="aA9V0P",_e="YPXPAS",Ie="Xf2zsA",Le="y7Kt7s",Ce="EeUgMu",xe="fdHrtm",ye="WIFaG4";const ke={CrossFade:e=>{const{reverse:t,...r}=e;return a.createElement(ne,n()({},r,{classNames:{enter:ae,enterActive:re,exit:ie,exitActive:oe}}),e.children)},OutIn:e=>{const{reverse:t,...r}=e;return a.createElement(ne,n()({},r,{classNames:{enter:se,enterActive:ce,exit:le,exitActive:ue}}),e.children)},SlideHorizontal:e=>{const{reverse:t,...r}=e;return a.createElement(ne,n()({},r,{classNames:t?{enter:fe,enterActive:ge,exit:Ee,exitActive:Te}:{enter:de,enterActive:he,exit:pe,exitActive:me}}),e.children)},SlideVertical:e=>{const{reverse:t,...r}=e;return a.createElement(ne,n()({},r,{classNames:t?{enter:Le,enterActive:Ce,exit:xe,exitActive:ye}:{enter:be,enterActive:ve,exit:_e,exitActive:Ie}}),e.children)}};var we=e=>{const t=ke[e.type],{type:n,...r}=e;return a.createElement(t,r)};const Ae="TRANSITION_GROUP";var Ne=e=>{const{id:t=Ae,transition:r="none",transitionDuration:i=0,transitionEnabled:o=!0,onTransitionComplete:s=()=>{},onTransitionStarting:c=()=>{},className:l,children:d,shouldUseViewTransition:h}=e,p=a.Children.toArray(d())[0],m=null==p?void 0:p.props.id,f="none"===r,g="SlideVertical"===r,E="undefined"!=typeof window&&"startViewTransition"in document&&h;return a.createElement(a.Fragment,null,f?d():E?a.createElement("div",n()({id:t},u(e),{className:l}),d()):a.createElement(D,n()({id:t},u(e),{className:l,childFactory:e=>a.cloneElement(e,{reverse:g})}),a.createElement(we,{type:r,key:m,timeout:i,onEntered:s,onExiting:c,enter:o,exit:o,unmountOnExit:!0},(()=>p))))};const Oe=o().memo(Ne,((e,t)=>!t.children().length||!1===t.transitionEnabled));var Me=e=>{const{id:t,children:a,className:r,...i}=e;return o().createElement("div",n()({id:t,className:r},u(e)),o().createElement(Oe,n()({id:t+"_"+Ae},i),a))},Re="JsJXaX",Se="AnQkDU";const Pe=a.memo(Ne,((e,t)=>!t.children().length));var Fe=e=>{const{id:t,children:r,className:i,...o}=e;return a.createElement("div",n()({id:t},u(e),{className:c(Re,i)}),a.createElement(Pe,n()({id:t+"_"+Ae,className:Se},o),r))};const Ge=({reportBiOnClick:e,onClick:t})=>(0,a.useCallback)((n=>{e?.(n),t?.(n)}),[e,t]),Be="wixui-",He=(e,...t)=>{const n=[];return e&&n.push(`${Be}${e}`),t.forEach((e=>{e&&(n.push(`${Be}${e}`),n.push(e))})),n.join(" ")},De=(e,t)=>e?{"data-comp":t,"data-aid":t}:{},$e={root:"linkElement"};var je=a.forwardRef(((e,t)=>{const{href:n,role:r,target:i,rel:o,className:s="",children:c,linkPopupId:l,anchorDataId:d,anchorCompId:h,tabIndex:p,dataTestId:E=$e.root,title:T,onClick:b,onDoubleClick:v,onMouseEnter:_,onMouseLeave:I,onFocus:L,onFocusCapture:C,onBlurCapture:x,"aria-live":y,"aria-disabled":k,"aria-label":w,"aria-labelledby":A,"aria-pressed":N,"aria-expanded":O,"aria-describedby":M,"aria-haspopup":R,"aria-current":S,dataPreview:P,dataPart:F}=e,G=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(l);let B;switch(G){case"Enter":B=f;break;case"Space":B=m;break;case"SpaceOrEnter":B=g;break;default:B=void 0}return void 0!==n||l?a.createElement("a",{...u(e),"data-testid":E,"data-popupid":l,"data-anchor":d,"data-anchor-comp-id":h,"data-preview":P,"data-part":F,href:n||void 0,target:i,role:l?"button":r,rel:o,className:s,onKeyDown:B,"aria-live":y,"aria-disabled":k,"aria-label":w,"aria-labelledby":A,"aria-pressed":N,"aria-expanded":O,"aria-haspopup":R,"aria-describedby":M,"aria-current":S,title:T,onClick:b,onMouseEnter:_,onMouseLeave:I,onDoubleClick:v,onFocus:L,onFocusCapture:C,onBlurCapture:x,ref:t,tabIndex:l?0:p},c):a.createElement("div",{...u(e),"data-testid":E,"data-preview":P,"data-part":F,className:s,tabIndex:p,"aria-label":w,"aria-labelledby":A,"aria-haspopup":R,"aria-disabled":k,"aria-expanded":O,title:T,role:r,onClick:b,onDoubleClick:v,onMouseEnter:_,onMouseLeave:I,ref:t},c)}));const Ue="buttonContent",Ye="stylablebutton-label",Ve="stylablebutton-icon",ze=(Object.keys({width:{type:"maxContent"}}),{left:"flex-start",right:"flex-end",center:"center","space-between":"space-between"}),We={start:"flex-start",end:"flex-end",center:"center",justify:"space-between"},Ze={"flex-start":"left","flex-end":"right",center:"center","space-between":"space-between"},qe={"flex-start":"start","flex-end":"end",center:"center","space-between":"justify"};var Je={root:"button",buttonLabel:"button__label",buttonIcon:"button__icon"},Ke=r(27232);var Qe=e=>{const{label:t,icon:n,override:a,semanticClassNames:r}=e;return o().createElement("span",{className:Ke.classes.container},t&&o().createElement("span",{className:(0,Ke.st)(Ke.classes.label,He(r.buttonLabel)),"data-testid":Ye},t),n&&o().createElement("span",{className:(0,Ke.st)(Ke.classes.icon,{override:!!a},He(r.buttonIcon)),"aria-hidden":"true","data-testid":Ve},n))};const Xe=e=>a.createElement("span",{dangerouslySetInnerHTML:{__html:e||""}}),et=(e,t)=>e?e.replace(/(id="|url\(#|href="#)([^"]+)(?=[")])/g,((e,n,a)=>""+n+(t+a))):e,tt=(e,t)=>["has",t,...e.split("has").slice(1)].join("");function nt(e){let{hover:t={},disabled:n={},...a}=e;return{...a,...Object.fromEntries([...Object.entries(t).map((e=>{let[t,n]=e;return[tt(t,"Hover"),n]})),...Object.entries(n).map((e=>{let[t,n]=e;return[tt(t,"Disabled"),n]}))])}}const at=(e,t)=>{const{id:r,link:i,type:o="button",svgString:s,label:c,isDisabled:l,className:d,stylableButtonClassName:h,customClassNames:p=[],isQaMode:g,fullNameCompType:E,reportBiOnClick:T,a11y:v,corvid:_,isMaxContent:I=!1,isWrapText:L=!1,onDblClick:C,onMouseEnter:x,onMouseLeave:y,onFocus:k,onBlur:w,ariaAttributes:A,onClick:N,preventLinkNavigation:O,lang:M}=e,R=(e=>Boolean(e&&(e.href||e.linkPopupId)))(i),S=O&&R,P=!l&&N||S,F=e.semanticClassNames||Je,G=a.useRef(null);a.useImperativeHandle(t,(()=>({focus:()=>{var e;null==(e=G.current)||e.focus()},blur:()=>{var e;null==(e=G.current)||e.blur()}})));const B=a.useMemo((()=>{var e,t;return b({ariaAttributes:{...A,...v,label:null!=(e=null!=(t=null==A?void 0:A.label)?t:v.label)?e:c},tabindex:null==v?void 0:v.tabindex})}),[v,c,A]),H=Ge({reportBiOnClick:T,onClick:P?e=>{S&&e.preventDefault(),!l&&(null==N||N(e))}:void 0}),D=a.useMemo((()=>((e,t,n)=>{let{onClick:a,onDblClick:r,onMouseEnter:i,onMouseLeave:o,onFocus:s,onBlur:c}=e;return{onClick:a,onMouseEnter:i,onMouseLeave:o,onKeyDown:t?m:f,onDoubleClick:!n&&r?r:void 0,onFocus:!n&&s?s:void 0,onBlur:!n&&c?c:void 0}})({onClick:H,onDblClick:C,onMouseLeave:y,onMouseEnter:x,onFocus:k,onBlur:w},R,l)),[H,C,y,x,k,w,R,l]),{iconSvgString:$,iconCollapsed:j,...U}=_||{},Y=(0,Ke.st)(Ke.classes.root,{error:!1,disabled:l,isMaxContent:I,isWrapText:L,...nt(U)},h,He(F.root,...p));let V=null,z=!1;j||null===$||($?(V=Xe(et($,r)),z=!0):s&&(V=Xe(et(s,r))));const W=t=>a.createElement("div",n()({id:r,className:d},De(g,E),u(e),{"data-semantic-classname":F.root}),a.createElement("button",n()({type:o,disabled:l,className:Y,"data-testid":Ue},B,D,{ref:G,role:t}),a.createElement(Qe,{label:c,icon:V,override:z,semanticClassNames:F})));return l&&R?W("link"):R?(()=>{const{onFocus:t,onBlur:o,...s}=D;return a.createElement("div",n()({id:r,className:d},s,u(e),De(g,E),{"data-semantic-classname":F.root,lang:M}),a.createElement(je,n()({},i,B,{href:l?void 0:i.href,className:(0,Ke.st)(Y,Ke.classes.link),onFocusCapture:t,onBlurCapture:o,ref:G}),a.createElement(Qe,{label:c,icon:V,semanticClassNames:F})))})():W()};var rt=a.forwardRef(at);const it=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),ot=e=>({useComponentProps:(t,n,a)=>{const r=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(a=t,a.startsWith("--")?t:it(t))]:void 0===n?null:n};var a}),{});e.updateStyles(n)}}))(a);return e({mapperProps:t,stateValues:n,controllerUtils:r})}}),st=e=>"linkPopupId"in e,ct=(e,t)=>{if(st(e))return e.linkPopupId;{const{pagesMap:n,mainPageId:a}=t||{};if(!n)return;const r=new URL(e.href??"");let i=Object.values(n).find((({pageUriSEO:e})=>!!e&&r.pathname?.includes(e)));return i||(i=a?n[a]:void 0),i?.pageId}},lt=e=>{if(void 0!==e)return null===e?"None":e.type},ut=(e,t)=>{if(!e?.type)return;const{type:n}=e;switch(n){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const n=ct(e,t);return n&&t?.pagesMap?.[n]?.title;default:return e.href}},dt=(e,t,n)=>{const{link:a,value:r,details:i,actionName:o,elementType:s,trackClicksAnalytics:c,pagesMetadata:l,...u}=t;if(!c)return;const d=l&&{...l,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},h=((e,t)=>{if(!e?.type)return;const{type:n}=e;switch(n){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:ct(e,t),isLightbox:st(e)};default:return}})(a,d),p=i||h?JSON.stringify({...h,...i}):void 0;e({src:76,evid:1113,...{...u,bl:navigator.language,url:window.location.href,details:p,elementType:s??"Unknown",actionName:o??lt(a),value:r??ut(a,d)}},{endpoint:"pa",...n})};var ht;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(ht||(ht={}));var pt=ot((e=>{let{mapperProps:t,stateValues:n}=e;const{trackClicksAnalytics:a,compId:r,language:i,mainPageId:o,...s}=t,{reportBi:c,reducedMotion:l}=n;return{...s,reportBiOnClick:e=>{const{fullNameCompType:t,label:n,link:l,isDisabled:u}=s;dt(c,{link:l,language:i,trackClicksAnalytics:a,elementTitle:null!=n?n:"",elementType:t,pagesMetadata:{mainPageId:o},elementGroup:ht.Button,details:{isDisabled:null!=u&&u},element_id:null!=r?r:e.currentTarget.id})},reducedMotion:l}}));const mt=new RegExp("<%= compId %>","g"),ft=(e,t)=>e.replace(mt,t),gt={root:"linkElement"},Et=(e,t)=>{const{href:r,role:i,target:o,rel:s,className:c="",children:l,linkPopupId:d,anchorDataId:h,anchorCompId:p,tabIndex:E,dataTestId:T=gt.root,title:b,onClick:v,onDoubleClick:_,onMouseEnter:I,onMouseLeave:L,onFocus:C,onFocusCapture:x,onBlurCapture:y,"aria-live":k,"aria-disabled":w,"aria-label":A,"aria-labelledby":N,"aria-pressed":O,"aria-expanded":M,"aria-describedby":R,"aria-haspopup":S,"aria-current":P,dataPreview:F,dataPart:G}=e,B=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(d);let H;switch(B){case"Enter":H=f;break;case"Space":H=m;break;case"SpaceOrEnter":H=g;break;default:H=void 0}return void 0!==r||d?a.createElement("a",n()({},u(e),{"data-testid":T,"data-popupid":d,"data-anchor":h,"data-anchor-comp-id":p,"data-preview":F,"data-part":G,href:r||void 0,target:o,role:d?"button":i,rel:s,className:c,onKeyDown:H,"aria-live":k,"aria-disabled":w,"aria-label":A,"aria-labelledby":N,"aria-pressed":O,"aria-expanded":M,"aria-haspopup":S,"aria-describedby":R,"aria-current":P,title:b,onClick:v,onMouseEnter:I,onMouseLeave:L,onDoubleClick:_,onFocus:C,onFocusCapture:x,onBlurCapture:y,ref:t,tabIndex:d?0:E}),l):a.createElement("div",n()({},u(e),{"data-testid":T,"data-preview":F,"data-part":G,className:c,tabIndex:E,"aria-label":A,"aria-labelledby":N,"aria-haspopup":S,"aria-disabled":w,"aria-expanded":M,title:b,role:i,onClick:v,onDoubleClick:_,onMouseEnter:I,onMouseLeave:L,ref:t}),l)};var Tt=a.forwardRef(Et);var bt={root:"vector-image"},vt={link:"aeyn4z",clickable:"qQrFOK",svgRoot:"VDJedC",nonScalingStroke:"l4CAhn"};var _t=e=>{const{id:t,svgContent:r,shouldScaleStroke:i,withShadow:o,link:s,ariaLabel:l,ariaExpanded:d,ariaAttributes:h,className:p="",customClassNames:m=[],containerClass:f="",onClick:g,onDblClick:E,onMouseEnter:T,onMouseLeave:b,hasPlatformClickHandler:v,onKeyDown:_,toggle:I,reportBiOnClick:L,tag:C="div",isClassNameToRootEnabled:x}=e,y=v||g,k=!(!(w=s)||0===Object.keys(w).length&&w.constructor===Object);var w;const A=c(vt.svgRoot,{[vt.nonScalingStroke]:!i,[vt.hasShadow]:o,[vt.clickable]:y,[p]:!x}),N=a.useRef(null);a.useEffect((()=>{let e;const t=N.current;return I&&(e=()=>I(!1),null==t||t.addEventListener("click",e)),()=>{e&&(null==t||t.removeEventListener("click",e))}}),[N,I]);const O=a.useMemo((()=>{if(!r)return r;const e=((e,t)=>{const n={},a=e.replace(/\sid="([^"<]+)"/g,((e,a)=>{const r=a.endsWith(t)?a:`${a}_${t}`;return n[a]=r,` id="${r}"`}));return Object.keys(n).reduce(((e,a)=>e.replace(new RegExp(`(${a})(?!_${t})`,"g"),n[a])),a)})(r,t),n=null!=h&&h.label?((e,t)=>{let n=e;const a=/aria-label="[^"]*"/;return n=e.match(a)?e.replace(a,`aria-label="${t}"`):e.replace(/(<svg[^>]*)>/,`$1 aria-label="${t}">`),n})(e,null==h?void 0:h.label):e;return ft(n,t)}),[t,r,null==h?void 0:h.label]),M=a.createElement("div",{"data-testid":"svgRoot-"+t,className:A,dangerouslySetInnerHTML:{__html:O}}),R=Ge({onClick:g,reportBiOnClick:L});return a.createElement(C,n()({id:t},u(e),((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(e.a11y),{className:c(f,p,He(bt.root,...m)),onClick:y||k?R:void 0,onDoubleClick:E,onMouseEnter:T,onMouseLeave:b,onKeyDown:_,ref:N,"aria-expanded":d}),k?a.createElement(Tt,n()({className:vt.link,"aria-label":l},s),M):M)};var It=e=>a.createElement(_t,n()({},e,{tag:"div"}));var Lt=ot((e=>{let{stateValues:t,mapperProps:n}=e;const{compId:a,language:r,mainPageId:i,fullNameCompType:o,trackClicksAnalytics:s,...c}=n,{toggle:l,reportBi:u}=t,d=e=>{const{link:t}=c;dt(u,{link:t,language:r,trackClicksAnalytics:s,elementType:o,pagesMetadata:{mainPageId:i},element_id:null!=a?a:e.currentTarget.id,elementGroup:ht.Decorative})};return l?{...c,toggle:l,reportBiOnClick:d,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||l(!1)}}:{...c,reportBiOnClick:d}}));const Ct="richTextElement",xt="containerElement",yt="screenReaderPrefixElement",kt="screenReaderSuffixElement";var wt={root:"rich-text",text:"rich-text__text"},At={wrapper:"Z_l5lU",clickable:"HQSswv",srOnly:"yi6otz",supportTableDesign:"zQ9jDz",safariFix:"qvSjx3",corvidColorOverride:"LkZBpT",WRichTextSkin:"Kbom4H",wRichTextSkin:"Kbom4H",richTextContainer:"upNqi2",WRichTextNewSkin:"MMl86N",wRichTextNewSkin:"MMl86N",WRichTextClickableSkin:"gYHZuN",wRichTextClickableSkin:"gYHZuN",WRichTextThemeSkin:"ku3DBC",wRichTextThemeSkin:"ku3DBC","list-direction-spec":"Vq6kJx",listDirectionSpec:"Vq6kJx"};const Nt=e=>{let{text:t,testId:n}=e;return a.createElement("div",{className:At.srOnly,"data-testid":n},t)};function Ot(e){return void 0===e&&(e={}),c(Boolean(e.hasColor)&&At.corvidColorOverride)}var Mt=a.forwardRef(((e,t)=>{const{id:n,className:r,customClassNames:i=[],html:o,skin:s="WRichTextSkin",a11y:l,lang:d,isQaMode:h,fullNameCompType:p,screenReader:m,ariaAttributes:f,onClick:E,onDblClick:T,shouldFixVerticalTopAlignment:v,isListInRtlEnabled:_,isClassNameToRootEnabled:I,corvid:L}=e,[C,x]=a.useState(!1),{prefix:y,suffix:k}=m||{},w=a.useRef(null);!function(e,t){void 0===t&&(t=[]);const n=(0,a.useRef)([]),r=()=>n.current.forEach((e=>{e.removeEventListener("keydown",g)}));(0,a.useEffect)((()=>{var t;r();const a=Array.from((null==(t=e.current)?void 0:t.querySelectorAll("a[data-popupid]"))||[]);return a.forEach((e=>{e.addEventListener("keydown",g)})),n.current=a,r}),t)}(w,[o]);const A=["WRichTextSkin","WRichTextClickableSkin"].includes(s),N=A||y||k,O={onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onClick:e.onClick,onDoubleClick:e.onDblClick};a.useEffect((()=>{x(Boolean(E)||Boolean(T))}),[E,T]);const M=c(At.wrapper,At[s],At.supportTableDesign,{[At.clickable]:C,[At.safariFix]:v,[At["list-direction-spec"]]:_}),R=N?{id:n,...A?{...u(e),className:c(M,r,He(wt.root,...i),Ot(L)),"data-testid":Ct,...O,...l,...b({ariaAttributes:f,lang:d}),...De(h,p)}:{className:I?r:""}}:void 0,S={...N?void 0:{id:n,...u(e),...De(h,p)},dangerouslySetInnerHTML:{__html:o},ref:e=>{w.current=e,t&&(t.current=e)},...A?{className:c(At.richTextContainer,I?"":r,Ot(L)),"data-testid":xt}:{className:c(M,I&&N?"":r,He(wt.root,...i),Ot(L)),"data-testid":Ct,...O,...l,...b({ariaAttributes:f,lang:d})}},P=y&&a.createElement(Nt,{text:y,testId:yt}),F=k&&a.createElement(Nt,{text:k,testId:kt}),G=a.createElement("div",S);return N?a.createElement("div",R,P,G,F):G}));const Rt=(e,t)=>!0===e[t]||"true"===e[t]||"new"===e[t];var St=ot((e=>{let{mapperProps:t,stateValues:n}=e;const{experiments:a={}}=n;return{...t,shouldFixVerticalTopAlignment:Rt(a,"specs.thunderbolt.WRichTextVerticalAlignTopSafariAndIOS"),isListInRtlEnabled:Rt(a,"specs.thunderbolt.wrichtextListInRtl"),isClassNameToRootEnabled:Rt(a,"specs.thunderbolt.isClassNameToRootEnabled")}})),Pt="Vd6aQZ",Ft="mHZSwn";var Gt=e=>{const{id:t,name:r,urlFragment:i,className:o,isAriaHidden:s}=e;return a.createElement("div",n()({id:t},u(e),{className:Pt+" ignore-focus "+o,"aria-hidden":s},!s&&{role:"region",tabIndex:-1,"aria-label":r}),i&&a.createElement("div",{id:i}),a.createElement("span",{className:Ft},r))},Bt={screenWidthBackground:"lvxhkV",HeaderHideToTop:"QJjwEo",headerHideToTop:"QJjwEo",HeaderHideToTopReverse:"kdBXfh",headerHideToTopReverse:"kdBXfh",HeaderFadeOut:"MP52zt",headerFadeOut:"MP52zt",transitionEnded:"Bhu9m5",HeaderFadeOutReverse:"LVP8Wf",headerFadeOutReverse:"LVP8Wf",inlineContent:"VrZrC0",centeredContent:"cKxVkc",centeredContentBg:"vlM3HR",root:"AT7o0U"};var Ht=e=>{let{id:t,className:a,skinClassName:r,tagName:i="div",transition:s,transitionEnded:l,eventHandlers:u,skinStyles:d,children:h,tabIndex:p,lang:m}=e;const f=i;return o().createElement(f,n()({id:t,className:c(r,s&&d[s],l&&d.transitionEnded,a),tabIndex:p,lang:m},u),h)};var Dt=e=>{let{wrapperProps:t,children:r,skinStyles:i}=e;return a.createElement(Ht,n()({},t,{skinClassName:i.root,skinStyles:i}),a.createElement("div",{className:i.screenWidthBackground}),a.createElement("div",{className:i.centeredContent},a.createElement("div",{className:i.centeredContentBg}),a.createElement("div",{className:i.inlineContent},r)))};var $t=e=>o().createElement(Dt,n()({},e,{skinStyles:Bt}));var jt={root:"footer"};var Ut=e=>{const{id:t,className:r,customClassNames:i=[],skin:o,children:s,meshProps:l,fillLayers:d,lang:h}=e,p={onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onClick:e.onClick,onDoubleClick:e.onDblClick},m=c(r,He(jt.root,...i));return a.createElement(o,{wrapperProps:{...u(e),lang:h,id:t,tagName:"footer",eventHandlers:p,tabIndex:"-1",className:m},fillLayers:d,"data-block-level-container":"FooterContainer"},a.createElement(C,n()({id:t},l),s))};var Yt=e=>o().createElement(Ut,n()({},e,{skin:$t})),Vt=r(96114),zt=r.n(Vt);function Wt(){if(!l())return{x:0,y:0,isAtPageBottom:!1};const{left:e,top:t}=document.body.getBoundingClientRect();return{x:e,y:t,isAtPageBottom:window.innerHeight+window.scrollY===document.body.scrollHeight}}var Zt={root:"header"};const qt="Reverse",Jt="up",Kt="down";var Qt=e=>{const{id:t,skin:r,children:i,animations:s,meshProps:u,className:d,customClassNames:h=[],fillLayers:p,lang:m}=e,[f,g]=(0,a.useState)(""),[E,T]=(0,a.useState)(!1),b=e=>{g(e),T(!1)};(0,a.useEffect)((()=>{window.TransitionEvent||setTimeout((()=>T(!0)),200)}),[f]);const v=f&&!(e=>e.endsWith(qt))(f),_=()=>{const e=(e=>""+e+qt)(f);b(e)},I={onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onClick:e.onClick,onDoubleClick:e.onDblClick,onFocus:v?_:void 0,onTransitionEnd:()=>T(!0)};let L=Kt,x=0;!function(e,t,n){void 0===n&&(n={}),n={waitFor:100,disabled:!1,...n};const r=(0,a.useRef)(Wt());let i=null;const o=()=>{zt().measure((()=>{const t=Wt(),n=r.current;r.current=t,i=null,zt().mutate((()=>e({prevPos:n,currPos:t})))}))};(l()?a.useLayoutEffect:a.useEffect)((()=>{if(!l())return;const e=()=>{null===i&&(i=window.setTimeout(o,n.waitFor))};return n.disabled?()=>{}:(window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e),i&&window.clearTimeout(i)})}),t)}((e=>{var t,n;let{currPos:a,prevPos:r}=e;const i=a.y&&-1*a.y,o=r.y&&-1*r.y,c=s[s.length-1],l=null==(t=c.params)||null==(t=t.animations)?void 0:t[c.params.animations.length-1];if(!l)return;const u="mobile"===(null==(n=c.viewMode)?void 0:n.toLowerCase())?1:(e=>{switch(e){case"HeaderFadeOut":return 200;case"HeaderHideToTop":return 400;default:return null}})(l.name);u&&(((e,t)=>{L===Kt&&e<t?(x=t,L=Jt):L===Jt&&e>t&&e>=0&&t>=0&&(x=t,L=Kt)})(i,o),v?(L===Jt&&i+u<x||0===a.y)&&_():L===Kt&&i-x>=u&&b(l.name))}),[f,s],{disabled:!s||!s.length});const y=c(d,He(Zt.root,...h));return o().createElement(r,{wrapperProps:{id:t,tagName:"header",eventHandlers:I,className:y,transition:f,transitionEnded:E,tabIndex:"-1",lang:m},"data-block-level-container":"HeaderContainer",fillLayers:p},o().createElement(C,n()({id:t},u,{children:i})))};var Xt=e=>o().createElement(Qt,n()({},e,{skin:$t}));var en=ot((e=>{let{mapperProps:t,controllerUtils:n}=e;const{updateStyles:r}=n,{compId:i,marginTop:o,isMobileView:s,isFixed:c,...l}=t;var u;return u=()=>{var e;const t=((null==(e=window.document.getElementById(i))?void 0:e.clientHeight)||0)>=window.document.body.clientHeight/2;s&&c&&t&&r({position:"relative !important",marginTop:o,top:0})},(0,a.useEffect)(u,[]),l})),tn={root:"WzbAF8",container:"mpGTIt",listItem:"O6KwRn",link:"oRtuWN",image:"YaS0jR"},nn="jhxvbR";const an="v1",rn=2,on=1920,sn=1920,cn=1e3,ln=1e3,un={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},dn={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},hn={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},pn={[hn.CENTER]:{x:.5,y:.5},[hn.TOP_LEFT]:{x:0,y:0},[hn.TOP_RIGHT]:{x:1,y:0},[hn.TOP]:{x:.5,y:0},[hn.BOTTOM_LEFT]:{x:0,y:1},[hn.BOTTOM_RIGHT]:{x:1,y:1},[hn.BOTTOM]:{x:.5,y:1},[hn.RIGHT]:{x:1,y:.5},[hn.LEFT]:{x:0,y:.5}},mn={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},fn={BG:"bg",IMG:"img",SVG:"svg"},gn={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},En={classic:1,super:2},Tn={radius:"0.66",amount:"1.00",threshold:"0.01"},bn={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},vn=25e6,_n=[1.5,2,4],In={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},Ln={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},Cn={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},xn={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},yn={AVIF:"AVIF",PAVIF:"PAVIF"};xn.JPG,xn.JPEG,xn.JPE,xn.PNG,xn.GIF,xn.WEBP;function kn(e,...t){return function(...n){const a=n[n.length-1]||{},r=[e[0]];return t.forEach((function(t,i){const o=Number.isInteger(t)?n[t]:a[t];r.push(o,e[i+1])})),r.join("")}}function wn(e){return e[e.length-1]}const An=[xn.PNG,xn.JPEG,xn.JPG,xn.JPE,xn.WIX_ICO_MP,xn.WIX_MP,xn.WEBP,xn.AVIF],Nn=[xn.JPEG,xn.JPG,xn.JPE];function On(e,t,n){return n&&t&&!(!(a=t.id)||!a.trim()||"none"===a.toLowerCase())&&Object.values(un).includes(e);var a}function Mn(e,t,n){return function(e,t,n=!1){return!((Sn(e)||Fn(e))&&t&&!n)}(e,t,n)&&(function(e){return An.includes($n(e))}(e)||function(e,t=!1){return Pn(e)&&t}(e,n))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function Rn(e){return $n(e)===xn.PNG}function Sn(e){return $n(e)===xn.WEBP}function Pn(e){return $n(e)===xn.GIF}function Fn(e){return $n(e)===xn.AVIF}const Gn=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),Bn=["\\.","\\*"],Hn="_";function Dn(e){return function(e){return Nn.includes($n(e))}(e)?xn.JPG:Rn(e)?xn.PNG:Sn(e)?xn.WEBP:Pn(e)?xn.GIF:Fn(e)?xn.AVIF:xn.UNRECOGNIZED}function $n(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function jn(e,t,n,a,r){let i;return i=r===dn.FILL?function(e,t,n,a){return Math.max(n/e,a/t)}(e,t,n,a):r===dn.FIT?function(e,t,n,a){return Math.min(n/e,a/t)}(e,t,n,a):1,i}function Un(e,t,n,a,r,i){e=e||a.width,t=t||a.height;const{scaleFactor:o,width:s,height:c}=function(e,t,n,a,r){let i,o=n,s=a;if(i=jn(e,t,n,a,r),r===dn.FIT&&(o=e*i,s=t*i),o&&s&&o*s>vn){const n=Math.sqrt(vn/(o*s));o*=n,s*=n,i=jn(e,t,o,s,r)}return{scaleFactor:i,width:o,height:s}}(e,t,a.width*r,a.height*r,n);return function(e,t,n,a,r,i,o){const{optimizedScaleFactor:s,upscaleMethodValue:c,forceUSM:l}=function(e,t,n,a){if("auto"===a)return function(e,t){const n=Wn(e,t);return{optimizedScaleFactor:In[n].maxUpscale,upscaleMethodValue:En.classic,forceUSM:!1}}(e,t);if("super"===a)return function(e){return{optimizedScaleFactor:wn(_n),upscaleMethodValue:En.super,forceUSM:!(_n.includes(e)||e>wn(_n))}}(n);return function(e,t){const n=Wn(e,t);return{optimizedScaleFactor:In[n].maxUpscale,upscaleMethodValue:En.classic,forceUSM:!1}}(e,t)}(e,t,i,r);let u=n,d=a;if(i<=s)return{width:u,height:d,scaleFactor:i,upscaleMethodValue:c,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case dn.FILL:u=n*(s/i),d=a*(s/i);break;case dn.FIT:u=e*s,d=t*s}return{width:u,height:d,scaleFactor:s,upscaleMethodValue:c,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,s,c,i,o,n)}function Yn(e,t,n,a){const r=zn(n)||function(e=hn.CENTER){return pn[e]}(a);return{x:Math.max(0,Math.min(e.width-t.width,r.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,r.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function Vn(e){return e.alignment&&mn[e.alignment]||mn[hn.CENTER]}function zn(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:Zn(Math.max(0,Math.min(100,e.x))/100,2),y:Zn(Math.max(0,Math.min(100,e.y))/100,2)}),t}function Wn(e,t){const n=e*t;return n>In[Ln.HIGH].size?Ln.HIGH:n>In[Ln.MEDIUM].size?Ln.MEDIUM:n>In[Ln.LOW].size?Ln.LOW:Ln.TINY}function Zn(e,t){const n=Math.pow(10,t||0);return(e*n/n).toFixed(t)}function qn(e){return e&&e.upscaleMethod&&gn[e.upscaleMethod.toUpperCase()]||gn.AUTO}function Jn(e,t){const n=Sn(e)||Fn(e);return $n(e)===xn.GIF||n&&t}const Kn={isMobile:!1},Qn=function(e){return Kn[e]};function Xn(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,n=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&n,Kn["isMobile"]=e}var e}function ea(e,t){const n={css:{container:{}}},{css:a}=n,{fittingType:r}=e;switch(r){case un.ORIGINAL_SIZE:case un.LEGACY_ORIGINAL_SIZE:case un.LEGACY_STRIP_ORIGINAL_SIZE:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat";break;case un.SCALE_TO_FIT:case un.LEGACY_STRIP_SCALE_TO_FIT:a.container.backgroundSize="contain",a.container.backgroundRepeat="no-repeat";break;case un.STRETCH:a.container.backgroundSize="100% 100%",a.container.backgroundRepeat="no-repeat";break;case un.SCALE_TO_FILL:case un.LEGACY_STRIP_SCALE_TO_FILL:a.container.backgroundSize="cover",a.container.backgroundRepeat="no-repeat";break;case un.TILE_HORIZONTAL:case un.LEGACY_STRIP_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case un.TILE_VERTICAL:case un.LEGACY_STRIP_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case un.TILE:case un.LEGACY_STRIP_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case un.LEGACY_STRIP_FIT_AND_TILE:a.container.backgroundSize="contain",a.container.backgroundRepeat="repeat";break;case un.FIT_AND_TILE:case un.LEGACY_BG_FIT_AND_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case un.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case un.LEGACY_BG_FIT_AND_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case un.LEGACY_BG_NORMAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat"}switch(t.alignment){case hn.CENTER:a.container.backgroundPosition="center center";break;case hn.LEFT:a.container.backgroundPosition="left center";break;case hn.RIGHT:a.container.backgroundPosition="right center";break;case hn.TOP:a.container.backgroundPosition="center top";break;case hn.BOTTOM:a.container.backgroundPosition="center bottom";break;case hn.TOP_RIGHT:a.container.backgroundPosition="right top";break;case hn.TOP_LEFT:a.container.backgroundPosition="left top";break;case hn.BOTTOM_RIGHT:a.container.backgroundPosition="right bottom";break;case hn.BOTTOM_LEFT:a.container.backgroundPosition="left bottom"}return n}const ta={[hn.CENTER]:"center",[hn.TOP]:"top",[hn.TOP_LEFT]:"top left",[hn.TOP_RIGHT]:"top right",[hn.BOTTOM]:"bottom",[hn.BOTTOM_LEFT]:"bottom left",[hn.BOTTOM_RIGHT]:"bottom right",[hn.LEFT]:"left",[hn.RIGHT]:"right"},na={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function aa(e,t){const n={css:{container:{},img:{}}},{css:a}=n,{fittingType:r}=e,i=t.alignment;switch(a.container.position="relative",r){case un.ORIGINAL_SIZE:case un.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(a.img.width=e.parts[0].width,a.img.height=e.parts[0].height):(a.img.width=e.src.width,a.img.height=e.src.height);break;case un.SCALE_TO_FIT:case un.LEGACY_FIT_WIDTH:case un.LEGACY_FIT_HEIGHT:case un.LEGACY_FULL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="contain",a.img.objectPosition=ta[i]||"unset";break;case un.LEGACY_BG_NORMAL:a.img.width="100%",a.img.height="100%",a.img.objectFit="none",a.img.objectPosition=ta[i]||"unset";break;case un.STRETCH:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="fill";break;case un.SCALE_TO_FILL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="cover"}if("number"==typeof a.img.width&&"number"==typeof a.img.height&&(a.img.width!==t.width||a.img.height!==t.height)){const e=Math.round((t.height-a.img.height)/2),n=Math.round((t.width-a.img.width)/2);Object.assign(a.img,na,function(e,t,n){return{[hn.TOP_LEFT]:{top:0,left:0},[hn.TOP_RIGHT]:{top:0,right:0},[hn.TOP]:{top:0,left:t},[hn.BOTTOM_LEFT]:{bottom:0,left:0},[hn.BOTTOM_RIGHT]:{bottom:0,right:0},[hn.BOTTOM]:{bottom:0,left:t},[hn.RIGHT]:{top:e,right:0},[hn.LEFT]:{top:e,left:0},[hn.CENTER]:{width:n.width,height:n.height,objectFit:"none"}}}(e,n,t)[i])}return n}function ra(e,t){const n={css:{container:{}},attr:{container:{},img:{}}},{css:a,attr:r}=n,{fittingType:i}=e,o=t.alignment,{width:s,height:c}=e.src;let l;switch(a.container.position="relative",i){case un.ORIGINAL_SIZE:case un.LEGACY_ORIGINAL_SIZE:case un.TILE:e.parts&&e.parts.length?(r.img.width=e.parts[0].width,r.img.height=e.parts[0].height):(r.img.width=s,r.img.height=c),r.img.preserveAspectRatio="xMidYMid slice";break;case un.SCALE_TO_FIT:case un.LEGACY_FIT_WIDTH:case un.LEGACY_FIT_HEIGHT:case un.LEGACY_FULL:r.img.width="100%",r.img.height="100%",r.img.transform="",r.img.preserveAspectRatio="";break;case un.STRETCH:r.img.width=t.width,r.img.height=t.height,r.img.x=0,r.img.y=0,r.img.transform="",r.img.preserveAspectRatio="none";break;case un.SCALE_TO_FILL:Mn(e.src.id)?(r.img.width=t.width,r.img.height=t.height):(l=function(e,t,n,a,r){const i=jn(e,t,n,a,r);return{width:Math.round(e*i),height:Math.round(t*i)}}(s,c,t.width,t.height,dn.FILL),r.img.width=l.width,r.img.height=l.height),r.img.x=0,r.img.y=0,r.img.transform="",r.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof r.img.width&&"number"==typeof r.img.height&&(r.img.width!==t.width||r.img.height!==t.height)){let e,n,a=0,s=0;i===un.TILE?(e=t.width%r.img.width,n=t.height%r.img.height):(e=t.width-r.img.width,n=t.height-r.img.height);const c=Math.round(e/2),l=Math.round(n/2);switch(o){case hn.TOP_LEFT:a=0,s=0;break;case hn.TOP:a=c,s=0;break;case hn.TOP_RIGHT:a=e,s=0;break;case hn.LEFT:a=0,s=l;break;case hn.CENTER:a=c,s=l;break;case hn.RIGHT:a=e,s=l;break;case hn.BOTTOM_LEFT:a=0,s=n;break;case hn.BOTTOM:a=c,s=n;break;case hn.BOTTOM_RIGHT:a=e,s=n}r.img.x=a,r.img.y=s}return r.container.width=t.width,r.container.height=t.height,r.container.viewBox=[0,0,t.width,t.height].join(" "),n}function ia(e,t,n){let a;switch(t.crop&&(a=function(e,t){const n=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),a=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return n&&a&&(e.width!==n||e.height!==a)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:n,height:a}:null}(t,t.crop),a&&(e.src.width=a.width,e.src.height=a.height,e.src.isCropped=!0,e.parts.push(sa(a)))),e.fittingType){case un.SCALE_TO_FIT:case un.LEGACY_FIT_WIDTH:case un.LEGACY_FIT_HEIGHT:case un.LEGACY_FULL:case un.FIT_AND_TILE:case un.LEGACY_BG_FIT_AND_TILE:case un.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case un.LEGACY_BG_FIT_AND_TILE_VERTICAL:case un.LEGACY_BG_NORMAL:e.parts.push(oa(e,n));break;case un.SCALE_TO_FILL:e.parts.push(function(e,t){const n=Un(e.src.width,e.src.height,dn.FILL,t,e.devicePixelRatio,e.upscaleMethod),a=zn(e.focalPoint);return{transformType:a?dn.FILL_FOCAL:dn.FILL,width:Math.round(n.width),height:Math.round(n.height),alignment:Vn(t),focalPointX:a&&a.x,focalPointY:a&&a.y,upscale:n.scaleFactor>1,forceUSM:n.forceUSM,scaleFactor:n.scaleFactor,cssUpscaleNeeded:n.cssUpscaleNeeded,upscaleMethodValue:n.upscaleMethodValue}}(e,n));break;case un.STRETCH:e.parts.push(function(e,t){const n=jn(e.src.width,e.src.height,t.width,t.height,dn.FILL),a={...t};return a.width=e.src.width*n,a.height=e.src.height*n,oa(e,a)}(e,n));break;case un.TILE_HORIZONTAL:case un.TILE_VERTICAL:case un.TILE:case un.LEGACY_ORIGINAL_SIZE:case un.ORIGINAL_SIZE:a=Yn(e.src,n,e.focalPoint,n.alignment),e.src.isCropped?(Object.assign(e.parts[0],a),e.src.width=a.width,e.src.height=a.height):e.parts.push(sa(a));break;case un.LEGACY_STRIP_TILE_HORIZONTAL:case un.LEGACY_STRIP_TILE_VERTICAL:case un.LEGACY_STRIP_TILE:case un.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:dn.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:Vn(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(n));break;case un.LEGACY_STRIP_SCALE_TO_FIT:case un.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:dn.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(n));break;case un.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:dn.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:Vn(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(n))}}function oa(e,t){const n=Un(e.src.width,e.src.height,dn.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?dn.FIT:dn.FILL,width:Math.round(n.width),height:Math.round(n.height),alignment:mn.center,upscale:n.scaleFactor>1,forceUSM:n.forceUSM,scaleFactor:n.scaleFactor,cssUpscaleNeeded:n.cssUpscaleNeeded,upscaleMethodValue:n.upscaleMethodValue}}function sa(e){return{transformType:dn.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function ca(e,t){t=t||{},e.quality=function(e,t){const n=e.fileType===xn.PNG,a=e.fileType===xn.JPG,r=e.fileType===xn.WEBP,i=e.fileType===xn.AVIF,o=a||n||r||i;if(o){const a=wn(e.parts),r=(s=a.width,c=a.height,In[Wn(s,c)].quality);let i=t.quality&&t.quality>=5&&t.quality<=90?t.quality:r;return i=n?i+5:i,i}var s,c;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,n="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,a="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&n&&a}(t.unsharpMask))return{radius:Zn(t.unsharpMask?.radius,2),amount:Zn(t.unsharpMask?.amount,2),threshold:Zn(t.unsharpMask?.threshold,2)};if(("number"!=typeof(n=(n=t.unsharpMask)||{}).radius||isNaN(n.radius)||0!==n.radius||"number"!=typeof n.amount||isNaN(n.amount)||0!==n.amount||"number"!=typeof n.threshold||isNaN(n.threshold)||0!==n.threshold)&&function(e){const t=wn(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===dn.FIT}(e))return Tn;var n;return}(e,t),e.filters=function(e){const t=e.filters||{},n={};la(t[Cn.CONTRAST],-100,100)&&(n[Cn.CONTRAST]=t[Cn.CONTRAST]);la(t[Cn.BRIGHTNESS],-100,100)&&(n[Cn.BRIGHTNESS]=t[Cn.BRIGHTNESS]);la(t[Cn.SATURATION],-100,100)&&(n[Cn.SATURATION]=t[Cn.SATURATION]);la(t[Cn.HUE],-180,180)&&(n[Cn.HUE]=t[Cn.HUE]);la(t[Cn.BLUR],0,100)&&(n[Cn.BLUR]=t[Cn.BLUR]);return n}(t)}function la(e,t,n){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=n}function ua(e,t,n,a){const r=function(e){return e?.isSEOBot??!1}(a),i=Dn(t.id),o=function(e,t){const n=/\.([^.]*)$/,a=new RegExp(`(${Gn.concat(Bn).join("|")})`,"g");if(t&&t.length){let e=t;const r=t.match(n);return r&&An.includes(r[1])&&(e=t.replace(n,"")),encodeURIComponent(e).replace(a,Hn)}const r=e.match(/\/(.*?)$/);return(r?r[1]:e).replace(n,"")}(t.id,t.name),s=r?1:function(e){return Math.min(e.pixelAspectRatio||1,rn)}(n),c=$n(t.id),l=c,u=Mn(t.id,a?.hasAnimation,a?.allowAnimatedTransform),d={fileName:o,fileExtension:c,fileType:i,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:Jn(t.id,a?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:s,quality:0,upscaleMethod:qn(a),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:u};return u&&(ia(d,t,n),ca(d,a)),d}function da(e,t,n){const a={...n},r=Qn("isMobile");switch(e){case un.LEGACY_BG_FIT_AND_TILE:case un.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case un.LEGACY_BG_FIT_AND_TILE_VERTICAL:case un.LEGACY_BG_NORMAL:const e=r?cn:on,n=r?ln:sn;a.width=Math.min(e,t.width),a.height=Math.min(n,Math.round(a.width/(t.width/t.height))),a.pixelAspectRatio=1}return a}const ha=kn`fit/w_${"width"},h_${"height"}`,pa=kn`fill/w_${"width"},h_${"height"},al_${"alignment"}`,ma=kn`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,fa=kn`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,ga=kn`crop/w_${"width"},h_${"height"},al_${"alignment"}`,Ea=kn`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Ta=kn`,lg_${"upscaleMethodValue"}`,ba=kn`,q_${"quality"}`,va=kn`,quality_auto`,_a=kn`,usm_${"radius"}_${"amount"}_${"threshold"}`,Ia=kn`,bl`,La=kn`,wm_${"watermark"}`,Ca={[Cn.CONTRAST]:kn`,con_${"contrast"}`,[Cn.BRIGHTNESS]:kn`,br_${"brightness"}`,[Cn.SATURATION]:kn`,sat_${"saturation"}`,[Cn.HUE]:kn`,hue_${"hue"}`,[Cn.BLUR]:kn`,blur_${"blur"}`},xa=kn`,enc_auto`,ya=kn`,enc_avif`,ka=kn`,enc_pavif`,wa=kn`,pstr`;function Aa(e,t,n,a={},r){if(Mn(t.id,a?.hasAnimation,a?.allowAnimatedTransform)){if(Sn(t.id)||Fn(t.id)){const{alignment:i,...o}=n;t.focalPoint={x:void 0,y:void 0},delete t?.crop,r=ua(e,t,o,a)}else r=r||ua(e,t,n,a);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case dn.CROP:t.push(fa(e));break;case dn.LEGACY_CROP:t.push(ga(e));break;case dn.LEGACY_FILL:let n=Ea(e);e.upscale&&(n+=Ta(e)),t.push(n);break;case dn.FIT:let a=ha(e);e.upscale&&(a+=Ta(e)),t.push(a);break;case dn.FILL:let r=pa(e);e.upscale&&(r+=Ta(e)),t.push(r);break;case dn.FILL_FOCAL:let i=ma(e);e.upscale&&(i+=Ta(e)),t.push(i)}}));let n=t.join("/");return e.quality&&(n+=ba(e)),e.unsharpMask&&(n+=_a(e.unsharpMask)),e.progressive||(n+=Ia(e)),e.watermark&&(n+=La(e)),e.filters&&(n+=Object.keys(e.filters).map((t=>Ca[t](e.filters))).join("")),e.fileType!==xn.GIF&&(e.encoding===yn.AVIF?(n+=ya(e),n+=va(e)):e.encoding===yn.PAVIF?(n+=ka(e),n+=va(e)):e.autoEncode&&(n+=xa(e))),e.src?.isAnimated&&e.transformed&&(n+=wa(e)),`${e.src.id}/${an}/${n}/${e.fileName}.${e.preferredExtension}`}(r)}return t.id}const Na={[hn.CENTER]:"50% 50%",[hn.TOP_LEFT]:"0% 0%",[hn.TOP_RIGHT]:"100% 0%",[hn.TOP]:"50% 0%",[hn.BOTTOM_LEFT]:"0% 100%",[hn.BOTTOM_RIGHT]:"100% 100%",[hn.BOTTOM]:"50% 100%",[hn.RIGHT]:"100% 50%",[hn.LEFT]:"0% 50%"},Oa=Object.entries(Na).reduce(((e,[t,n])=>(e[n]=t,e)),{}),Ma=[un.TILE,un.TILE_HORIZONTAL,un.TILE_VERTICAL,un.LEGACY_BG_FIT_AND_TILE,un.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,un.LEGACY_BG_FIT_AND_TILE_VERTICAL],Ra=[un.LEGACY_ORIGINAL_SIZE,un.ORIGINAL_SIZE,un.LEGACY_BG_NORMAL];function Sa(e,t,{width:n,height:a}){return e===un.TILE&&t.width>n&&t.height>a}function Pa(e,{width:t,height:n}){if(!t||!n){const a=t||Math.min(980,e.width),r=a/e.width;return{width:a,height:n||e.height*r}}return{width:t,height:n}}function Fa(e,t,n,a="center"){const r={img:{},container:{}};if(e===un.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return Oa[t]||""}(t.focalPoint),i=e||a;t.focalPoint&&!e?r.img={objectPosition:Ga(t,n,t.focalPoint)}:r.img={objectPosition:Na[i]}}else[un.LEGACY_ORIGINAL_SIZE,un.ORIGINAL_SIZE].includes(e)?r.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:Ma.includes(e)&&(r.container={backgroundSize:`${t.width}px ${t.height}px`});return r}function Ga(e,t,n){const{width:a,height:r}=e,{width:i,height:o}=t,{x:s,y:c}=n;if(!i||!o)return`${s}% ${c}%`;const l=Math.max(i/a,o/r),u=a*l,d=r*l,h=Math.max(0,Math.min(u-i,u*(s/100)-i/2)),p=Math.max(0,Math.min(d-o,d*(c/100)-o/2));return`${h&&Math.floor(h/(u-i)*100)}% ${p&&Math.floor(p/(d-o)*100)}%`}const Ba={width:"100%",height:"100%"};function Ha(e,t,n,a={}){const{autoEncode:r=!0,isSEOBot:i,shouldLoadHQImage:o,hasAnimation:s,allowAnimatedTransform:c,encoding:l}=a;if(!On(e,t,n))return bn;const u=void 0===c||c,d=Mn(t.id,s,u);if(!d||o)return Da(e,t,n,{...a,autoEncode:r,useSrcset:d});const h={...n,...Pa(t,n)},{alignment:p,htmlTag:m}=h,f=Sa(e,t,h),g=function(e,t,{width:n,height:a},r=!1){if(r)return{width:n,height:a};const i=!Ra.includes(e),o=Sa(e,t,{width:n,height:a}),s=!o&&Ma.includes(e),c=s?t.width:n,l=s?t.height:a,u=i?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(c,Rn(t.id)):1;return{width:o?1920:c*u,height:l*u}}(e,t,h,i),E=function(e,t,n){return n?0:Ma.includes(t)?1:e>200?2:3}(h.width,e,i),T=function(e,t){const n=Ma.includes(e)&&!t;return e===un.SCALE_TO_FILL||n?un.SCALE_TO_FIT:e}(e,f),b=Fa(e,t,n,p),{uri:v}=Da(T,t,{...g,alignment:p,htmlTag:m},{autoEncode:r,filters:E?{blur:E}:{},hasAnimation:s,allowAnimatedTransform:u,encoding:l}),{attr:_={},css:I}=Da(e,t,{...h,alignment:p,htmlTag:m},{});return I.img=I.img||{},I.container=I.container||{},Object.assign(I.img,b.img,Ba),Object.assign(I.container,b.container),{uri:v,css:I,attr:_,transformed:!0}}function Da(e,t,n,a){let r={};if(On(e,t,n)){const i=da(e,t,n),o=ua(e,t,i,a);r.uri=Aa(e,t,i,a,o),a?.useSrcset&&(r.srcset=function(e,t,n,a,r){const i=n.pixelAspectRatio||1;return{dpr:[`${1===i?r.uri:Aa(e,t,{...n,pixelAspectRatio:1},a)} 1x`,`${2===i?r.uri:Aa(e,t,{...n,pixelAspectRatio:2},a)} 2x`]}}(e,t,i,a,r)),Object.assign(r,function(e,t){let n;return n=t.htmlTag===fn.BG?ea:t.htmlTag===fn.SVG?ra:aa,n(e,t)}(o,i),{transformed:o.transformed})}else r=bn;return r}const $a="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;Xn();Xn();const ja=$a,{STATIC_MEDIA_URL:Ua}=e,Ya=({fittingType:e,src:t,target:n,options:a})=>{const r=Ha(e,t,n,{...a,autoEncode:!0});return r?.uri&&!/^[a-z]+:/.test(r.uri)&&(r.uri=`${Ua}${r.uri}`),r},Va=/^[a-z]+:/,za=e=>{const{id:t,containerId:n,uri:r,alt:i,name:o="",role:s,width:c,height:l,displayMode:u,devicePixelRatio:d,quality:h,alignType:p,bgEffectName:m="",focalPoint:f,upscaleMethod:g,className:E="",crop:T,imageStyles:b={},targetWidth:v,targetHeight:_,targetScale:I,onLoad:L=()=>{},onError:C=()=>{},shouldUseLQIP:x,containerWidth:y,containerHeight:k,getPlaceholder:w,isInFirstFold:A,placeholderTransition:N,socialAttrs:O,isSEOBot:M,skipMeasure:R,hasAnimation:S,encoding:P}=e,F=a.useRef(null);let G="";const B="blur"===N,H=a.useRef(null);if(!H.current)if(w||x||A||M){const e={upscaleMethod:g,...h||{},shouldLoadHQImage:A,isSEOBot:M,hasAnimation:S,encoding:P};H.current=(w||Ya)({fittingType:u,src:{id:r,width:c,height:l,crop:T,name:o,focalPoint:f},target:{width:y,height:k,alignment:p,htmlTag:"img"},options:e}),G=!H.current.transformed||A||M?"":"true"}else H.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const D=!M&&(w||x)&&!A&&H.current.transformed,$=a.useMemo((()=>JSON.stringify({containerId:n,...n&&{containerId:n},...p&&{alignType:p},...R&&{skipMeasure:!0},displayMode:u,...y&&{targetWidth:y},...k&&{targetHeight:k},...v&&{targetWidth:v},..._&&{targetHeight:_},...I&&{targetScale:I},isLQIP:D,isSEOBot:M,lqipTransition:N,encoding:P,imageData:{width:c,height:l,uri:r,name:o,displayMode:u,hasAnimation:S,...h&&{quality:h},...d&&{devicePixelRatio:d},...f&&{focalPoint:f},...T&&{crop:T},...g&&{upscaleMethod:g}}})),[n,p,R,u,y,k,v,_,I,D,M,N,P,c,l,r,o,S,h,d,f,T,g]),j=H.current,U=j?.uri,Y=j?.srcset,V=j.css?.img,z=`${nn} ${E}`;a.useEffect((()=>{const e=F.current;L&&e?.currentSrc&&e?.complete&&L({target:e})}),[]);const W=j&&!j?.transformed?`max(${c}px, 100%)`:v?`${v}px`:null;return a.createElement("wow-image",{id:t,class:z,"data-image-info":$,"data-motion-part":`BG_IMG ${n}`,"data-bg-effect-name":m,"data-has-ssr-src":G,"data-animate-blur":!M&&D&&B?"":void 0,style:W?{"--wix-img-max-width":W}:{}},a.createElement("img",{src:U,ref:F,alt:i||"",role:s,style:{...V,...b},onLoad:L,onError:C,width:y||void 0,height:k||void 0,...O,srcSet:A?Y?.dpr?.map((e=>Va.test(e)?e:`${Ua}${e}`)).join(", "):void 0,fetchpriority:A?"high":void 0,loading:!1===A?"lazy":void 0,suppressHydrationWarning:!0}))};var Wa="Tj01hh";var Za=e=>{var t,r;const{id:i,alt:o,role:s,className:l,imageStyles:u={},targetWidth:d,targetHeight:h,onLoad:p,onError:m,containerWidth:f,containerHeight:g,isInFirstFold:E,socialAttrs:T,skipMeasure:b,responsiveImageProps:v,zoomedImageResponsiveOverride:_,displayMode:I}=e,L=d||f,C=h||g,{fallbackSrc:x,srcset:y,sources:k,css:w}=v||{},{width:A,height:N,...O}=(null==v||null==(t=v.css)?void 0:t.img)||{},M="original_size"===I?null==v||null==(r=v.css)?void 0:r.img:O;var R;return x&&y&&w?a.createElement("img",n()({fetchpriority:E?"high":void 0,loading:!1===E?"lazy":void 0,sizes:L+"px",srcSet:b?null==_?void 0:_.srcset:null==v?void 0:v.srcset,id:i,src:x,alt:o||"",role:s,style:{...u,...b?{...null==_||null==(R=_.css)?void 0:R.img}:{...M}},onLoad:p,onError:m,className:c(l,Wa),width:L,height:C},T)):x&&k&&w?a.createElement("picture",null,k.map((e=>{let{srcset:t,media:n,sizes:r}=e;return a.createElement("source",{key:n,srcSet:t,media:n,sizes:r})})),a.createElement("img",n()({fetchpriority:E?"high":void 0,loading:!1===E?"lazy":void 0,id:i,src:k[0].fallbackSrc,alt:o||"",role:s,style:{...u,objectFit:k[0].imgStyle.objectFit,objectPosition:k[0].imgStyle.objectPosition},onLoad:p,onError:m,className:c(l,Wa),width:L,height:C},T))):a.createElement(za,e)};var qa=e=>{const{id:t,images:r,styles:i,iconSize:o,className:s,shouldRenderPlaceholders:l=!1,translations:d,onMouseEnter:h,onMouseLeave:p,getPlaceholder:m,reportBiOnClick:f}=e,g=l&&o&&m?{getPlaceholder:m,containerWidth:o,containerHeight:o}:{};return a.createElement("div",n()({id:t},u(e),{className:c(s,i.root),onMouseEnter:h,onMouseLeave:p}),a.createElement("ul",{className:i.container,"aria-label":d.ariaLabel},r.map(((e,r)=>a.createElement("li",{id:e.containerId,key:e.containerId,onClick:()=>f(e),className:i.listItem},a.createElement(Tt,n()({className:i.link},e.link,{"aria-label":e.alt}),a.createElement(Za,n()({id:"img_"+r+"_"+t},(e=>{let{link:t,...n}=e;return n})(e),{className:i.image},g))))))))};var Ja=e=>a.createElement(qa,n()({},e,{styles:tn}));var Ka=ot((e=>{let{mapperProps:t,stateValues:n}=e;const{compId:a,language:r,fullNameCompType:i,trackClicksAnalytics:o,...s}=t;return{...s,reportBiOnClick:e=>{var t;dt(n.reportBi,{language:r,trackClicksAnalytics:o,element_id:a,value:null==(t=e.link)?void 0:t.href,elementTitle:e.title,details:{uri:e.uri},elementType:i,elementGroup:ht.Social})}}}));var Qa={root:"big2ZD","responsive-root":"SHHiV9",responsiveRoot:"SHHiV9"};const Xa={Container:{component:A},BackgroundGroup:{component:Me},PageGroup:{component:Fe},StylableButton:{component:rt,controller:pt},VectorImage:{component:It,controller:Lt},WRichText:{component:Mt,controller:St},Anchor:{component:Gt},FooterContainer_TransparentScreen:{component:Yt},HeaderContainer_TransparentScreen:{component:Xt,controller:en},LinkBar_Classic:{component:Ja,controller:Ka},PagesContainer:{component:e=>{const{children:t,className:r}=e;return a.createElement("main",n()({id:"PAGES_CONTAINER"},u(e),{className:r,tabIndex:-1,"data-main-content":!0}),t())}},MasterPage:{component:e=>{const{classNames:t={},pageDidMount:r,children:i,className:o}=e,s=c(Object.values(t),o,"css-editing-scope");return a.createElement("div",n()({id:"masterPage"},u(e),{className:s,ref:r}),i())}},PinnedLayer:{component:e=>{let{id:t,className:n,rootClassName:r="root",children:i}=e;return a.createElement("div",{id:t,className:c(n,Qa[r])},i())}}}}(),i}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt_bootstrap.3fbbd89c.bundle.min.js.map