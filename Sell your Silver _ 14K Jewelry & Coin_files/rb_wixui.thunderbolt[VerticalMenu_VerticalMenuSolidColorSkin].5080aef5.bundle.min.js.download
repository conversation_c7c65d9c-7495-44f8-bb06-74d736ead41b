!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[VerticalMenu_VerticalMenuSolidColorSkin]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[VerticalMenu_VerticalMenuSolidColorSkin]"]=t(require("react")):e["rb_wixui.thunderbolt[VerticalMenu_VerticalMenuSolidColorSkin]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,r),i.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};return function(){"use strict";r.r(a),r.d(a,{components:function(){return z}});var e=r(448),t=r.n(e),n=r(5329),i=r.n(n);const o=(e,t)=>{let n=[];return null==t||t.forEach((t=>{var r,a;const i=!(null==(r=t.link)||!r.anchorDataId)||!(null==(a=t.link)||!a.anchorCompId),l=(e=>(e||"").replace("./","").split("/").slice(1))(e).some((e=>{var n;return e&&e===(null==(n=t.link)?void 0:n.innerRoute)}));if(!1!==t.selected)if(t.selected||t.link&&(t.link.href===e||l)&&!i)n.push(t);else{const r=o(e,t.items);r.length>0&&(n=[...r,...n,t])}})),n},l=(e,t)=>e.map((e=>({...e,items:l(e.items||[],t),selected:t.includes(e)})));var u=(e,t)=>{const n=o(e,t);return l(t,n)};const s=e=>e.filter((e=>(e=>void 0===e.isVisible||e.isVisible)(e))).map((e=>void 0!==e.items&&e.items.length?{...e,items:s(e.items)}:e));var c=s;function d(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=d(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var m=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=d(e))&&(r&&(r+=" "),r+=t);return r};const p=(e,t=0,{leading:n=!1,trailing:r=!0}={})=>{let a=null;return function(...i){n&&null===a&&e.apply(this,i),a&&clearTimeout(a),a=r&&n&&!a?setTimeout((()=>{a=null}),t):setTimeout((()=>{r&&e.apply(this,i),a=null}),t)}},f=()=>"undefined"!=typeof window,b=e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{});const h="wixui-",v=(e,...t)=>{const n=[];return e&&n.push(`${h}${e}`),t.forEach((e=>{e&&(n.push(`${h}${e}`),n.push(e))})),n.join(" ")};var g={root:"vertical-menu",menuItem:"vertical-menu__item",subMenu:"vertical-menu__submenu",menuItemLabel:"vertical-menu__item-label"};const k=13,y=27;function I(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const M=I(32),C=I(k),S=e=>{C(e),M(e)},L=(I(y),{root:"linkElement"}),E=(e,r)=>{const{href:a,role:i,target:o,rel:l,className:u="",children:s,linkPopupId:c,anchorDataId:d,anchorCompId:m,tabIndex:p,dataTestId:f=L.root,title:h,onClick:v,onDoubleClick:g,onMouseEnter:k,onMouseLeave:y,onFocus:I,onFocusCapture:E,onBlurCapture:O,"aria-live":w,"aria-disabled":x,"aria-label":B,"aria-labelledby":D,"aria-pressed":T,"aria-expanded":_,"aria-describedby":A,"aria-haspopup":P,"aria-current":R,dataPreview:N,dataPart:H}=e,V=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(c);let j;switch(V){case"Enter":j=C;break;case"Space":j=M;break;case"SpaceOrEnter":j=S;break;default:j=void 0}return void 0!==a||c?n.createElement("a",t()({},b(e),{"data-testid":f,"data-popupid":c,"data-anchor":d,"data-anchor-comp-id":m,"data-preview":N,"data-part":H,href:a||void 0,target:o,role:c?"button":i,rel:l,className:u,onKeyDown:j,"aria-live":w,"aria-disabled":x,"aria-label":B,"aria-labelledby":D,"aria-pressed":T,"aria-expanded":_,"aria-haspopup":P,"aria-describedby":A,"aria-current":R,title:h,onClick:v,onMouseEnter:k,onMouseLeave:y,onDoubleClick:g,onFocus:I,onFocusCapture:E,onBlurCapture:O,ref:r,tabIndex:c?0:p}),s):n.createElement("div",t()({},b(e),{"data-testid":f,"data-preview":N,"data-part":H,className:u,tabIndex:p,"aria-label":B,"aria-labelledby":D,"aria-haspopup":P,"aria-disabled":x,"aria-expanded":_,title:h,role:i,onClick:v,onDoubleClick:g,onMouseEnter:k,onMouseLeave:y,ref:r}),s)};var O=n.forwardRef(E);const w=e=>"linkElement-"+e,x=e=>"subMenu-"+e,B="subMenu-",D=e=>"itemContentWrapper-"+e;function T(e,t){const[r,a]=(0,n.useState)("bottom"),i=()=>{a((n=>{if(!e.current)return n;const r=e.current.querySelectorAll("[id^="+B+"]"),a=Array.from(r).every(((e,t)=>n=>{const r=n.getBoundingClientRect();let a,i;return"top"===e?(a=r.height<=r.bottom,i=r.height<=r.bottom+t+document.documentElement.clientHeight):(i=r.top+r.height<=document.documentElement.clientHeight,a=r.height<=r.top+t),!("bottom"!==e||i||!a)||!("top"!==e||!i)})(n,t));return"bottom"===n&&a?"top":"top"===n&&a?"bottom":n}))};return(0,n.useEffect)((()=>{if(!f())return;const e=p(i,300);return window.addEventListener("resize",e),window.addEventListener("scroll",e),i(),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e)}}),[]),r}const _=(e,t)=>{if(!e)return!1;const n=[];let r=!0;for(;r;){n.push(e);const t=e.lastIndexOf("-");-1===t?r=!1:e=e.slice(0,t)}return n.push(e),n.includes(t)},A=()=>{document.activeElement instanceof HTMLElement&&document.activeElement.blur()},P=e=>{let{navRef:r,item:a,uniqueId:o,isSubItem:l,menuItemHeight:u,style:s,separatedButton:c,subMenuOpenSide:d,parentMenuItemRef:p,reportBiOnClick:f,onItemClick:b,onItemMouseIn:h,onItemMouseOut:k,onItemDblClick:y,highlightedLinkId:I,setHighlightedLinkId:M}=e;const C=T(r,u),S=(0,n.useRef)(null),L=e=>{if(!r.current)return!1;const t=e.relatedTarget;return r.current.contains(t)},E=a.items&&a.items.length>0;return i().createElement("li",{className:m(s.item,v(l?g.subMenu:g.menuItem)),key:o},i().createElement("div",{"data-testid":D(o),className:m(s.itemContentWrapper,a.selected&&s.selected,(N=a.link,!Boolean(N&&(N.href||N.linkPopupId))&&s.noLink),_(I,o)&&s.itemHighlight)},i().createElement("span",{className:m(s.linkWrapper,s.linkContainer),onMouseEnter:()=>M(o),onFocus:()=>M(o),onMouseUp:A,onKeyUp:e=>"Enter"===e.key&&A(),onMouseOut:e=>!L(e)&&M(null),onBlur:e=>{L(e)||M(null)},onKeyDown:e=>{var t;"Escape"===e.key&&(e.stopPropagation(),null==p||null==(t=p.current)||t.focus(),M(null))}},i().createElement(O,t()({dataTestId:w(o),className:m(s.label,l?s.subItemLabel:s.itemLabel,v(g.menuItemLabel))},a.link,{"aria-haspopup":E?"true":void 0,"aria-current":a.selected?"page":void 0,tabIndex:0,onClick:e=>{null==b||b(e,a)},onMouseEnter:e=>{null==h||h(e,a)},onMouseLeave:e=>{null==k||k(e,a)},onDoubleClick:e=>{null==y||y(e,a)},ref:S}),a.label,a.displayCount&&i().createElement("span",{className:s.displayCount},"(",a.displayCount,")"))),E&&(B=o,R=a.items,i().createElement("ul",{className:m(s.subMenu,"top"===C?s.menuDirectionTop:s.menuDirectionBottom,"right"===d?s.menuSideRight:s.menuSideLeft),"data-testid":x(B),id:x(B)},R.map(((e,t)=>i().createElement(P,{key:t,item:e,uniqueId:B+"-"+t,isSubItem:!0,navRef:r,menuItemHeight:u,style:s,separatedButton:c,subMenuOpenSide:d,reportBiOnClick:f,onItemClick:b,onItemMouseIn:h,onItemMouseOut:k,onItemDblClick:y,highlightedLinkId:I,setHighlightedLinkId:M,parentMenuItemRef:S})))))),c&&i().createElement("div",{className:s.separator}));var B,R,N};var R=P;var N=e=>{const{items:r,skin:a,id:o,className:l,customClassNames:u=[],ariaLabel:s,menuItemHeight:c,style:d,separatedButton:p,subMenuOpenSide:f,reportBiOnClick:h,onMouseEnter:k,onMouseLeave:y,onItemClick:I,onItemDblClick:M,onItemMouseIn:C,onItemMouseOut:S,lang:L}=e,E=(0,n.useRef)(null),[O,w]=(0,n.useState)(null);return i().createElement("nav",t()({id:o},b(e),{ref:E,"aria-label":s,className:m(d[a],d.autoHeight,l,v(g.root,...u)),tabIndex:-1,onMouseEnter:k,onMouseLeave:y,lang:L}),i().createElement("ul",{className:d.menuContainer},r&&r.map(((e,t)=>i().createElement(R,{key:t,item:e,uniqueId:t.toString(),isSubItem:!1,navRef:E,menuItemHeight:c,style:d,separatedButton:p,subMenuOpenSide:f,reportBiOnClick:h,onItemClick:I,onItemDblClick:M,onItemMouseIn:C,onItemMouseOut:S,highlightedLinkId:O,setHighlightedLinkId:w})))))},H={displayCount:"VUs9VM",autoHeight:"m48Yht",linkContainer:"fIxawB",itemLabel:"Ur5Vmd",subItemLabel:"A47Z2B",VerticalMenuSeparatedButtonFixedWidthSkin:"IOfTmr",verticalMenuSeparatedButtonFixedWidthSkin:"IOfTmr",item:"Z7cwZn",subMenu:"E_2Q_U",menuSideRight:"uf9Zs0",menuSideLeft:"pYmDZK",menuDirectionTop:"OSGBAh",menuDirectionBottom:"F8Lkq5",itemContentWrapper:"YGlZMx",noLink:"amuh9Z",linkWrapper:"DzUZFw",label:"a50_l6",selected:"lBccMF",itemHighlight:"F95vTA",separator:"MfQCD7"};const V=(e,r)=>{var a,i;const{a11y:o,ariaAttributes:l,translations:s,items:d=[],currentUrl:m,style:p}=e,f=n.useMemo((()=>c(d)),[d]),b=n.useMemo((()=>u(m,f)),[m,f]);return n.createElement(N,t()({},e,{ref:r,items:b,ariaLabel:null!=(a=null!=(i=null==l?void 0:l.label)?i:null==o?void 0:o.label)?a:s.ariaLabel,style:{...H,...p}}))};var j=n.forwardRef(V),F={displayCount:"xyNFD3",autoHeight:"gxChTi",VerticalMenuSolidColorSkin:"J5AcBQ",verticalMenuSolidColorSkin:"J5AcBQ",menuContainer:"ADkeoY",item:"ybGdqG",subMenu:"niKtuR",menuSideRight:"pLtej1",menuSideLeft:"UPEerR",menuDirectionTop:"sKAPJX",menuDirectionBottom:"asC21j",itemContentWrapper:"i4bvwx",noLink:"yL5MMl",linkWrapper:"qFH5R7",label:"kFPGSw",selected:"iLEOZ6",itemHighlight:"Bf9iOm"};const U=(e,n)=>i().createElement(j,t()({},e,{ref:n,style:F,separatedButton:!1}));var W=i().forwardRef(U);const Z=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),q=e=>"linkPopupId"in e,G=(e,t)=>{if(q(e))return e.linkPopupId;{const{pagesMap:n,mainPageId:r}=t||{};if(!n)return;const a=new URL(e.href??"");let i=Object.values(n).find((({pageUriSEO:e})=>!!e&&a.pathname?.includes(e)));return i||(i=r?n[r]:void 0),i?.pageId}},K=e=>{if(void 0!==e)return null===e?"None":e.type},$=(e,t)=>{if(!e?.type)return;const{type:n}=e;switch(n){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const n=G(e,t);return n&&t?.pagesMap?.[n]?.title;default:return e.href}},J=(e,t,n)=>{const{link:r,value:a,details:i,actionName:o,elementType:l,trackClicksAnalytics:u,pagesMetadata:s,...c}=t;if(!u)return;const d=s&&{...s,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},m=((e,t)=>{if(!e?.type)return;const{type:n}=e;switch(n){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:G(e,t),isLightbox:q(e)};default:return}})(r,d),p=i||m?JSON.stringify({...m,...i}):void 0;e({src:76,evid:1113,...{...c,bl:navigator.language,url:window.location.href,details:p,elementType:l??"Unknown",actionName:o??K(r),value:a??$(r,d)}},{endpoint:"pa",...n})};var Q;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(Q||(Q={}));var Y;const z={VerticalMenu_VerticalMenuSolidColorSkin:{component:W,controller:(Y=e=>{let{stateValues:t,mapperProps:n}=e;const{currentUrl:r,reportBi:a}=t,{compId:i,language:o,mainPageId:l,fullNameCompType:u,trackClicksAnalytics:s,...c}=n;return{...c,currentUrl:r,reportBiOnClick:e=>{const{link:t,label:n,selected:r}=e;J(a,{link:t,language:o,trackClicksAnalytics:s,element_id:i,elementTitle:n,details:{selected:r},elementType:u,pagesMetadata:{mainPageId:l},elementGroup:Q.MenuAndAnchor})}}},{useComponentProps:(e,t,n)=>{const r=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(r=t,r.startsWith("--")?t:Z(t))]:void 0===n?null:n};var r}),{});e.updateStyles(n)}}))(n);return Y({mapperProps:e,stateValues:t,controllerUtils:r})}})}}}(),a}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[VerticalMenu_VerticalMenuSolidColorSkin].5080aef5.bundle.min.js.map