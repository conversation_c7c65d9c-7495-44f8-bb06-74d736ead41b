!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[HtmlComponent]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[HtmlComponent]"]=t(require("react")):e["rb_wixui.thunderbolt[HtmlComponent]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){"use strict";var t={5329:function(t){t.exports=e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var s=r[e]={exports:{}};return t[e](s,s.exports,n),s.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){n.r(o),n.d(o,{components:function(){return b}});var e=n(5329);function t(e){var r,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(r=0;r<e.length;r++)e[r]&&(n=t(e[r]))&&(o&&(o+=" "),o+=n);else for(r in e)e[r]&&(o&&(o+=" "),o+=r);return o}var r=function(){for(var e,r,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(r=t(e))&&(o&&(o+=" "),o+=r);return o};const s=()=>"undefined"!=typeof window,a=e=>Object.entries(e).reduce(((e,[t,r])=>(t.includes("data-")&&(e[t]=r),e)),{});const u=13,c=27;function i(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}i(32),i(u),i(c);const l=(e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}};var d="_xg6_p",f="SMuTIa",p="wuksD5";const m=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),v=(((e,t,r=[])=>{e.reduce(((e,t)=>({...e,[t]:!0})),{}),r.length})(["isSocialElementsBlocked"],(e=>{let{isSocialElementsBlocked:t}=e;return t?"WithConsentWrapper":void 0})),e=>({consentPolicy:e.currentConsentPolicy,openSettingModal:()=>e.openSettingModal([])}));var y;const b={HtmlComponent:{component:e.forwardRef(((t,n)=>{const{id:o,className:u,url:c,allow:i,scrolling:m,title:v,sandbox:y,onMessage:b,onMouseEnter:g,onMouseLeave:w,translations:x,isConsentPolicyActive:h}=t,[E,C]=e.useState(!1);e.useEffect((()=>C(!0)),[]);const S=e.useCallback((e=>{try{"message"===e.type&&(null==b||b({data:e.payload,type:"message"}))}catch(e){return}}),[b]),[M,P]=function(t){let{reducer:r=()=>({}),iframeLoaded:n}=t;const o=(0,e.useRef)([]),a=(0,e.useRef)(void 0),u=(0,e.useRef)(void 0),c=(0,e.useRef)(void 0),i=(0,e.useCallback)(((e,t)=>{if(a.current&&n||null!=t&&t.forceSend){var r;const t=null==(r=a.current)?void 0:r.contentWindow;return void(null==t||t.postMessage(e,"*"))}if(!a.current||!u.current||!1===n)return void o.current.push(e);const s=a.current.contentWindow;null==s||s.postMessage(e,"*")}),[n]),l=(0,e.useCallback)((()=>{0!==o.current.length&&!1!==n&&(o.current.forEach((e=>i(e))),o.current=[])}),[i,n]),d=(0,e.useCallback)((e=>{if(c.current&&(c.current(),c.current=void 0),!e)return;const t=()=>{u.current=!0,l(),r({type:"load"},i)};e.addEventListener("load",t),a.current=e,c.current=()=>{e.removeEventListener("load",t)}}),[r,i,l]);return(0,e.useEffect)((()=>{n&&l()}),[n,l]),(0,e.useEffect)((()=>{if(!s())return;const e=e=>{var t;e.source&&e.source!==(null==(t=a.current)?void 0:t.contentWindow)||r({type:"message",payload:e.data},i)};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}}),[r,i]),[d,i]}({reducer:S});e.useImperativeHandle(n,(()=>({postMessage(e){P(e)}})));const j=((e,t)=>e?{}:t)(h,{id:o,className:r(d,u),...a(t),...l(t.a11y)});return e.createElement("div",j,E&&e.createElement("wix-iframe",{"data-src":c},e.createElement("div",{className:f,onMouseEnter:g,onMouseLeave:w},e.createElement("iframe",{ref:M,sandbox:y,className:p,scrolling:m,title:v||x.title,name:"htmlComp-iframe",width:"100%",height:"100%",allow:i,"data-src":c}))))})),controller:(y=e=>{let{stateValues:t,mapperProps:r}=e;return{...r,...v(t)}},{useComponentProps:(e,t,r)=>{const n=(e=>({...e,updateStyles:t=>{const r=Object.entries(t).reduce(((e,[t,r])=>{return{...e,[(n=t,n.startsWith("--")?t:m(t))]:void 0===r?null:r};var n}),{});e.updateStyles(r)}}))(r);return y({mapperProps:e,stateValues:t,controllerUtils:n})}})}}}(),o}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[HtmlComponent].547b12c6.bundle.min.js.map