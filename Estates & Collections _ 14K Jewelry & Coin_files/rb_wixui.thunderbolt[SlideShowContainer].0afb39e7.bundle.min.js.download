!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[SlideShowContainer]",["react","reactDOM"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[SlideShowContainer]"]=t(require("react"),require("react-dom")):e["rb_wixui.thunderbolt[SlideShowContainer]"]=t(e.React,e.ReactDOM)}("undefined"!=typeof self?self:this,(function(e,t){return function(){var n={5329:function(t){"use strict";t.exports=e},95561:function(e){"use strict";e.exports=t},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={exports:{}};return n[e](i,i.exports,o),i.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return function(){"use strict";o.r(i),o.d(i,{components:function(){return We}});var e=o(448),t=o.n(e),n=o(5329),r=o.n(n);function s(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=s(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var a=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=s(e))&&(r&&(r+=" "),r+=t);return r};const l=()=>r().createElement("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},r().createElement("circle",{cx:"12",cy:"12",r:"11.5",fill:"white",stroke:"black"}),r().createElement("path",{d:"M9.11923 17.4383L9.11916 17.4383C8.85811 17.6003 8.5 17.4181 8.5 17.0757V6.92427C8.5 6.58188 8.85811 6.39972 9.11916 6.56171L9.1192 6.56173L17.3014 11.6379L17.3018 11.6382C17.5657 11.8015 17.5666 12.1984 17.3013 12.3631L9.11923 17.4383Z",stroke:"black"})),c=()=>r().createElement("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},r().createElement("circle",{cx:"12",cy:"12",r:"11.5",fill:"white",stroke:"black"}),r().createElement("rect",{x:"9",y:"7",width:"1",height:"10",rx:"0.5",fill:"black"}),r().createElement("rect",{x:"14",y:"7",width:"1",height:"10",rx:"0.5",fill:"black"}));var u="W2bcb7";const d=()=>{};var p=e=>{let{testId:t,className:o,state:i,pauseAriaLabel:s,resumeAriaLabel:p,ariaControls:f,onPause:h=d,onResume:v=d,buttonRef:m}=e;const E="paused"===i,x=(0,n.useMemo)((()=>E?v:h),[E,v,h]);return r().createElement("button",{"data-testid":t,ref:m,className:a(u,o),type:"button","aria-label":E?p:s,"aria-pressed":"playing"===i,"aria-controls":f,onClick:x},E?r().createElement(l,null):r().createElement(c,null))};const f="wixui-",h=(e,...t)=>{const n=[];return e&&n.push(`${f}${e}`),t.forEach((e=>{e&&(n.push(`${f}${e}`),n.push(e))})),n.join(" ")};const v=()=>"function"==typeof window.IntersectionObserver;const m={left:"onSwipeLeft",right:"onSwipeRight",up:"onSwipeUp",down:"onSwipeDown"},E=e=>{if(e.touches&&e.touches.length){const t=e.touches[0];return{x:t.pageX,y:t.pageY}}},x={touched:!0,moved:!1,deltaCoords:{x:0,y:0}},g=(e,t,n)=>()=>{let r;const o=e=>{r={...x,numOfTouches:e.touches.length,startCoords:E(e),startTime:Date.now(),evObj:{...e}}},i=e=>{if(!r)return;const t=E(e);if(t){r.startCoords||(r.startCoords=t);const e=r.startCoords.x-t.x,n=r.startCoords.y-t.y;r.moved=!0,r.deltaCoords={x:e,y:n}}},s=()=>{if(r)if(r.endTime=Date.now(),"onTap"===e&&(e=>e.touched&&!e.moved&&1===e.numOfTouches)(r))t(r.evObj);else if((e=>e.moved&&1===e.numOfTouches&&e.endTime-e.startTime<500&&(Math.abs(e.deltaCoords.x)>100||Math.abs(e.deltaCoords.y)>60))(r)){const n=((e,t)=>{let n;return n=Math.abs(e)>Math.abs(t)?e>0?"left":"right":t>0?"up":"down",n})(r.deltaCoords.x,r.deltaCoords.y);e===m[n]&&t(r.evObj)}};return n&&n.current&&(n.current.addEventListener("touchstart",o,{passive:!0}),n.current.addEventListener("touchmove",i,{passive:!0}),n.current.addEventListener("touchend",s,{passive:!0})),()=>{n&&n.current&&(n.current.removeEventListener("touchstart",o),n.current.removeEventListener("touchmove",i),n.current.removeEventListener("touchend",s))}},b=(e,t,r)=>n.useEffect(g(e,t,r),[e,r,t]),y="Slideshow",C="Next",S="Previous",w="Slides",k="slidesWrapper",N="nextButton",O="prevButton",L="shadowLayer",P="pauseResumeButton",A="NoTransition";var T={root:"slideshow"};var R,M={root:"multi-state-box"},j={thinArrowsSkin:"JMUd8Z","slide-show-container":"mDzRgi",slideShowContainer:"mDzRgi",thinArrowsLargeSelectedCircleSkin:"QodGTM","nav-btn":"XvQ3FE",navBtn:"XvQ3FE","prev-btn":"CdshHv",prevBtn:"CdshHv","next-btn":"Qinjwp",nextBtn:"Qinjwp",slides:"hDJzl4","in-transition":"tAoGqR",inTransition:"tAoGqR","slides-dynamic-height":"T23gn7",slidesDynamicHeight:"T23gn7",shadowLayer:"N2NGoO","dots-nav-section":"lZ5yqT",dotsNavSection:"lZ5yqT","dots-nav-list":"dZxZOk",dotsNavList:"dZxZOk","nav-dot":"ZVUGJp",navDot:"ZVUGJp",selected:"Ale4Rm",playbackControl:"W44q6c",longArrowsLargeSelectedCircleSkin:"ENJ6Ca",squareButtonsSkin:"ghJlnq",StateBoxSkin:"XVHXq_",stateBoxSkin:"XVHXq_"};function D(){return D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},D.apply(null,arguments)}var I,B=function(e){return n.createElement("svg",D({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 21 41"},e),R||(R=n.createElement("path",{d:"M20.3 40.8 0 20.5 20.3.2l.7.7L1.3 20.5 21 40.1z"})))};function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},V.apply(null,arguments)}var H,F;function _(){return _=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_.apply(null,arguments)}const q={thinArrowsSkin:B,thinArrowsLargeSelectedCircleSkin:B,longArrowsLargeSelectedCircleSkin:function(e){return n.createElement("svg",V({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 39 11"},e),I||(I=n.createElement("path",{d:"m33.5 0-.7.7L37.1 5H0v1h37.1l-4.3 4.3.7.7L39 5.5z"})))},squareButtonsSkin:function(e){return n.createElement("svg",_({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 50 50"},e),H||(H=n.createElement("path",{d:"M0 0h50v50H0z"})),F||(F=n.createElement("path",{d:"M28.5 32.4c.1 0 .3-.1.4-.2.2-.2.2-.5 0-.7L22.2 25l6.7-6.5c.2-.2.2-.5 0-.7-.2-.2-.5-.2-.7 0L21 24.7c-.1.1-.2.2-.2.4 0 .1.1.3.2.4l7.1 6.8c.1 0 .3.1.4.1z"})))},StateBoxSkin:()=>null};var Z=e=>{let{skin:t,direction:r,moveToNextSlide:o,moveToPrevSlide:i,translations:s}=e;const l=q[t],c=n.createElement("button",{"data-testid":O,"aria-label":s.prevButtonAriaLabel||S,onClick:i,className:a(j.navBtn,j.prevBtn)},n.createElement(l,null)),u=n.createElement("button",{"data-testid":N,"aria-label":s.nextButtonAriaLabel||C,onClick:o,className:a(j.navBtn,j.nextBtn)},n.createElement(l,null)),d="rtl"===r;return n.createElement(n.Fragment,null,d?[u,c]:[c,u])};var z=e=>{let{translations:t,currentSlideIndex:r,slidesProps:o,isPlaying:i,focusSlideShow:s,changeSlide:l}=e;return n.createElement("nav",{"aria-label":t.navDotsAriaLabel||w,className:j.dotsNavSection},n.createElement("ol",{className:j.dotsNavList},o.map(((e,t)=>((e,t)=>n.createElement("li",{key:e.id,"aria-current":t===r?"true":void 0},n.createElement("a",{href:"./#"+e.id,"aria-label":e.title,onClick:e=>{e.preventDefault(),e.stopPropagation(),t!==r&&l(t),i||s()},className:a(j.navDot,{[j.selected]:t===r})})))(e,t)))))};function U(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}function X(){return X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},X.apply(null,arguments)}function G(e,t){return G=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},G(e,t)}function J(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,G(e,t)}var W=r().createContext(null);function $(e,t){var r=Object.create(null);return e&&n.Children.map(e,(function(e){return e})).forEach((function(e){r[e.key]=function(e){return t&&(0,n.isValidElement)(e)?t(e):e}(e)})),r}function Q(e,t,n){return null!=n[t]?n[t]:e.props[t]}function Y(e,t,r){var o=$(e.children),i=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,o=Object.create(null),i=[];for(var s in e)s in t?i.length&&(o[s]=i,i=[]):i.push(s);var a={};for(var l in t){if(o[l])for(r=0;r<o[l].length;r++){var c=o[l][r];a[o[l][r]]=n(c)}a[l]=n(l)}for(r=0;r<i.length;r++)a[i[r]]=n(i[r]);return a}(t,o);return Object.keys(i).forEach((function(s){var a=i[s];if((0,n.isValidElement)(a)){var l=s in t,c=s in o,u=t[s],d=(0,n.isValidElement)(u)&&!u.props.in;!c||l&&!d?c||!l||d?c&&l&&(0,n.isValidElement)(u)&&(i[s]=(0,n.cloneElement)(a,{onExited:r.bind(null,a),in:u.props.in,exit:Q(a,"exit",e),enter:Q(a,"enter",e)})):i[s]=(0,n.cloneElement)(a,{in:!1}):i[s]=(0,n.cloneElement)(a,{onExited:r.bind(null,a),in:!0,exit:Q(a,"exit",e),enter:Q(a,"enter",e)})}})),i}var K=Object.values||function(e){return Object.keys(e).map((function(t){return e[t]}))},ee=function(e){function t(t,n){var r,o=(r=e.call(this,t,n)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r));return r.state={contextValue:{isMounting:!0},handleExited:o,firstRender:!0},r}J(t,e);var o=t.prototype;return o.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},o.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(e,t){var r,o,i=t.children,s=t.handleExited;return{children:t.firstRender?(r=e,o=s,$(r.children,(function(e){return(0,n.cloneElement)(e,{onExited:o.bind(null,e),in:!0,appear:Q(e,"appear",r),enter:Q(e,"enter",r),exit:Q(e,"exit",r)})}))):Y(e,i,s),firstRender:!1}},o.handleExited=function(e,t){var n=$(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState((function(t){var n=X({},t.children);return delete n[e.key],{children:n}})))},o.render=function(){var e=this.props,t=e.component,n=e.childFactory,o=U(e,["component","childFactory"]),i=this.state.contextValue,s=K(this.state.children).map(n);return delete o.appear,delete o.enter,delete o.exit,null===t?r().createElement(W.Provider,{value:i},s):r().createElement(W.Provider,{value:i},r().createElement(t,o,s))},t}(r().Component);ee.propTypes={},ee.defaultProps={component:"div",childFactory:function(e){return e}};var te=ee;function ne(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}var re=o(95561),oe=o.n(re),ie=!1,se=function(e){return e.scrollTop},ae="unmounted",le="exited",ce="entering",ue="entered",de="exiting",pe=function(e){function t(t,n){var r;r=e.call(this,t,n)||this;var o,i=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?i?(o=le,r.appearStatus=ce):o=ue:o=t.unmountOnExit||t.mountOnEnter?ae:le,r.state={status:o},r.nextCallback=null,r}J(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===ae?{status:le}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==ce&&n!==ue&&(t=ce):n!==ce&&n!==ue||(t=de)}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},n.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===ce){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:oe().findDOMNode(this);n&&se(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===le&&this.setState({status:ae})},n.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,o=this.props.nodeRef?[r]:[oe().findDOMNode(this),r],i=o[0],s=o[1],a=this.getTimeouts(),l=r?a.appear:a.enter;!e&&!n||ie?this.safeSetState({status:ue},(function(){t.props.onEntered(i)})):(this.props.onEnter(i,s),this.safeSetState({status:ce},(function(){t.props.onEntering(i,s),t.onTransitionEnd(l,(function(){t.safeSetState({status:ue},(function(){t.props.onEntered(i,s)}))}))})))},n.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:oe().findDOMNode(this);t&&!ie?(this.props.onExit(r),this.safeSetState({status:de},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:le},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:le},(function(){e.props.onExited(r)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:oe().findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],i=o[0],s=o[1];this.props.addEndListener(i,s)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===ae)return null;var t=this.props,n=t.children,o=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,U(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return r().createElement(W.Provider,{value:null},"function"==typeof n?n(e,o):r().cloneElement(r().Children.only(n),o))},t}(r().Component);function fe(){}pe.contextType=W,pe.propTypes={},pe.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:fe,onEntering:fe,onEntered:fe,onExit:fe,onExiting:fe,onExited:fe},pe.UNMOUNTED=ae,pe.EXITED=le,pe.ENTERING=ce,pe.ENTERED=ue,pe.EXITING=de;var he=pe,ve=function(e,t){return e&&t&&t.split(" ").forEach((function(t){return r=t,void((n=e).classList?n.classList.remove(r):"string"==typeof n.className?n.className=ne(n.className,r):n.setAttribute("class",ne(n.className&&n.className.baseVal||"",r)));var n,r}))},me=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(t=e.call.apply(e,[this].concat(r))||this).appliedClasses={appear:{},enter:{},exit:{}},t.onEnter=function(e,n){var r=t.resolveArguments(e,n),o=r[0],i=r[1];t.removeClasses(o,"exit"),t.addClass(o,i?"appear":"enter","base"),t.props.onEnter&&t.props.onEnter(e,n)},t.onEntering=function(e,n){var r=t.resolveArguments(e,n),o=r[0],i=r[1]?"appear":"enter";t.addClass(o,i,"active"),t.props.onEntering&&t.props.onEntering(e,n)},t.onEntered=function(e,n){var r=t.resolveArguments(e,n),o=r[0],i=r[1]?"appear":"enter";t.removeClasses(o,i),t.addClass(o,i,"done"),t.props.onEntered&&t.props.onEntered(e,n)},t.onExit=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"appear"),t.removeClasses(n,"enter"),t.addClass(n,"exit","base"),t.props.onExit&&t.props.onExit(e)},t.onExiting=function(e){var n=t.resolveArguments(e)[0];t.addClass(n,"exit","active"),t.props.onExiting&&t.props.onExiting(e)},t.onExited=function(e){var n=t.resolveArguments(e)[0];t.removeClasses(n,"exit"),t.addClass(n,"exit","done"),t.props.onExited&&t.props.onExited(e)},t.resolveArguments=function(e,n){return t.props.nodeRef?[t.props.nodeRef.current,e]:[e,n]},t.getClassNames=function(e){var n=t.props.classNames,r="string"==typeof n,o=r?""+(r&&n?n+"-":"")+e:n[e];return{baseClassName:o,activeClassName:r?o+"-active":n[e+"Active"],doneClassName:r?o+"-done":n[e+"Done"]}},t}J(t,e);var n=t.prototype;return n.addClass=function(e,t,n){var r=this.getClassNames(t)[n+"ClassName"],o=this.getClassNames("enter").doneClassName;"appear"===t&&"done"===n&&o&&(r+=" "+o),"active"===n&&e&&se(e),r&&(this.appliedClasses[t][n]=r,function(e,t){e&&t&&t.split(" ").forEach((function(t){return r=t,void((n=e).classList?n.classList.add(r):function(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}(n,r)||("string"==typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)));var n,r}))}(e,r))},n.removeClasses=function(e,t){var n=this.appliedClasses[t],r=n.base,o=n.active,i=n.done;this.appliedClasses[t]={},r&&ve(e,r),o&&ve(e,o),i&&ve(e,i)},n.render=function(){var e=this.props,t=(e.classNames,U(e,["classNames"]));return r().createElement(he,X({},t,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t}(r().Component);me.defaultProps={classNames:""},me.propTypes={};var Ee=me,xe="sNF2R0",ge="hLoBV3",be="Rdf41z",ye="ftlZWo";var Ce="ATGlOr",Se="KQSXD0",we="pagQKE",ke="_6zG5H";var Ne="BB49uC",Oe="j9xE1V",Le="ICs7Rs",Pe="DxijZJ",Ae="B5kjYq",Te="cJijIV",Re="hOxaWM",Me="T9p3fN";var je="qDxYJm",De="aA9V0P",Ie="YPXPAS",Be="Xf2zsA",Ve="y7Kt7s",He="EeUgMu",Fe="fdHrtm",_e="WIFaG4";const qe={CrossFade:e=>{const{reverse:r,...o}=e;return n.createElement(Ee,t()({},o,{classNames:{enter:xe,enterActive:ge,exit:be,exitActive:ye}}),e.children)},OutIn:e=>{const{reverse:r,...o}=e;return n.createElement(Ee,t()({},o,{classNames:{enter:Ce,enterActive:Se,exit:we,exitActive:ke}}),e.children)},SlideHorizontal:e=>{const{reverse:r,...o}=e;return n.createElement(Ee,t()({},o,{classNames:r?{enter:Ae,enterActive:Te,exit:Re,exitActive:Me}:{enter:Ne,enterActive:Oe,exit:Le,exitActive:Pe}}),e.children)},SlideVertical:e=>{const{reverse:r,...o}=e;return n.createElement(Ee,t()({},o,{classNames:r?{enter:Ve,enterActive:He,exit:Fe,exitActive:_e}:{enter:je,enterActive:De,exit:Ie,exitActive:Be}}),e.children)}};var Ze=e=>{const t=qe[e.type],{type:r,...o}=e;return n.createElement(t,o)};const ze=(e,t)=>{let{isPlaying:r,isSlideShowInViewport:o,reverse:i,transition:s,transitionDuration:l,currentSlideIndex:c,onSlideEntered:u,onSlideExited:d,children:p,dynamicHeight:f,inTransition:h}=e;const v=!o||r&&o?"off":"polite",m="SlideVertical"===s||"SlideHorizontal"===s;return"NoTransition"===s?n.createElement("div",{ref:t,"data-testid":k,className:a(j.slides,f?j.slidesDynamicHeight:void 0),"aria-live":v},p):n.createElement("div",{ref:t,"data-testid":k,"aria-live":v,className:a(j.slides,f?j.slidesDynamicHeight:void 0,h&&m?j.inTransition:void 0)},n.createElement(te,{component:null,childFactory:e=>n.cloneElement(e,{reverse:i})},n.createElement(Ze,{type:s,key:c,timeout:l,onEntered:u,onExited:d,unmountOnExit:!0},p)))};var Ue=n.forwardRef(ze);const Xe=(e,o)=>{const{id:i,className:s,customClassNames:l=[],skin:c,hasShadowLayer:u,translations:d,currentSlideIndex:f,slidesProps:m,showNavigationDots:E,showNavigationButton:x,autoPlay:g,initialAutoPlay:C,showPlaybackControl:S,shouldChangeSlidesOnSwipe:w=!0,autoPlayInterval:k,pauseAutoPlayOnMouseOver:N,transition:O,transitionDuration:R,transitionReverse:D,direction:I,changeSlide:B,reducedMotion:V,children:H,onCurrentSlideChanged:F,onChange:_,onMouseEnter:q,onMouseLeave:U,onClick:X,onDblClick:G,play:J,onPlay:W,pause:$,onPause:Q,isPlaying:Y=g&&n.Children.toArray(H()).length>1&&!V,dynamicSlidesHeight:K=!1,observeChildListChange:ee}=e,{isPaused:te,isHovered:ne,setIsHovered:re,isFocused:oe,setIsPaused:ie,setIsFocused:se,onFocus:ae,playbackControlButtonRef:le}=function(e){let{autoplay:t}=e;const[o,i]=(0,n.useState)(!1),[s,a]=(0,n.useState)(!1),[l,c]=(0,n.useState)(!t),u=r().useRef(null);return{isHovered:o,setIsHovered:i,isFocused:s,onFocus:(0,n.useCallback)((e=>{e.nativeEvent.target!==u.current&&a(!0)}),[a,u]),setIsFocused:a,isPaused:l,setIsPaused:c,playbackControlButtonRef:u}}({autoplay:Y});(0,n.useEffect)((()=>{ie(!Y)}),[Y,ie]);const[ce,ue]=n.useState(!1),[de,pe]=n.useState(!1),fe=n.useRef(null),he=!V&&O!==A,ve=n.useMemo((()=>D?!ce:ce),[ce,D]),me=n.Children.toArray(H()),Ee=g&&me.length>1&&!V,xe=n.useRef(null),ge=!te&&!(ne&&Ee&&N)&&!(oe&&Ee),be=n.useCallback((e=>{let{slideIndex:t,isBackward:n,callback:r}=e;if(de||t===f)"function"==typeof r&&r();else{if(he&&"function"==typeof r&&(xe.current=r),he){pe(!0);ue(!!(void 0===n?t<f:n))}B(t),null==_||_({type:"change"}),he||(null==F||F(t),"function"==typeof r&&r())}}),[B,f,he,de,_,F]),ye=n.useCallback((function(e){void 0===e&&(e=void 0);const t=f===me.length-1?0:f+1;return be({slideIndex:t,isBackward:!1,callback:e})}),[me.length,f,be]),Ce=n.useCallback((function(e){void 0===e&&(e=void 0);const t=0===f?me.length-1:f-1;return be({slideIndex:t,isBackward:!0,callback:e})}),[me.length,f,be]),Se=Ee&&N?e=>{re(!0),null==q||q(e)}:q,we=Ee&&N?e=>{re(!1),null==U||U(e)}:U,ke=Ee?{onFocus:ae,onBlur:()=>{se(!1)}}:{},Ne=n.useRef(null),Oe=function(e,t){void 0===t&&(t=!1);const[r,o]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{if(!v())return o(t),()=>{};const n=e.current;if(n&&v()){const e=new window.IntersectionObserver((e=>{const t=e[e.length-1];o(t.isIntersecting)}));return e.observe(n),()=>{e.disconnect()}}return()=>{}}),[e,t]),r}(Ne);b("onSwipeLeft",(()=>w&&ye()),Ne),b("onSwipeRight",(()=>w&&Ce()),Ne),function(e,t){const r=(0,n.useRef)((()=>{}));(0,n.useEffect)((()=>{r.current=e}),[e]),(0,n.useEffect)((()=>{if(null!==t){const e=setInterval((function(){r.current()}),t);return()=>clearInterval(e)}return()=>{}}),[t])}(ye,ge&&Oe?k:null);const Le=n.useCallback((()=>({height:{[i]:()=>{const e=document.getElementById(i);return e?e.clientHeight:0}}})),[i]);n.useImperativeHandle(o,(()=>({play:()=>{J(),null==W||W({type:"autoplayOn"})},pause:()=>{$(),null==Q||Q({type:"autoplayOff"})},moveToSlide:be,next:ye,previous:Ce,getCustomMeasures:Le})),[ye,Ce,be,Le,Q,W,$,J]);const Pe="StateBoxSkin"===c?M.root:T.root;return n.useEffect((()=>{ee&&null!=fe&&fe.current&&ee(i,fe.current)}),[]),n.createElement("div",t()({id:i},(e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{}))(e),{ref:Ne,className:a(s,j.slideShowContainer,j[c],"ignore-focus",h(Pe,...l)),role:"region",tabIndex:-1,"aria-label":d.slideShowAriaLabel||y,onClick:X,onDoubleClick:G,onMouseEnter:Se,onMouseLeave:we},ke),x&&n.createElement(Z,{direction:I,skin:c,translations:d,moveToNextSlide:ye,moveToPrevSlide:Ce}),u&&n.createElement("div",{"data-testid":L,className:j.shadowLayer}),n.createElement(Ue,{ref:fe,isPlaying:ge,isSlideShowInViewport:Oe,reverse:ve,transition:V?A:O,transitionDuration:R,currentSlideIndex:f,inTransition:de,onSlideEntered:()=>{he&&pe(!1)},onSlideExited:()=>{null==F||F(f),xe.current&&(xe.current(),xe.current=null)},dynamicHeight:K},me[f]),E&&n.createElement(z,{focusSlideShow:()=>{var e;return null==(e=Ne.current)?void 0:e.focus()},translations:d,slidesProps:m,currentSlideIndex:f,changeSlide:e=>{be({slideIndex:e})}}),S&&C&&n.createElement(p,{buttonRef:le,testId:P,state:te?"paused":"playing",className:j.playbackControl,pauseAriaLabel:d.pauseControlAriaLabel,resumeAriaLabel:d.playControlAriaLabel,onPause:()=>ie(!0),onResume:()=>ie(!1)}))};const Ge=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`));var Je;const We={SlideShowContainer:{component:n.forwardRef(Xe),controller:(Je=e=>{let{mapperProps:t,controllerUtils:n,stateValues:r}=e;const{updateProps:o}=n,{reducedMotion:i}=r;return{...t,reducedMotion:i,changeSlide:e=>{o({currentSlideIndex:e})},play:()=>{o({isPlaying:!0})},pause:()=>{o({isPlaying:!1})},observeChildListChange:t.isMasterPage?r.observeChildListChangeMaster:r.observeChildListChange}},{useComponentProps:(e,t,n)=>{const r=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(r=t,r.startsWith("--")?t:Ge(t))]:void 0===n?null:n};var r}),{});e.updateStyles(n)}}))(n);return Je({mapperProps:e,stateValues:t,controllerUtils:r})}})}}}(),i}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[SlideShowContainer].0afb39e7.bundle.min.js.map