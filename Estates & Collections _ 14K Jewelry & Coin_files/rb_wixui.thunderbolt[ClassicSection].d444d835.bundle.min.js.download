!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[ClassicSection]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[ClassicSection]"]=t(require("react")):e["rb_wixui.thunderbolt[ClassicSection]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)({}).hasOwnProperty.call(i,a)&&(e[a]=i[a])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},i={};function a(e){var n=i[e];if(void 0!==n)return n.exports;var r=i[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";a.r(n),a.d(n,{components:function(){return di}});var e={};a.r(e),a.d(e,{STATIC_MEDIA_URL:function(){return rt},ph:function(){return it}});var t=a(448),i=a.n(t),r=a(5329),o=a.n(r);function c(e){var t,i,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(i=c(e[t]))&&(a&&(a+=" "),a+=i);else for(t in e)e[t]&&(a&&(a+=" "),a+=t);return a}var s=function(){for(var e,t,i=0,a="";i<arguments.length;)(e=arguments[i++])&&(t=c(e))&&(a&&(a+=" "),a+=t);return a};const l=e=>Object.entries(e).reduce(((e,[t,i])=>(t.includes("data-")&&(e[t]=i),e)),{});const d=13,u=27;function h(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}h(32),h(d),h(u);const g="wixui-",m=(e,...t)=>{const i=[];return e&&i.push(`${g}${e}`),t.forEach((e=>{e&&(i.push(`${g}${e}`),i.push(e))})),i.join(" ")},p="mesh-container-content",_="inline-content",f=e=>o().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),T=(e,t)=>{const{id:a,className:n,wedges:r=[],rotatedComponents:c=[],children:d,fixedComponents:u=[],extraClassName:h="",renderRotatedComponents:g=f}=e,m=o().Children.toArray(d()),T=[],I=[];m.forEach((e=>u.includes(e.props.id)?T.push(e):I.push(e)));const E=(e=>{const{wedges:t,rotatedComponents:i,childrenArray:a,renderRotatedComponents:n}=e,r=i.reduce(((e,t)=>({...e,[t]:!0})),{});return[...a.map((e=>{return r[(t=e,t.props.id.split("__")[0])]?n(e):e;var t})),...t.map((e=>o().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:I,rotatedComponents:c,wedges:r,renderRotatedComponents:g});return o().createElement("div",i()({},l(e),{"data-mesh-id":a+"inlineContent","data-testid":_,className:s(n,h),ref:t}),o().createElement("div",{"data-mesh-id":a+"inlineContent-gridContainer","data-testid":p},E),T)};var I=o().forwardRef(T),E="jhxvbR";const L="v1",A=2,b=1920,y=1920,w=1e3,C=1e3,M={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},O={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},v={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},R={[v.CENTER]:{x:.5,y:.5},[v.TOP_LEFT]:{x:0,y:0},[v.TOP_RIGHT]:{x:1,y:0},[v.TOP]:{x:.5,y:0},[v.BOTTOM_LEFT]:{x:0,y:1},[v.BOTTOM_RIGHT]:{x:1,y:1},[v.BOTTOM]:{x:.5,y:1},[v.RIGHT]:{x:1,y:.5},[v.LEFT]:{x:0,y:.5}},S={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},G={BG:"bg",IMG:"img",SVG:"svg"},N={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},P={classic:1,super:2},F={radius:"0.66",amount:"1.00",threshold:"0.01"},x={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},k=25e6,H=[1.5,2,4],B={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},Y={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},U={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},$={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},z={AVIF:"AVIF",PAVIF:"PAVIF"};$.JPG,$.JPEG,$.JPE,$.PNG,$.GIF,$.WEBP;function j(e,...t){return function(...i){const a=i[i.length-1]||{},n=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?i[t]:a[t];n.push(o,e[r+1])})),n.join("")}}function D(e){return e[e.length-1]}const V=[$.PNG,$.JPEG,$.JPG,$.JPE,$.WIX_ICO_MP,$.WIX_MP,$.WEBP,$.AVIF],Z=[$.JPEG,$.JPG,$.JPE];function W(e,t,i){return i&&t&&!(!(a=t.id)||!a.trim()||"none"===a.toLowerCase())&&Object.values(M).includes(e);var a}function q(e,t,i){return function(e,t,i=!1){return!((X(e)||Q(e))&&t&&!i)}(e,t,i)&&(function(e){return V.includes(ne(e))}(e)||function(e,t=!1){return K(e)&&t}(e,i))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function J(e){return ne(e)===$.PNG}function X(e){return ne(e)===$.WEBP}function K(e){return ne(e)===$.GIF}function Q(e){return ne(e)===$.AVIF}const ee=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),te=["\\.","\\*"],ie="_";function ae(e){return function(e){return Z.includes(ne(e))}(e)?$.JPG:J(e)?$.PNG:X(e)?$.WEBP:K(e)?$.GIF:Q(e)?$.AVIF:$.UNRECOGNIZED}function ne(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function re(e,t,i,a,n){let r;return r=n===O.FILL?function(e,t,i,a){return Math.max(i/e,a/t)}(e,t,i,a):n===O.FIT?function(e,t,i,a){return Math.min(i/e,a/t)}(e,t,i,a):1,r}function oe(e,t,i,a,n,r){e=e||a.width,t=t||a.height;const{scaleFactor:o,width:c,height:s}=function(e,t,i,a,n){let r,o=i,c=a;if(r=re(e,t,i,a,n),n===O.FIT&&(o=e*r,c=t*r),o&&c&&o*c>k){const i=Math.sqrt(k/(o*c));o*=i,c*=i,r=re(e,t,o,c,n)}return{scaleFactor:r,width:o,height:c}}(e,t,a.width*n,a.height*n,i);return function(e,t,i,a,n,r,o){const{optimizedScaleFactor:c,upscaleMethodValue:s,forceUSM:l}=function(e,t,i,a){if("auto"===a)return function(e,t){const i=de(e,t);return{optimizedScaleFactor:B[i].maxUpscale,upscaleMethodValue:P.classic,forceUSM:!1}}(e,t);if("super"===a)return function(e){return{optimizedScaleFactor:D(H),upscaleMethodValue:P.super,forceUSM:!(H.includes(e)||e>D(H))}}(i);return function(e,t){const i=de(e,t);return{optimizedScaleFactor:B[i].maxUpscale,upscaleMethodValue:P.classic,forceUSM:!1}}(e,t)}(e,t,r,n);let d=i,u=a;if(r<=c)return{width:d,height:u,scaleFactor:r,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case O.FILL:d=i*(c/r),u=a*(c/r);break;case O.FIT:d=e*c,u=t*c}return{width:d,height:u,scaleFactor:c,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,c,s,r,o,i)}function ce(e,t,i,a){const n=le(i)||function(e=v.CENTER){return R[e]}(a);return{x:Math.max(0,Math.min(e.width-t.width,n.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,n.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function se(e){return e.alignment&&S[e.alignment]||S[v.CENTER]}function le(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:ue(Math.max(0,Math.min(100,e.x))/100,2),y:ue(Math.max(0,Math.min(100,e.y))/100,2)}),t}function de(e,t){const i=e*t;return i>B[Y.HIGH].size?Y.HIGH:i>B[Y.MEDIUM].size?Y.MEDIUM:i>B[Y.LOW].size?Y.LOW:Y.TINY}function ue(e,t){const i=Math.pow(10,t||0);return(e*i/i).toFixed(t)}function he(e){return e&&e.upscaleMethod&&N[e.upscaleMethod.toUpperCase()]||N.AUTO}function ge(e,t){const i=X(e)||Q(e);return ne(e)===$.GIF||i&&t}const me={isMobile:!1},pe=function(e){return me[e]};function _e(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,i=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&i,me["isMobile"]=e}var e}function fe(e,t){const i={css:{container:{}}},{css:a}=i,{fittingType:n}=e;switch(n){case M.ORIGINAL_SIZE:case M.LEGACY_ORIGINAL_SIZE:case M.LEGACY_STRIP_ORIGINAL_SIZE:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat";break;case M.SCALE_TO_FIT:case M.LEGACY_STRIP_SCALE_TO_FIT:a.container.backgroundSize="contain",a.container.backgroundRepeat="no-repeat";break;case M.STRETCH:a.container.backgroundSize="100% 100%",a.container.backgroundRepeat="no-repeat";break;case M.SCALE_TO_FILL:case M.LEGACY_STRIP_SCALE_TO_FILL:a.container.backgroundSize="cover",a.container.backgroundRepeat="no-repeat";break;case M.TILE_HORIZONTAL:case M.LEGACY_STRIP_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case M.TILE_VERTICAL:case M.LEGACY_STRIP_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case M.TILE:case M.LEGACY_STRIP_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case M.LEGACY_STRIP_FIT_AND_TILE:a.container.backgroundSize="contain",a.container.backgroundRepeat="repeat";break;case M.FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case M.LEGACY_BG_FIT_AND_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case M.LEGACY_BG_NORMAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat"}switch(t.alignment){case v.CENTER:a.container.backgroundPosition="center center";break;case v.LEFT:a.container.backgroundPosition="left center";break;case v.RIGHT:a.container.backgroundPosition="right center";break;case v.TOP:a.container.backgroundPosition="center top";break;case v.BOTTOM:a.container.backgroundPosition="center bottom";break;case v.TOP_RIGHT:a.container.backgroundPosition="right top";break;case v.TOP_LEFT:a.container.backgroundPosition="left top";break;case v.BOTTOM_RIGHT:a.container.backgroundPosition="right bottom";break;case v.BOTTOM_LEFT:a.container.backgroundPosition="left bottom"}return i}const Te={[v.CENTER]:"center",[v.TOP]:"top",[v.TOP_LEFT]:"top left",[v.TOP_RIGHT]:"top right",[v.BOTTOM]:"bottom",[v.BOTTOM_LEFT]:"bottom left",[v.BOTTOM_RIGHT]:"bottom right",[v.LEFT]:"left",[v.RIGHT]:"right"},Ie={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ee(e,t){const i={css:{container:{},img:{}}},{css:a}=i,{fittingType:n}=e,r=t.alignment;switch(a.container.position="relative",n){case M.ORIGINAL_SIZE:case M.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(a.img.width=e.parts[0].width,a.img.height=e.parts[0].height):(a.img.width=e.src.width,a.img.height=e.src.height);break;case M.SCALE_TO_FIT:case M.LEGACY_FIT_WIDTH:case M.LEGACY_FIT_HEIGHT:case M.LEGACY_FULL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="contain",a.img.objectPosition=Te[r]||"unset";break;case M.LEGACY_BG_NORMAL:a.img.width="100%",a.img.height="100%",a.img.objectFit="none",a.img.objectPosition=Te[r]||"unset";break;case M.STRETCH:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="fill";break;case M.SCALE_TO_FILL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="cover"}if("number"==typeof a.img.width&&"number"==typeof a.img.height&&(a.img.width!==t.width||a.img.height!==t.height)){const e=Math.round((t.height-a.img.height)/2),i=Math.round((t.width-a.img.width)/2);Object.assign(a.img,Ie,function(e,t,i){return{[v.TOP_LEFT]:{top:0,left:0},[v.TOP_RIGHT]:{top:0,right:0},[v.TOP]:{top:0,left:t},[v.BOTTOM_LEFT]:{bottom:0,left:0},[v.BOTTOM_RIGHT]:{bottom:0,right:0},[v.BOTTOM]:{bottom:0,left:t},[v.RIGHT]:{top:e,right:0},[v.LEFT]:{top:e,left:0},[v.CENTER]:{width:i.width,height:i.height,objectFit:"none"}}}(e,i,t)[r])}return i}function Le(e,t){const i={css:{container:{}},attr:{container:{},img:{}}},{css:a,attr:n}=i,{fittingType:r}=e,o=t.alignment,{width:c,height:s}=e.src;let l;switch(a.container.position="relative",r){case M.ORIGINAL_SIZE:case M.LEGACY_ORIGINAL_SIZE:case M.TILE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=c,n.img.height=s),n.img.preserveAspectRatio="xMidYMid slice";break;case M.SCALE_TO_FIT:case M.LEGACY_FIT_WIDTH:case M.LEGACY_FIT_HEIGHT:case M.LEGACY_FULL:n.img.width="100%",n.img.height="100%",n.img.transform="",n.img.preserveAspectRatio="";break;case M.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="none";break;case M.SCALE_TO_FILL:q(e.src.id)?(n.img.width=t.width,n.img.height=t.height):(l=function(e,t,i,a,n){const r=re(e,t,i,a,n);return{width:Math.round(e*r),height:Math.round(t*r)}}(c,s,t.width,t.height,O.FILL),n.img.width=l.width,n.img.height=l.height),n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){let e,i,a=0,c=0;r===M.TILE?(e=t.width%n.img.width,i=t.height%n.img.height):(e=t.width-n.img.width,i=t.height-n.img.height);const s=Math.round(e/2),l=Math.round(i/2);switch(o){case v.TOP_LEFT:a=0,c=0;break;case v.TOP:a=s,c=0;break;case v.TOP_RIGHT:a=e,c=0;break;case v.LEFT:a=0,c=l;break;case v.CENTER:a=s,c=l;break;case v.RIGHT:a=e,c=l;break;case v.BOTTOM_LEFT:a=0,c=i;break;case v.BOTTOM:a=s,c=i;break;case v.BOTTOM_RIGHT:a=e,c=i}n.img.x=a,n.img.y=c}return n.container.width=t.width,n.container.height=t.height,n.container.viewBox=[0,0,t.width,t.height].join(" "),i}function Ae(e,t,i){let a;switch(t.crop&&(a=function(e,t){const i=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),a=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return i&&a&&(e.width!==i||e.height!==a)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:i,height:a}:null}(t,t.crop),a&&(e.src.width=a.width,e.src.height=a.height,e.src.isCropped=!0,e.parts.push(ye(a)))),e.fittingType){case M.SCALE_TO_FIT:case M.LEGACY_FIT_WIDTH:case M.LEGACY_FIT_HEIGHT:case M.LEGACY_FULL:case M.FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case M.LEGACY_BG_FIT_AND_TILE_VERTICAL:case M.LEGACY_BG_NORMAL:e.parts.push(be(e,i));break;case M.SCALE_TO_FILL:e.parts.push(function(e,t){const i=oe(e.src.width,e.src.height,O.FILL,t,e.devicePixelRatio,e.upscaleMethod),a=le(e.focalPoint);return{transformType:a?O.FILL_FOCAL:O.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:se(t),focalPointX:a&&a.x,focalPointY:a&&a.y,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}(e,i));break;case M.STRETCH:e.parts.push(function(e,t){const i=re(e.src.width,e.src.height,t.width,t.height,O.FILL),a={...t};return a.width=e.src.width*i,a.height=e.src.height*i,be(e,a)}(e,i));break;case M.TILE_HORIZONTAL:case M.TILE_VERTICAL:case M.TILE:case M.LEGACY_ORIGINAL_SIZE:case M.ORIGINAL_SIZE:a=ce(e.src,i,e.focalPoint,i.alignment),e.src.isCropped?(Object.assign(e.parts[0],a),e.src.width=a.width,e.src.height=a.height):e.parts.push(ye(a));break;case M.LEGACY_STRIP_TILE_HORIZONTAL:case M.LEGACY_STRIP_TILE_VERTICAL:case M.LEGACY_STRIP_TILE:case M.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:O.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:se(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case M.LEGACY_STRIP_SCALE_TO_FIT:case M.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:O.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case M.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:O.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:se(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i))}}function be(e,t){const i=oe(e.src.width,e.src.height,O.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?O.FIT:O.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:S.center,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}function ye(e){return{transformType:O.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function we(e,t){t=t||{},e.quality=function(e,t){const i=e.fileType===$.PNG,a=e.fileType===$.JPG,n=e.fileType===$.WEBP,r=e.fileType===$.AVIF,o=a||i||n||r;if(o){const a=D(e.parts),n=(c=a.width,s=a.height,B[de(c,s)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:n;return r=i?r+5:r,r}var c,s;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,i="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,a="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&i&&a}(t.unsharpMask))return{radius:ue(t.unsharpMask?.radius,2),amount:ue(t.unsharpMask?.amount,2),threshold:ue(t.unsharpMask?.threshold,2)};if(("number"!=typeof(i=(i=t.unsharpMask)||{}).radius||isNaN(i.radius)||0!==i.radius||"number"!=typeof i.amount||isNaN(i.amount)||0!==i.amount||"number"!=typeof i.threshold||isNaN(i.threshold)||0!==i.threshold)&&function(e){const t=D(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===O.FIT}(e))return F;var i;return}(e,t),e.filters=function(e){const t=e.filters||{},i={};Ce(t[U.CONTRAST],-100,100)&&(i[U.CONTRAST]=t[U.CONTRAST]);Ce(t[U.BRIGHTNESS],-100,100)&&(i[U.BRIGHTNESS]=t[U.BRIGHTNESS]);Ce(t[U.SATURATION],-100,100)&&(i[U.SATURATION]=t[U.SATURATION]);Ce(t[U.HUE],-180,180)&&(i[U.HUE]=t[U.HUE]);Ce(t[U.BLUR],0,100)&&(i[U.BLUR]=t[U.BLUR]);return i}(t)}function Ce(e,t,i){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=i}function Me(e,t,i,a){const n=function(e){return e?.isSEOBot??!1}(a),r=ae(t.id),o=function(e,t){const i=/\.([^.]*)$/,a=new RegExp(`(${ee.concat(te).join("|")})`,"g");if(t&&t.length){let e=t;const n=t.match(i);return n&&V.includes(n[1])&&(e=t.replace(i,"")),encodeURIComponent(e).replace(a,ie)}const n=e.match(/\/(.*?)$/);return(n?n[1]:e).replace(i,"")}(t.id,t.name),c=n?1:function(e){return Math.min(e.pixelAspectRatio||1,A)}(i),s=ne(t.id),l=s,d=q(t.id,a?.hasAnimation,a?.allowAnimatedTransform),u={fileName:o,fileExtension:s,fileType:r,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:ge(t.id,a?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:c,quality:0,upscaleMethod:he(a),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:d};return d&&(Ae(u,t,i),we(u,a)),u}function Oe(e,t,i){const a={...i},n=pe("isMobile");switch(e){case M.LEGACY_BG_FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case M.LEGACY_BG_FIT_AND_TILE_VERTICAL:case M.LEGACY_BG_NORMAL:const e=n?w:b,i=n?C:y;a.width=Math.min(e,t.width),a.height=Math.min(i,Math.round(a.width/(t.width/t.height))),a.pixelAspectRatio=1}return a}const ve=j`fit/w_${"width"},h_${"height"}`,Re=j`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Se=j`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,Ge=j`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,Ne=j`crop/w_${"width"},h_${"height"},al_${"alignment"}`,Pe=j`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Fe=j`,lg_${"upscaleMethodValue"}`,xe=j`,q_${"quality"}`,ke=j`,quality_auto`,He=j`,usm_${"radius"}_${"amount"}_${"threshold"}`,Be=j`,bl`,Ye=j`,wm_${"watermark"}`,Ue={[U.CONTRAST]:j`,con_${"contrast"}`,[U.BRIGHTNESS]:j`,br_${"brightness"}`,[U.SATURATION]:j`,sat_${"saturation"}`,[U.HUE]:j`,hue_${"hue"}`,[U.BLUR]:j`,blur_${"blur"}`},$e=j`,enc_auto`,ze=j`,enc_avif`,je=j`,enc_pavif`,De=j`,pstr`;function Ve(e,t,i,a={},n){if(q(t.id,a?.hasAnimation,a?.allowAnimatedTransform)){if(X(t.id)||Q(t.id)){const{alignment:r,...o}=i;t.focalPoint={x:void 0,y:void 0},delete t?.crop,n=Me(e,t,o,a)}else n=n||Me(e,t,i,a);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case O.CROP:t.push(Ge(e));break;case O.LEGACY_CROP:t.push(Ne(e));break;case O.LEGACY_FILL:let i=Pe(e);e.upscale&&(i+=Fe(e)),t.push(i);break;case O.FIT:let a=ve(e);e.upscale&&(a+=Fe(e)),t.push(a);break;case O.FILL:let n=Re(e);e.upscale&&(n+=Fe(e)),t.push(n);break;case O.FILL_FOCAL:let r=Se(e);e.upscale&&(r+=Fe(e)),t.push(r)}}));let i=t.join("/");return e.quality&&(i+=xe(e)),e.unsharpMask&&(i+=He(e.unsharpMask)),e.progressive||(i+=Be(e)),e.watermark&&(i+=Ye(e)),e.filters&&(i+=Object.keys(e.filters).map((t=>Ue[t](e.filters))).join("")),e.fileType!==$.GIF&&(e.encoding===z.AVIF?(i+=ze(e),i+=ke(e)):e.encoding===z.PAVIF?(i+=je(e),i+=ke(e)):e.autoEncode&&(i+=$e(e))),e.src?.isAnimated&&e.transformed&&(i+=De(e)),`${e.src.id}/${L}/${i}/${e.fileName}.${e.preferredExtension}`}(n)}return t.id}const Ze={[v.CENTER]:"50% 50%",[v.TOP_LEFT]:"0% 0%",[v.TOP_RIGHT]:"100% 0%",[v.TOP]:"50% 0%",[v.BOTTOM_LEFT]:"0% 100%",[v.BOTTOM_RIGHT]:"100% 100%",[v.BOTTOM]:"50% 100%",[v.RIGHT]:"100% 50%",[v.LEFT]:"0% 50%"},We=Object.entries(Ze).reduce(((e,[t,i])=>(e[i]=t,e)),{}),qe=[M.TILE,M.TILE_HORIZONTAL,M.TILE_VERTICAL,M.LEGACY_BG_FIT_AND_TILE,M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,M.LEGACY_BG_FIT_AND_TILE_VERTICAL],Je=[M.LEGACY_ORIGINAL_SIZE,M.ORIGINAL_SIZE,M.LEGACY_BG_NORMAL];function Xe(e,t,{width:i,height:a}){return e===M.TILE&&t.width>i&&t.height>a}function Ke(e,{width:t,height:i}){if(!t||!i){const a=t||Math.min(980,e.width),n=a/e.width;return{width:a,height:i||e.height*n}}return{width:t,height:i}}function Qe(e,t,i,a="center"){const n={img:{},container:{}};if(e===M.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return We[t]||""}(t.focalPoint),r=e||a;t.focalPoint&&!e?n.img={objectPosition:et(t,i,t.focalPoint)}:n.img={objectPosition:Ze[r]}}else[M.LEGACY_ORIGINAL_SIZE,M.ORIGINAL_SIZE].includes(e)?n.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:qe.includes(e)&&(n.container={backgroundSize:`${t.width}px ${t.height}px`});return n}function et(e,t,i){const{width:a,height:n}=e,{width:r,height:o}=t,{x:c,y:s}=i;if(!r||!o)return`${c}% ${s}%`;const l=Math.max(r/a,o/n),d=a*l,u=n*l,h=Math.max(0,Math.min(d-r,d*(c/100)-r/2)),g=Math.max(0,Math.min(u-o,u*(s/100)-o/2));return`${h&&Math.floor(h/(d-r)*100)}% ${g&&Math.floor(g/(u-o)*100)}%`}const tt={width:"100%",height:"100%"};function it(e,t,i,a={}){const{autoEncode:n=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:c,allowAnimatedTransform:s,encoding:l}=a;if(!W(e,t,i))return x;const d=void 0===s||s,u=q(t.id,c,d);if(!u||o)return at(e,t,i,{...a,autoEncode:n,useSrcset:u});const h={...i,...Ke(t,i)},{alignment:g,htmlTag:m}=h,p=Xe(e,t,h),_=function(e,t,{width:i,height:a},n=!1){if(n)return{width:i,height:a};const r=!Je.includes(e),o=Xe(e,t,{width:i,height:a}),c=!o&&qe.includes(e),s=c?t.width:i,l=c?t.height:a,d=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(s,J(t.id)):1;return{width:o?1920:s*d,height:l*d}}(e,t,h,r),f=function(e,t,i){return i?0:qe.includes(t)?1:e>200?2:3}(h.width,e,r),T=function(e,t){const i=qe.includes(e)&&!t;return e===M.SCALE_TO_FILL||i?M.SCALE_TO_FIT:e}(e,p),I=Qe(e,t,i,g),{uri:E}=at(T,t,{..._,alignment:g,htmlTag:m},{autoEncode:n,filters:f?{blur:f}:{},hasAnimation:c,allowAnimatedTransform:d,encoding:l}),{attr:L={},css:A}=at(e,t,{...h,alignment:g,htmlTag:m},{});return A.img=A.img||{},A.container=A.container||{},Object.assign(A.img,I.img,tt),Object.assign(A.container,I.container),{uri:E,css:A,attr:L,transformed:!0}}function at(e,t,i,a){let n={};if(W(e,t,i)){const r=Oe(e,t,i),o=Me(e,t,r,a);n.uri=Ve(e,t,r,a,o),a?.useSrcset&&(n.srcset=function(e,t,i,a,n){const r=i.pixelAspectRatio||1;return{dpr:[`${1===r?n.uri:Ve(e,t,{...i,pixelAspectRatio:1},a)} 1x`,`${2===r?n.uri:Ve(e,t,{...i,pixelAspectRatio:2},a)} 2x`]}}(e,t,r,a,n)),Object.assign(n,function(e,t){let i;return i=t.htmlTag===G.BG?fe:t.htmlTag===G.SVG?Le:Ee,i(e,t)}(o,r),{transformed:o.transformed})}else n=x;return n}const nt="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;_e();_e();const rt=nt,{STATIC_MEDIA_URL:ot}=e,ct=({fittingType:e,src:t,target:i,options:a})=>{const n=it(e,t,i,{...a,autoEncode:!0});return n?.uri&&!/^[a-z]+:/.test(n.uri)&&(n.uri=`${ot}${n.uri}`),n},st=/^[a-z]+:/,lt=e=>{const{id:t,containerId:i,uri:a,alt:n,name:o="",role:c,width:s,height:l,displayMode:d,devicePixelRatio:u,quality:h,alignType:g,bgEffectName:m="",focalPoint:p,upscaleMethod:_,className:f="",crop:T,imageStyles:I={},targetWidth:L,targetHeight:A,targetScale:b,onLoad:y=()=>{},onError:w=()=>{},shouldUseLQIP:C,containerWidth:M,containerHeight:O,getPlaceholder:v,isInFirstFold:R,placeholderTransition:S,socialAttrs:G,isSEOBot:N,skipMeasure:P,hasAnimation:F,encoding:x}=e,k=r.useRef(null);let H="";const B="blur"===S,Y=r.useRef(null);if(!Y.current)if(v||C||R||N){const e={upscaleMethod:_,...h||{},shouldLoadHQImage:R,isSEOBot:N,hasAnimation:F,encoding:x};Y.current=(v||ct)({fittingType:d,src:{id:a,width:s,height:l,crop:T,name:o,focalPoint:p},target:{width:M,height:O,alignment:g,htmlTag:"img"},options:e}),H=!Y.current.transformed||R||N?"":"true"}else Y.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const U=!N&&(v||C)&&!R&&Y.current.transformed,$=r.useMemo((()=>JSON.stringify({containerId:i,...i&&{containerId:i},...g&&{alignType:g},...P&&{skipMeasure:!0},displayMode:d,...M&&{targetWidth:M},...O&&{targetHeight:O},...L&&{targetWidth:L},...A&&{targetHeight:A},...b&&{targetScale:b},isLQIP:U,isSEOBot:N,lqipTransition:S,encoding:x,imageData:{width:s,height:l,uri:a,name:o,displayMode:d,hasAnimation:F,...h&&{quality:h},...u&&{devicePixelRatio:u},...p&&{focalPoint:p},...T&&{crop:T},..._&&{upscaleMethod:_}}})),[i,g,P,d,M,O,L,A,b,U,N,S,x,s,l,a,o,F,h,u,p,T,_]),z=Y.current,j=z?.uri,D=z?.srcset,V=z.css?.img,Z=`${E} ${f}`;r.useEffect((()=>{const e=k.current;y&&e?.currentSrc&&e?.complete&&y({target:e})}),[]);const W=z&&!z?.transformed?`max(${s}px, 100%)`:L?`${L}px`:null;return r.createElement("wow-image",{id:t,class:Z,"data-image-info":$,"data-motion-part":`BG_IMG ${i}`,"data-bg-effect-name":m,"data-has-ssr-src":H,"data-animate-blur":!N&&U&&B?"":void 0,style:W?{"--wix-img-max-width":W}:{}},r.createElement("img",{src:j,ref:k,alt:n||"",role:c,style:{...V,...I},onLoad:y,onError:w,width:M||void 0,height:O||void 0,...G,srcSet:R?D?.dpr?.map((e=>st.test(e)?e:`${ot}${e}`)).join(", "):void 0,fetchpriority:R?"high":void 0,loading:!1===R?"lazy":void 0,suppressHydrationWarning:!0}))};var dt="Tj01hh";var ut=e=>{var t,a;const{id:n,alt:o,role:c,className:l,imageStyles:d={},targetWidth:u,targetHeight:h,onLoad:g,onError:m,containerWidth:p,containerHeight:_,isInFirstFold:f,socialAttrs:T,skipMeasure:I,responsiveImageProps:E,zoomedImageResponsiveOverride:L,displayMode:A}=e,b=u||p,y=h||_,{fallbackSrc:w,srcset:C,sources:M,css:O}=E||{},{width:v,height:R,...S}=(null==E||null==(t=E.css)?void 0:t.img)||{},G="original_size"===A?null==E||null==(a=E.css)?void 0:a.img:S;var N;return w&&C&&O?r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,sizes:b+"px",srcSet:I?null==L?void 0:L.srcset:null==E?void 0:E.srcset,id:n,src:w,alt:o||"",role:c,style:{...d,...I?{...null==L||null==(N=L.css)?void 0:N.img}:{...G}},onLoad:g,onError:m,className:s(l,dt),width:b,height:y},T)):w&&M&&O?r.createElement("picture",null,M.map((e=>{let{srcset:t,media:i,sizes:a}=e;return r.createElement("source",{key:i,srcSet:t,media:i,sizes:a})})),r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,id:n,src:M[0].fallbackSrc,alt:o||"",role:c,style:{...d,objectFit:M[0].imgStyle.objectFit,objectPosition:M[0].imgStyle.objectPosition},onLoad:g,onError:m,className:s(l,dt),width:b,height:y},T))):r.createElement(lt,e)};var ht=e=>{var t,i,a;const{className:n,customIdPrefix:o,getPlaceholder:c,hasAnimation:s,...l}=e,d=r.useMemo((()=>JSON.stringify({containerId:l.containerId,alignType:l.alignType,fittingType:l.displayMode,hasAnimation:s,imageData:{width:l.width,height:l.height,uri:l.uri,name:l.name,...l.quality&&{quality:l.quality},displayMode:l.displayMode}})),[l,s]),u=r.useRef(null);u.current||(u.current=c?c({fittingType:l.displayMode,src:{id:l.uri,width:l.width,height:l.height,name:l.name},target:{width:l.containerWidth,height:l.containerHeight,alignment:l.alignType,htmlTag:"bg"},options:{hasAnimation:s,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const h=u.current,g=null!=(t=null==h?void 0:h.uri)?t:"",m=null!=(i=null==(a=h.css)?void 0:a.container)?i:{},p=Object.assign(g?{backgroundImage:"url("+g+")"}:{},m);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+l.containerId,class:n,style:p,"data-tiled-image-info":d,"data-has-bg-scroll-effect":l.hasBgScrollEffect||"","data-bg-effect-name":l.bgEffectName||"","data-motion-part":"BG_IMG "+l.containerId})};const gt=new RegExp("<%= compId %>","g"),mt=(e,t)=>e.replace(gt,t);var pt=e=>null==e?void 0:e.replace(":hover",""),_t="bX9O_S",ft="Z_wCwr",Tt="Jxk_UL",It="K8MSra",Et="YTb3b4";const Lt={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var At=e=>{const{id:t,videoRef:a,videoInfo:n,posterImageInfo:o,muted:c,preload:l,loop:d,alt:u,isVideoEnabled:h,getPlaceholder:g,extraClassName:m=""}=e;n.containerId=pt(n.containerId);const p=r.useMemo((()=>JSON.stringify(n)),[n]),_=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+n.containerId,className:Et},r.createElement("defs",{dangerouslySetInnerHTML:{__html:mt(o.filterEffectSvgString,n.containerId)}})),r.createElement(ut,i()({key:n.videoId+"_img",id:o.containerId+"_img",className:s(ft,Tt,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,Lt,{getPlaceholder:g})));return h?r.createElement("wix-video",{id:t,"data-video-info":p,"data-motion-part":"BG_IMG "+n.containerId,class:s(_t,"bgVideo",m)},r.createElement("video",{key:n.videoId+"_video",ref:a,id:n.containerId+"_video",className:It,crossOrigin:"anonymous","aria-label":u,playsInline:!0,preload:l,muted:c,loop:d}),_):_},bt="SUz0WK";var yt=e=>{const{id:t,containerId:i,pageId:a,children:n,bgEffectName:o="",containerSize:c}=e;return r.createElement("wix-bg-media",{id:t,class:bt,"data-container-id":i,"data-container-size":((null==c?void 0:c.width)||0)+", "+((null==c?void 0:c.height)||0),"data-page-id":a,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+i},n)};const wt="bgOverlay";var Ct="m4khSP",Mt="FNxOn5";var Ot=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":wt,className:Ct},t&&r.createElement(ht,i()({customIdPrefix:"bgImgOverlay_",className:Mt},t)))};const vt="bgLayers",Rt="colorUnderlay",St="mediaPadding",Gt="canvas";var Nt="MW5IWV",Pt="N3eg0s",Ft="Kv1aVt",xt="dLPlxY",kt="VgO9Yg",Ht="LWbAav",Bt="yK6aSC",Yt="K_YxMd",Ut="NGjcJN",$t="mNGsUM",zt="I8xA4L";const jt="bgImage";var Dt=e=>{const{videoRef:t,canvasRef:a,hasBgFullscreenScrollEffect:n,image:o,backgroundImage:c,backgroundMedia:l,video:d,backgroundOverlay:u,shouldPadMedia:h,extraClass:g="",shouldRenderUnderlay:m=!d,reducedMotion:p=!1,getPlaceholder:_,hasCanvasAnimation:f,useWixMediaCanvas:T,onClick:I}=e,{onImageLoad:E}=(e=>{let{onReady:t,image:i}=e;return(0,r.useEffect)((()=>{t&&!i&&t()}),[t,i]),{onImageLoad:e=>{null!=i&&i.onLoad&&i.onLoad(e),t&&t()}}})(e),L=pt(e.containerId),A="img_"+pt(L),b=o&&r.createElement(ut,i()({id:A,className:s(Ft,xt,$t,jt),imageStyles:{width:"100%",height:"100%"},getPlaceholder:_},o,{onLoad:E})),y=c&&r.createElement(ht,i()({},c,{containerId:L,className:s(Ft,xt,$t,jt),getPlaceholder:_})),w=d&&r.createElement(At,i()({id:"videoContainer_"+L},d,{extraClassName:Bt,reducedMotion:p,videoRef:t,getPlaceholder:_})),C=T&&a||f?r.createElement("wix-media-canvas",{"data-container-id":L,class:f?zt:""},b,y,w,r.createElement("canvas",{id:L+"webglcanvas",className:s(Yt,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Gt})):r.createElement(r.Fragment,null,b,y,w,a&&r.createElement("canvas",{id:L+"webglcanvas",ref:a,className:s(Yt,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Gt})),M=l?r.createElement(yt,i()({id:"bgMedia_"+L},l),C):r.createElement("div",{id:"bgMedia_"+L,"data-motion-part":"BG_MEDIA "+L,className:kt},C),O=u&&r.createElement(Ot,u);return r.createElement("div",{id:vt+"_"+L,"data-hook":vt,"data-motion-part":"BG_LAYER "+L,className:s(Nt,g,{[Pt]:n}),onClick:I},m&&r.createElement("div",{"data-testid":Rt,className:s(Ht,Ft)}),h?r.createElement("div",{"data-testid":St,className:Ut},M,O):r.createElement(r.Fragment,null,M,O))},Vt="dkukWC",Zt="FRCqDF",Wt="xnZvZH",qt="MBOSCN";const Jt=(e,t,i)=>{const a=((e,t)=>e?[...Array(1+(t||0)).keys()].reverse().map((e=>r.createElement("div",{key:"divider-layer-"+e,style:{"--divider-layer-i":e},className:Zt,"data-testid":"divider-layer-"+e,"data-divider-layer":e}))):null)(!!t,i);return t?r.createElement("div",{className:s(Vt,{[Wt]:"top"===e,[qt]:"bottom"===e}),"data-testid":e+"-divider"},a):null};var Xt=e=>{var t,i;const a=r.useMemo((()=>{var t;return Jt("top",null==e?void 0:e.hasTopDivider,null==e||null==(t=e.topLayers)?void 0:t.size)}),[null==e?void 0:e.hasTopDivider,null==e||null==(t=e.topLayers)?void 0:t.size]),n=r.useMemo((()=>{var t;return Jt("bottom",null==e?void 0:e.hasBottomDivider,null==e||null==(t=e.bottomLayers)?void 0:t.size)}),[null==e?void 0:e.hasBottomDivider,null==e||null==(i=e.bottomLayers)?void 0:i.size]);return r.createElement(r.Fragment,null,a,n)};var Kt={root:"section"},Qt="Oqnisf",ei="cM88eO",ti="YtfWHd",ii="mj3xJ8";const ai=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M7.5,5 C8.32842712,5 9,5.67157288 9,6.5 L9,11.5 C9,12.3284271 8.32842712,13 7.5,13 C6.67157288,13 6,12.3284271 6,11.5 L6,6.5 C6,5.67157288 6.67157288,5 7.5,5 Z M11.5,5 C12.3284271,5 13,5.67157288 13,6.5 L13,11.5 C13,12.3284271 12.3284271,13 11.5,13 C10.6715729,13 10,12.3284271 10,11.5 L10,6.5 C10,5.67157288 10.6715729,5 11.5,5 Z M7.5,6 C7.22385763,6 7,6.22385763 7,6.5 L7,11.5 C7,11.7761424 7.22385763,12 7.5,12 C7.77614237,12 8,11.7761424 8,11.5 L8,6.5 C8,6.22385763 7.77614237,6 7.5,6 Z M11.5,6 C11.2238576,6 11,6.22385763 11,6.5 L11,11.5 C11,11.7761424 11.2238576,12 11.5,12 C11.7761424,12 12,11.7761424 12,11.5 L12,6.5 C12,6.22385763 11.7761424,6 11.5,6 Z"}));ai.displayName="PauseSmall";var ni=ai;const ri=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M6.87468837,5.45041947 L12.7318793,8.46657119 C13.20163,8.68731241 13.20163,9.26940918 12.7318793,9.53342881 L6.87468837,12.5495805 C6.58008377,12.7012867 6.00070071,12.5801226 6,12.0161517 L6,5.98384828 C6,5.65247743 6.35266876,5.20682168 6.87468837,5.45041947 Z M7,11.3602529 L11.5834735,9 L7,6.63974714 L7,11.3602529 Z"}));ri.displayName="PlaySmall";var oi=ri;const ci=(e,t)=>{var a;const{id:n,fillLayers:c={},className:d,customClassNames:u=[],meshProps:h={wedges:[],rotatedComponents:[]},anchorUrlFragment:g,children:p,onMouseEnter:_,onMouseLeave:f,onClick:T,onDblClick:E,getPlaceholder:L,a11y:A={},onStop:b,onReady:y,dividers:w,lang:C,translations:M,isPlayPauseSectionExperimentOn:O}=e,v={onMouseEnter:_,onMouseLeave:f,onClick:T,onDoubleClick:E},R=c.hasBgFullscreenScrollEffect,S=!!c.video,G=function(e,t,i){const a=o().useRef(null),n=o().useRef(null);return t?n.current||(n.current={play:()=>a.current?.play(),load:()=>a.current?.load(),pause:()=>a.current?.pause(),stop:()=>{a.current&&(a.current.pause(),a.current.currentTime=0,i&&i(a.current))}}):n.current=null,o().useImperativeHandle(e,(()=>n.current||{load(){},stop(){}})),a}(t,S,b),{tabindex:N,...P}=A,[F,x]=r.useState(!(null!=G&&null!=(a=G.current)&&a.paused)),k=()=>{const e=null==G?void 0:G.current;e&&(e.paused?e.play():e.pause(),x((t=>{const i=!t,a=document.getElementById(n);if(a){a.querySelectorAll("video").forEach((t=>{t!==e&&(i?t.play():t.pause())}))}return i})))};return r.createElement("section",i()({id:n},l(e),v,P,((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(A),{className:s(Qt,d,m(Kt.root,...u)),"data-block-level-container":"ClassicSection",lang:C}),g&&r.createElement("div",{className:ti,id:g}),S&&O&&r.createElement("button",{className:ii,onClick:k,"aria-label":M.ariaLabel,"aria-pressed":F},F?r.createElement(ni,null):r.createElement(oi,null)),r.createElement(Dt,i()({},c,{onReady:y,getPlaceholder:L,videoRef:G,onClick:k})),w&&r.createElement(Xt,w),r.createElement(I,i()({id:n},h,{extraClassName:s({[ei]:R})}),p))};const si=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`));var li;const di={ClassicSection:{component:r.forwardRef(ci),controller:(li=e=>{let{mapperProps:t,stateValues:i}=e;const{experiments:a={}}=i,n=((e,t)=>!0===e[t]||"true"===e[t]||"new"===e[t])(a,"specs.thunderbolt.playPauseSection");return{...t,isPlayPauseSectionExperimentOn:n}},{useComponentProps:(e,t,i)=>{const a=(e=>({...e,updateStyles:t=>{const i=Object.entries(t).reduce(((e,[t,i])=>{return{...e,[(a=t,a.startsWith("--")?t:si(t))]:void 0===i?null:i};var a}),{});e.updateStyles(i)}}))(i);return li({mapperProps:e,stateValues:t,controllerUtils:a})}})}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[ClassicSection].d444d835.bundle.min.js.map