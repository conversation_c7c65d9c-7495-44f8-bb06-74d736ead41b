!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[MeshGroup]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[MeshGroup]"]=t(require("react")):e["rb_wixui.thunderbolt[MeshGroup]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={exports:{}};return t[e](i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return function(){"use strict";n.r(o),n.d(o,{components:function(){return m}});var e=n(448),t=n.n(e);function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n);else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}var i=function(){for(var e,t,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o},a=n(5329),s=n.n(a);const d=e=>Object.entries(e).reduce(((e,[t,r])=>(t.includes("data-")&&(e[t]=r),e)),{});const u="mesh-container-content",c="inline-content",p=e=>s().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),f=(e,r)=>{const{id:n,className:o,wedges:a=[],rotatedComponents:f=[],children:l,fixedComponents:m=[],extraClassName:h="",renderRotatedComponents:y=p}=e,b=s().Children.toArray(l()),v=[],x=[];b.forEach((e=>m.includes(e.props.id)?v.push(e):x.push(e)));const g=(e=>{const{wedges:t,rotatedComponents:r,childrenArray:n,renderRotatedComponents:o}=e,i=r.reduce(((e,t)=>({...e,[t]:!0})),{});return[...n.map((e=>{return i[(t=e,t.props.id.split("__")[0])]?o(e):e;var t})),...t.map((e=>s().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:x,rotatedComponents:f,wedges:a,renderRotatedComponents:y});return s().createElement("div",t()({},d(e),{"data-mesh-id":n+"inlineContent","data-testid":c,className:i(o,h),ref:r}),s().createElement("div",{"data-mesh-id":n+"inlineContent-gridContainer","data-testid":u},g),v)};var l=s().forwardRef(f);const m={MeshGroup:{component:e=>{const{id:r,meshProps:n,children:o,className:s,carmiClassName:u=""}=e;return a.createElement("div",t()({id:r,className:i(s,u)},d(e)),a.createElement(l,t()({id:r},n),o))}}}}(),o}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[MeshGroup].f88c99ed.bundle.min.js.map