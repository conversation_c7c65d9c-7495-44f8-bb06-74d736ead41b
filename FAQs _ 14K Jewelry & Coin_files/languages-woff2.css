@font-face {font-family: "Helvetica-W01-Roman"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/4021a3b9-f782-438b-aeb4-c008109a8b64.woff") format("woff");}
@font-face {font-family: "Helvetica-W01-Bold"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/c5749443-93da-4592-b794-42f28d62ef72.woff") format("woff");}
@font-face {font-family: "Braggadocio-W01"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/518e4577-eecc-4ecd-adb4-2ee21df35b20.woff") format("woff");}
@font-face {font-family: "Clarendon-W01-Medium-692107"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/b6878f57-4d64-4d70-926d-fa4dec6173a5.woff") format("woff");}
@font-face {font-family: "DIN-Next-W01-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/bc176270-17fa-4c78-a343-9fe52824e501.woff") format("woff");}
@font-face {font-family: "SnellRoundhandW01-Scrip"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/efbfc170-aaf0-4472-91f4-dbb5bc2f4c59.woff") format("woff");}
@font-face {font-family: "Stencil-W01-Bold"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/a9eddc47-990d-47a3-be4e-c8cdec0090c6.woff") format("woff");}
@font-face {font-family: "Helvetica-W01-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/03805817-4611-4dbc-8c65-0f73031c3973.woff") format("woff");}
@font-face {font-family: "Victoria-Titling-MT-W90"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/faceff42-b106-448b-b4cf-5b3a02ad61f1.woff") format("woff");}
@font-face {font-family: "AmericanTypwrterITCW01--731025"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/0c0f4d28-4c13-4e84-9a36-e63cd529ae86.woff") format("woff");}
@font-face {font-family: "Soho-W01-Thin-Condensed"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/f84b539d-ed34-4400-a139-c0f909af49aa.woff") format("woff");}
@font-face {font-family: "Pacifica-W00-Condensed"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/6849614c-986c-45b1-a1a7-39c891759bb9.woff") format("woff");}
@font-face {font-family: "Avenida-W01"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/53f05821-c783-4593-bf20-c3d770f32863.woff") format("woff");}
@font-face {font-family: "ITC-Arecibo-W01-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/5d6cd606-b520-4335-96e1-755691d666e8.woff") format("woff");}
@font-face {font-family: "Droid-Serif-W01-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/83ae2051-dcdd-4931-9946-8be747a40d00.woff") format("woff");}
@font-face {font-family: "Museo-W01-700"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/28d74e9b-4ea9-4e3c-b265-c67a72c66856.woff") format("woff");}
@font-face {font-family: "Museo-Slab-W01-100"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/cacc0862-f146-4746-92b1-60e6114a66c4.woff") format("woff");}
@font-face {font-family: "Geotica-W01-Four-Open"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/cc0b2292-9358-41ee-b3b9-429952586f69.woff") format("woff");}
@font-face {font-family: "Marzo-W00-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/e947b76a-edcf-4519-bc3d-c2da35865717.woff") format("woff");}
@font-face {font-family: "ReklameScriptW00-Medium"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/05b176f5-c622-4c35-af98-c0c056dd5b66.woff") format("woff");}
@font-face {font-family: "Nimbus-Sans-TW01Con"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/8fb1090e-b4d0-4685-ac8f-3d0c29d60130.woff") format("woff");}
@font-face {font-family: "Bodoni-W01-Poster"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/4d1b9848-7ebd-472c-9d31-4af0aa7faaea.woff") format("woff");}
@font-face {font-family: "Comic-Sans-W01-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/234c98b8-36ae-45ab-8a55-77980708b2bc.woff") format("woff");}
@font-face {font-family: "Courier-PS-W01"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/b059d02a-a222-4c63-9fd3-705eaeea1c16.woff") format("woff");}
@font-face {font-family: "Impact-W01-2010"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/4cefdf47-0136-4169-9933-3225dbbec9d9.woff") format("woff");}
@font-face {font-family: "Lucida-Console-W01"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/60f4a13f-3943-432a-bb51-b612e41239c5.woff") format("woff");}
@font-face {font-family: "Tahoma-W01-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/20323430-24f4-4767-9d4d-060d1e89758a.woff") format("woff");}
@font-face {font-family: "Arial-W01-Black"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/c6f5bcd6-66fc-44af-be95-bb1f2b38d080.woff") format("woff");}
@font-face {font-family: "Tahoma-W15--Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/ae844b11-5158-4caf-90b4-7ace49ac3440.woff") format("woff");}
@font-face {font-family: "Tahoma-W99-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/d3bbaa1b-d5e3-431f-93a7-9cea63601bb6.woff") format("woff");}
@font-face {font-family: "Coquette-W00-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/4e5374b3-a214-41e5-81f0-a34c9292da7e.woff") format("woff");}
@font-face {font-family: "Rosewood-W01-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/4d9bc879-ab51-45da-bf37-c9710cd1cc32.woff") format("woff");}
@font-face {font-family: "segoe_printregular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/segoe_print-webfont.woff") format("woff");}
@font-face {font-family: 'Open Sans'; font-style: normal; font-weight: 400; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/opensans-regular-webfont.woff") format("woff");}
@font-face {font-family: 'Open Sans'; font-style: normal; font-weight: 700; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/opensans-bold-webfont.woff") format("woff");}
@font-face {font-family: 'Open Sans'; font-style: italic; font-weight: 400; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/opensans-italic-webfont.woff") format("woff");}
@font-face {font-family: 'Open Sans'; font-style: italic; font-weight: 700; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/opensans-bolditalic-webfont.woff") format("woff");}
@font-face {font-family: "Avenir-LT-W01_35-Light1475496"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/0078f486-8e52-42c0-ad81-3c8d3d43f48e.woff2") format("woff2");}
@font-face {font-family: "Avenir-LT-W01_85-Heavy1475544"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/d513e15e-8f35-4129-ad05-481815e52625.woff2") format("woff2");}
@font-face {font-family: "BaskervilleMTW01-SmBdIt"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/c887df8e-b6c3-4c97-85b8-91cfdde77b07.woff2") format("woff2");}
@font-face {font-family: "Belinda-W00-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/242487aa-209a-4dbd-aca2-64a3c73a8946.woff2") format("woff2");}
@font-face {font-family: "Brandon-Grot-W01-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/69b40392-453a-438a-a121-a49e5fbc9213.woff2") format("woff2");}
@font-face {font-family: "Bree-W01-Thin-Oblique"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/ceb3b4a3-0083-44ae-95cb-e362f95cc91b.woff2") format("woff2");}
@font-face {font-family: "Adobe-Caslon-W01-SmBd"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/becfadb1-eaca-4817-afbd-fe4d61e1f661.woff2") format("woff2");}
@font-face {font-family: "Didot-W01-Italic"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/abe3d3a9-c990-459f-9407-54ac96cd2f00.woff2") format("woff2");}
@font-face {font-family: "Futura-LT-W01-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/26091050-06ef-4fd5-b199-21b27c0ed85e.woff2") format("woff2");}
@font-face {font-family: "Futura-LT-W01-Book"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/8bf38806-3423-4080-b38f-d08542f7e4ac.woff2") format("woff2");}
@font-face {font-family: "Kepler-W03-Light-SCd-Cp"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/b2b1472c-55f2-478a-a9c9-9373214a27e5.woff2") format("woff2");}
@font-face {font-family: "Lulo-Clean-W01-One-Bold"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/aee74cb3-c913-4b54-9722-6001c92325f2.woff2") format("woff2");}
@font-face {font-family: "Proxima-N-W01-Reg"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/64017d81-9430-4cba-8219-8f5cc28b923e.woff2") format("woff2");}
@font-face {font-family: "Trend-Sans-W00-Four"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/29c66f1e-5243-4f34-8a19-47405f72954c.woff2") format("woff2");}
@font-face {font-family: "DINNeuzeitGroteskLTW01-_812426"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/5cee8d6e-89ad-4d8c-a0ac-584d316b15ae.woff2") format("woff2");}
@font-face {font-family: "Peaches-and-Cream-Regular-W00"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/3c5beda8-45cc-4f76-abca-8eccfeb6220c.woff2") format("woff2");}
@font-face {font-family: "Helvetica-W02-Roman"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/b56b944e-bbe0-4450-a241-de2125d3e682.woff") format("woff");}
@font-face {font-family: "Helvetica-W02-Bold"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/192dac76-a6d9-413d-bb74-22308f2e0cc5.woff") format("woff");}
@font-face {font-family: "Clarendon-W02-Medium-693834"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/01681361-4a95-4651-a6c8-4005d0fc4a79.woff") format("woff");}
@font-face {font-family: "DIN-Next-W02-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/07d62b21-8d7a-4c36-be86-d32ab1089972.woff") format("woff");}
@font-face {font-family: "AmericanTypwrterITCW02--737091"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/4e5713c6-f9bf-44d7-bc17-775b7c102f1c.woff") format("woff");}
@font-face {font-family: "Helvetica-W02-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/80c34ad2-27c2-4d99-90fa-985fd64ab81a.woff") format("woff");}
@font-face {font-family: "Soho-W02-Thin-Condensed"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/68eb3cfd-be6c-4f9e-8ca4-e13ce8d29329.woff") format("woff");}
@font-face {font-family: "Avenida-W02"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/582278da-0505-4fbe-9102-2b529c7c973a.woff") format("woff");}
@font-face {font-family: "Droid-Serif-W02-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/764779cf-076d-427a-87b4-136ccc83fba0.woff") format("woff");}
@font-face {font-family: "Comic-Sans-W02-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/301e2ea2-8153-453c-9051-0a729098e682.woff") format("woff");}
@font-face {font-family: "Courier-PS-W02"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/bcc470b9-5a9b-45e9-bf60-6daca06bc70e.woff") format("woff");}
@font-face {font-family: "Impact-W02-2010"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/eb1185bb-8f9d-4855-83fa-d06f0efef677.woff") format("woff");}
@font-face {font-family: "Lucida-Console-W02"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/3e5b24ea-4345-4830-8c7d-0e7ef26b4e63.woff") format("woff");}
@font-face {font-family: "Tahoma-W02-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/94e45703-fbd7-46e5-9fcd-228ae59d6266.woff") format("woff");}
@font-face {font-family: "Rosewood-W08-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/62a23651-230c-4724-b2c0-087544ed1a27.woff") format("woff");}
@font-face {font-family: "Arial-W02-Black"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/41280d6d-9240-4d82-9e98-3ea1a1913501.woff") format("woff");}
@font-face {font-family: "DIN-Next-W10-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/a9e95a29-98a7-404a-90ee-1929ad09c696.woff") format("woff");}
@font-face {font-family: "Helvetica-LT-W10-Bold"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/0a3939d0-3833-4db3-8b85-f64c2b3350d2.woff") format("woff");}
@font-face {font-family: "Helvetica-LT-W10-Roman"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/6f8d1983-4d34-4fa4-9110-988f6c495757.woff") format("woff");}
@font-face {font-family: "Bodoni-Poster-W10"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/e04da7b7-ccbf-4cbf-b19a-947551d17de6.woff") format("woff");}
@font-face {font-family: "Droid-Serif-W10-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/571d67cb-de3d-41af-8c0a-06a53d490466.woff") format("woff");}
@font-face {font-family: "Comic-Sans-W10-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/73381861-eb6a-4f7c-8c14-cd34a714f943.woff") format("woff");}
@font-face {font-family: "Courier-PS-W10"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/2593bfe2-2f34-4218-a1e2-fde3bdc686e1.woff") format("woff");}
@font-face {font-family: "Impact-W10-2010"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/1c7b5ef1-5b09-4473-8003-a974846653a7.woff") format("woff");}
@font-face {font-family: "Lucida-Console-W10-0"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/2c0bffef-a666-4646-a4bc-7faf1fa689f5.woff") format("woff");}
@font-face {font-family: "Tahoma-W10-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/9ee00678-b6d7-4b4f-8448-70cfa267d36b.woff") format("woff");}
@font-face {font-family: "Arial-W10-Black"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/7cfb7eb0-2332-4048-a7f4-2c3fa389c3a3.woff") format("woff");}
@font-face {font-family: "FBBlueGothicL"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/1a10c1c0-157a-4f57-96c1-1af2fc242e06.woff") format("woff");}
@font-face {font-family: "FBChamBlue"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/75c0e570-e4e0-4e86-a031-1ade01e5b3f5.woff") format("woff");}
@font-face {font-family: "FBGreen"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/399c1f00-ff31-4f87-868c-bcbfcabcdd51.woff") format("woff");}
@font-face {font-family: "FBNeoGothic"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/c52a9d59-984f-45b4-bfd7-6f6af54eb89f.woff") format("woff");}
@font-face {font-family: "FBPlum"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/101fd386-ed60-4ed9-8ac2-80d0492347ac.woff") format("woff");}
@font-face {font-family: "NanumGothic-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/nanum-gothic-regular.woff") format("woff");}
@font-face {font-family: "BM-HANNA"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/bm-hanna.woff") format("woff");}
@font-face {font-family: "AhmedLTW20-OutlineRegul"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/bc9495bd-5edc-4c5b-be28-dfb45e27e688.woff") format("woff");}
@font-face {font-family: "Arian-LT-W20-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/c977bad6-94c3-457c-9771-d8e0017a33c2.woff") format("woff");}
@font-face {font-family: "Arian-LT-W20-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/c257a373-9919-458c-b7b2-83850775058d.woff") format("woff");}
@font-face {font-family: "Janna-LT-W20-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/26c24286-5aab-4747-81b9-54330e77fb14.woff") format("woff");}
@font-face {font-family: "Kufi-LT-W20-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/e0e311dc-5674-493c-8c19-f0a0a1422837.woff") format("woff");}
@font-face {font-family: "HelveticaNeueLTW20-Ligh"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/b8ee7e47-48e4-4b5b-8a74-cf02708fb54a.woff") format("woff");}
@font-face {font-family: "Midan-W20"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/be87d34b-77db-4286-87d9-d2964115c6c5.woff") format("woff");}
@font-face {font-family: "TanseekModernW20-Light"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/688d77ff-8c0d-4baf-ac95-f45c034e1caf.woff") format("woff");}
@font-face {font-family: "DINNextLTW23-UltraLight"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/63b0b154-64e6-4846-be80-b601f3ce9b28.woff") format("woff");}
@font-face {font-family: "ArabicTypesettingW23-Re"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/5a32e87e-0f32-4971-a43f-4ec453bc74ca.woff") format("woff");}
@font-face {font-family: "CoHeadlineW23-ArabicBol"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/b15a6899-c706-46a9-8c2b-a80b62ba301b.woff") format("woff");}
@font-face {font-family: 'Amiri'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/amiri-regular.woff") format("woff");}
@font-face {font-family: 'Droid-Naskh'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/droidnaskh-regular.woff") format("woff");}
@font-face {font-family: "Adler-W26-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/fcb3f76f-a112-479e-ab7f-ab1c2be906c9.woff") format("woff");}
@font-face {font-family: "Frank-Ruhl-W26-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/0e834425-e268-4b38-b5a8-f24b8632d6ae.woff") format("woff");}
@font-face {font-family: "Haim-Arukeem-W26-Medium"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/f70c24b0-d6be-4d04-99cd-46efc41d00b4.woff") format("woff");}
@font-face {font-family: "Miriam-W26-Medium"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/21aeb0a3-3309-4415-818b-36f94e2a1a3a.woff") format("woff");}
@font-face {font-family: "Nekudot-W26-Bold"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/c28b65cd-9544-42f1-9ffc-d6ffa544e6fb.woff") format("woff");}
@font-face {font-family: "Gulash-W26-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/64f53eeb-1d5e-493c-aa3b-aa8e2c066320.woff") format("woff");}
@font-face {font-family: "Shabazi-W26-Bold"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/09048cb1-f6a6-4b44-9d96-6d20013ef7e8.woff") format("woff");}
@font-face {font-family: "Chips-W26-Normal"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/e526922d-4fe2-4e4d-834d-6b62ebd244da.woff") format("woff");}
@font-face {font-family: "Alef-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/alef-regular.woff") format("woff");}
@font-face {font-family: "OpenSansHebrewCondensed-Regular"; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/opensans-hebrew-condensed-regular.woff") format("woff");}
@font-face {font-family: 'almoni-dl-aaa-300'; font-weight: 300; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/almoni-dl-aaa-300.woff") format("woff");}
@font-face {font-family: 'almoni-dl-aaa-400'; font-weight: 400; font-style: normal; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/almoni-dl-aaa-400.woff") format("woff");}
@font-face {font-family: 'almoni-dl-aaa-700'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/almoni-dl-aaa-700.woff") format("woff");}
@font-face {font-family: 'asimon-aaa-400'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/asimon-aaa-400.woff") format("woff");}
@font-face {font-family: 'atlas-aaa-500'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/atlas-aaa-500.woff") format("woff");}
@font-face {font-family: 'mixtape-aaa-400'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/mixtape-aaa-400.woff") format("woff");}
@font-face {font-family: 'museum-aaa-400'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/museum-aaa-400.woff") format("woff");}
@font-face {font-family: 'omes-aaa-400'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/omes-aaa-400.woff") format("woff");}
@font-face {font-family: 'MeodedPashut-oeregular'; src: url("//static.parastorage.com/services/third-party/fonts/user-site-fonts/fonts/open-source/meodedpashut_oeregular.woff") format("woff");}
@font-face {font-family: "Roboto-Thin"; src: url("//fonts.gstatic.com/s/roboto/v18/e7MeVAyvogMqFwwl61PKhPesZW2xOQ-xsNqO47m55DA.woff2") format("woff2");}
@font-face {font-family: "Roboto-Thin"; font-weight: 700; src: url("//fonts.gstatic.com/s/roboto/v18/fIKu7GwZTy_12XzG_jt8eA.woff2") format("woff2");}
@font-face {font-family: "Roboto-Thin"; font-style: italic; src: url("//fonts.gstatic.com/s/roboto/v18/dzxs_VxZUhdM2mEBkNa8svk_vArhqVIZ0nv9q090hN8.woff2") format("woff2");}
@font-face {font-family: "Roboto-Thin"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/roboto/v18/vSzulfKSK0LLjjfeaxcREvesZW2xOQ-xsNqO47m55DA.woff2") format("woff2");}
@font-face {font-family: "Roboto-Bold"; src: url("//fonts.gstatic.com/s/roboto/v18/97uahxiqZRoncBaCEI3aW1tXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Roboto-Bold"; font-weight: 700; src: url("//fonts.gstatic.com/s/roboto/v18/9_7S_tWeGDh5Pq3u05RVkltXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Roboto-Bold"; font-style: italic; src: url("//fonts.gstatic.com/s/roboto/v18/t6Nd4cfPRhZP44Q5QAjcC6g5eI2G47JWe0-AuFtD150.woff2") format("woff2");}
@font-face {font-family: "Roboto-Bold"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/roboto/v18/bmC0pGMXrhphrZJmniIZpag5eI2G47JWe0-AuFtD150.woff2") format("woff2");}
@font-face {font-family: "WorkSans-ExtraLight"; src: url("//fonts.gstatic.com/s/worksans/v3/u_mYNr_qYP37m7vgvmIYZxUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "WorkSans-ExtraLight"; font-weight: 700; src: url("//fonts.gstatic.com/s/worksans/v3/FD_Udbezj8EHXbdsqLUplxUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "WorkSans-SemiBold"; src: url("//fonts.gstatic.com/s/worksans/v3/z9rX03Xuz9ZNHTMg1_ghGRUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "WorkSans-SemiBold"; font-weight: 700; src: url("//fonts.gstatic.com/s/worksans/v3/4udXuXg54JlPEP5iKO5AmRUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "Poppins-ExtraLight"; src: url("//fonts.gstatic.com/s/poppins/v5/h3r77AwDsldr1E_2g4qqGPk_vArhqVIZ0nv9q090hN8.woff2") format("woff2");}
@font-face {font-family: "Poppins-ExtraLight"; font-weight: 700; src: url("//fonts.gstatic.com/s/poppins/v5/rijG6I_IOXJjsH07UEo2mw.woff2") format("woff2");}
@font-face {font-family: "Poppins-ExtraLight"; font-style: italic; src: url("//fonts.gstatic.com/s/poppins/v5/-GlaWpWcSgdVagNuOGuFKRUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "Poppins-ExtraLight"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/poppins/v5/Fm41upUVp7KTKUZhL0PfQVtXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Poppins-SemiBold"; src: url("//fonts.gstatic.com/s/poppins/v5/9VWMTeb5jtXkNoTv949Npfk_vArhqVIZ0nv9q090hN8.woff2") format("woff2");}
@font-face {font-family: "Poppins-SemiBold"; font-weight: 700; src: url("//fonts.gstatic.com/s/poppins/v5/aDjpMND83pDErGXlVEr-Sfk_vArhqVIZ0nv9q090hN8.woff2") format("woff2");}
@font-face {font-family: "Poppins-SemiBold"; font-style: italic; src: url("//fonts.gstatic.com/s/poppins/v5/RbebACOccNN-5ixkDIVLjRUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "Poppins-SemiBold"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/poppins/v5/c4FPK8_hIFKoX59qcGwdChUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "Barlow-ExtraLight"; src: url("//fonts.gstatic.com/s/barlow/v1/51v0xj5VPw1cLYHNhfd8NFtXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Barlow-ExtraLight"; font-weight: 700; src: url("//fonts.gstatic.com/s/barlow/v1/2woyxyDnPU0v4IiqYU9D1g.woff2") format("woff2");}
@font-face {font-family: "Barlow-ExtraLight"; font-style: italic; src: url("//fonts.gstatic.com/s/barlow/v1/14AxwKgJhKIO-YYUP_KtZag5eI2G47JWe0-AuFtD150.woff2") format("woff2");}
@font-face {font-family: "Barlow-ExtraLight"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/barlow/v1/cdbGxfKO8gdkBd5U5TuXqPesZW2xOQ-xsNqO47m55DA.woff2") format("woff2");}
@font-face {font-family: "Barlow-Medium"; src: url("//fonts.gstatic.com/s/barlow/v1/ZqlneECqpsd9SXlmAsD2E1tXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Barlow-Medium"; font-weight: 700; src: url("//fonts.gstatic.com/s/barlow/v1/yS165lxqGuDghyUMXeu6xVtXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Barlow-Medium"; font-style: italic; src: url("//fonts.gstatic.com/s/barlow/v1/xJLokI-F3wr7NRWXgS0pZ6g5eI2G47JWe0-AuFtD150.woff2") format("woff2");}
@font-face {font-family: "Barlow-Medium"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/barlow/v1/hw7DQwyFvE7wFOFzpow4xqg5eI2G47JWe0-AuFtD150.woff2") format("woff2");}
@font-face {font-family: "Oswald-ExtraLight"; src: url("//fonts.gstatic.com/s/oswald/v16/GwZ_PiN1Aind9Eyjp868E1tXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Oswald-ExtraLight"; font-weight: 700; src: url("//fonts.gstatic.com/s/oswald/v16/RqRF4AQrkUh3ft98NHH2mA.woff2") format("woff2");}
@font-face {font-family: "Oswald-Medium"; src: url("//fonts.gstatic.com/s/oswald/v16/cgaIrkaP9Empe8_PwXbajFtXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Oswald-Medium"; font-weight: 700; src: url("//fonts.gstatic.com/s/oswald/v16/dI-qzxlKVQA6TUC5RKSb31tXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-Light"; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/iEjm9hVxcattz37Y8gZwVSNMxVe3WGf96EDbCaLCBKE.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-Light"; font-weight: 700; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/iEjm9hVxcattz37Y8gZwVXP87xhFzkXvitf5EbJwljk.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-Light"; font-style: italic; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/zuqx3k1yUEl3Eavo-ZPEAhjqQayVfgmnRFwqYqN-Dis.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-Light"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/zuqx3k1yUEl3Eavo-ZPEAjp2K1CgsixPpkXulytJk5A.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-SemiBold"; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/iEjm9hVxcattz37Y8gZwVVBiiiFZ1SMKhjDurTuPCI4.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-SemiBold"; font-weight: 700; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/iEjm9hVxcattz37Y8gZwVVYUpUlN7yzNHgIMH66hSOI.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-SemiBold"; font-style: italic; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/zuqx3k1yUEl3Eavo-ZPEAoNfVaeyxI1fRb3LCiKLt24.woff2") format("woff2");}
@font-face {font-family: "CormorantGaramond-SemiBold"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/cormorantgaramond/v5/zuqx3k1yUEl3Eavo-ZPEAoWXH9gdibkBmfnjU2pcZcs.woff2") format("woff2");}
@font-face {font-family: 'CormorantGaramond'; font-style: normal; font-weight: 400; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/CormorantGaramond/v1/cormorant-garamond-v7-latin-ext_latin_cyrillic-regular.woff2") format("woff2");}
@font-face {font-family: 'CormorantGaramond'; font-style: italic; font-weight: 400; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/CormorantGaramond/v1/cormorant-garamond-v7-latin-ext_latin_cyrillic-italic.woff2") format("woff2");}
@font-face {font-family: 'CormorantGaramond'; font-style: normal; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/CormorantGaramond/v1/cormorant-garamond-v7-latin-ext_latin_cyrillic-700.woff2") format("woff2");}
@font-face {font-family: 'CormorantGaramond'; font-style: italic; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/CormorantGaramond/v1/cormorant-garamond-v7-latin-ext_latin_cyrillic-700italic.woff2") format("woff2");}
@font-face {font-family: "PlayfairDisplay-Bold"; src: url("//fonts.gstatic.com/s/playfairdisplay/v13/UC3ZEjagJi85gF9qFaBgIIWMvkC5IXg8PD2cMeMDjBI.woff2") format("woff2");}
@font-face {font-family: "PlayfairDisplay-Bold"; font-weight: 700; src: url("//fonts.gstatic.com/s/playfairdisplay/v13/UC3ZEjagJi85gF9qFaBgILxv9TIgpWQaRKdG-_MdlP0.woff2") format("woff2");}
@font-face {font-family: "PlayfairDisplay-Bold"; font-style: italic; src: url("//fonts.gstatic.com/s/playfairdisplay/v13/n7G4PqJvFP2Kubl0VBLDEPizZYmr4BUkAcTxjCN2kLE.woff2") format("woff2");}
@font-face {font-family: "PlayfairDisplay-Bold"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/playfairdisplay/v13/n7G4PqJvFP2Kubl0VBLDEA9QP145tN5qB9RQEnC5ftI.woff2") format("woff2");}
@font-face {font-family: "DancingScript-Regular"; src: url("//fonts.gstatic.com/s/dancingscript/v9/DK0eTGXiZjN6yA8zAEyM2RN-0beyHaEC1kqeqPFpWrs.woff2") format("woff2");}
@font-face {font-family: "DancingScript-Regular"; font-weight: 700; src: url("//fonts.gstatic.com/s/dancingscript/v9/KGBfwabt0ZRLA5W1ywjowZR92E8gBbe58j0pHY_YhTY.woff2") format("woff2");}
@font-face {font-family: "Raleway-SemiBold"; src: url("//fonts.gstatic.com/s/raleway/v12/STBOO2waD2LpX45SXYjQBfk_vArhqVIZ0nv9q090hN8.woff2") format("woff2");}
@font-face {font-family: "Raleway-SemiBold"; font-weight: 700; src: url("//fonts.gstatic.com/s/raleway/v12/WmVKXVcOuffP_qmCpFuyzfk_vArhqVIZ0nv9q090hN8.woff2") format("woff2");}
@font-face {font-family: "Raleway-SemiBold"; font-style: italic; src: url("//fonts.gstatic.com/s/raleway/v12/OY22yoG8EJ3IN_muVWm29BUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "Raleway-SemiBold"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/raleway/v12/lFxvRPuGFG5ktd7P0WRwKhUOjZSKWg4xBWp_C_qQx0o.woff2") format("woff2");}
@font-face {font-family: "Lato-Light"; src: url("//fonts.gstatic.com/s/lato/v14/2hXzmNaFRuKTSBR9nRGO-A.woff2") format("woff2");}
@font-face {font-family: "Lato-Light"; font-weight: 700; src: url("//fonts.gstatic.com/s/lato/v14/7nLfsQCzhQW_PwpkrwroYw.woff2") format("woff2");}
@font-face {font-family: "Lato-Light"; font-style: italic; src: url("//fonts.gstatic.com/s/lato/v14/XNVd6tsqi9wmKNvnh5HNEFtXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "Lato-Light"; font-weight: 700; font-style: italic; src: url("//fonts.gstatic.com/s/lato/v14/AcvTq8Q0lyKKNxRlL28Rn1tXRa8TVwTICgirnJhmVJw.woff2") format("woff2");}
@font-face {font-family: "August-Bold"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/AugustBold/v1/augustbold-webfont.woff2") format("woff2");}
@font-face {font-family: "August-Light"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/AugustLight/v1/augustlight-webfont.woff2") format("woff2");}
@font-face {font-family: "August-Medium"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/AugustMedium/v1/augustmedium-webfont.woff2") format("woff2");}
@font-face {font-family: "Knedge-Bold"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/KnedgeBold/v1/knedgebold-webfont.woff2") format("woff2");}
@font-face {font-family: "TsukushiGothic"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/TsukushiGothic/v2/WIX-TsukuGoPr5-R.woff2") format("woff2");}
@font-face {font-family: "TsukushiGothic"; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/TsukushiGothic/v2/WIX-TsukuGoPr5-D.woff2") format("woff2");}
@font-face {font-family: "Rodin-Light"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Rodin/v2/WIX-RodinProN-L.woff2") format("woff2");}
@font-face {font-family: "Rodin-Light"; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Rodin/v2/WIX-RodinProN-M.woff2") format("woff2");}
@font-face {font-family: "Rodin-Demi-Bold"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Rodin/v2/WIX-RodinProN-DB.woff2") format("woff2");}
@font-face {font-family: "NewCezanne"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/NewCezanne/v2/WIX-NewCezanneProN-M.woff2") format("woff2");}
@font-face {font-family: "UDKakugoLarge"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/UDKakugoLarge/v2/WIX-UDKakugo_LargePr6N-M.woff2") format("woff2");}
@font-face {font-family: "UDKakugoLarge"; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/UDKakugoLarge/v2/WIX-UDKakugo_LargePr6N-DB.woff2") format("woff2");}
@font-face {font-family: "TsukushiMaruGothic"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/TsukushiMaruGothic/v2/WIX-TsukuARdGothicStd-M.woff2") format("woff2");}
@font-face {font-family: "TsukushiMaruGothic"; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/TsukushiMaruGothic/v2/WIX-TsukuARdGothicStd-B.woff2") format("woff2");}
@font-face {font-family: "Seurat"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Seurat/v2/WIX-SeuratProN-M.woff2") format("woff2");}
@font-face {font-family: "Seurat"; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Seurat/v2/WIX-SeuratProN-DB.woff2") format("woff2");}
@font-face {font-family: "TsukushiBMaruGothic"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/TsukushiBMaruGothic/v2/WIX-TsukuBRdGothicStd-M.woff2") format("woff2");}
@font-face {font-family: "UDMincho"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/UDMincho/v2/WIX-UDMinchoPr6N-M.woff2") format("woff2");}
@font-face {font-family: "UDMincho"; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/UDMincho/v2/WIX-UDMinchoPr6N-DB.woff2") format("woff2");}
@font-face {font-family: "TsukushiOldMincho"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/TsukushiOldMincho/v2/WIX-TsukuAOldMinPr6N-M.woff2") format("woff2");}
@font-face {font-family: "Matisse"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Matisse/v2/WIX-MatisseProN-M.woff2") format("woff2");}
@font-face {font-family: "Matisse"; font-weight: 700; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Matisse/v2/WIX-MatisseProN-DB.woff2") format("woff2");}
@font-face {font-family: "Skip"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Skip/v2/WIX-SkipStd-M.woff2") format("woff2");}
@font-face {font-family: "Cookhand"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Japanese/Cookhand/v2/WIX-CookHandStd-R.woff2") format("woff2");}
@font-face {font-family: "Adobe-Caslon-W08-SmBd"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Adobe_Caslon/v1/AdobeCaslonW08-Semibold.woff2") format("woff2");}
@font-face {font-family: "Avenir-LT-W05_85-Heavy"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Avenir_Family_Pack/v1/AvenirLTW05-85Heavy.woff2") format("woff2");}
@font-face {font-family: "Avenir-LT-W05_35-Light"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Avenir_Family_Pack/v1/AvenirLTW05-35Light.woff2") format("woff2");}
@font-face {font-family: "Brandon-Grot-W05-Light"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/BrandonGrot/v1/BrandonGrotesqueCondW05-Lt.woff2") format("woff2");}
@font-face {font-family: "Bree-W05-Thin-Oblique"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Bree_Bundle/v1/BreeW05-ThinOblique.woff2") format("woff2");}
@font-face {font-family: "Geotica-W05-Four-Open"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Geotica/v1/GeoticaFourW05-Open.woff2") format("woff2");}
@font-face {font-family: "Didot-W05-Italic"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Linotype_Didot/v1/DidotLTW05-Italic.woff2") format("woff2");}
@font-face {font-family: "Lulo-Clean-W05-One-Bold"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Lulo_Clean/v1/LuloCleanW05-OneBold.woff2") format("woff2");}
@font-face {font-family: "BaskervilleMTW05-SmBdIt"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Monotype_Baskerville/v1/BaskervilleMTW05-SemiboldIt.woff2") format("woff2");}
@font-face {font-family: "Museo-W05-700"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Museo/v1/MuseoW05-700.woff2") format("woff2");}
@font-face {font-family: "Museo-Slab-W05-100"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Museo_Slab/v1/MuseoSlabW05-100.woff2") format("woff2");}
@font-face {font-family: "Futura-LT-W05-Book"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Futura_Complete/v1/FuturaLTW05-Book.woff2") format("woff2");}
@font-face {font-family: "Futura-LT-W05-Light"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Futura_Complete/v1/FuturaLTW05-LightCondensed.woff2") format("woff2");}
@font-face {font-family: "Nimbus-Sans-TW05Con"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/Nimbus/v1/NimbusSansW05-Regular.woff2") format("woff2");}
@font-face {font-family: "Bodoni-W05-Poster"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/PosterBodoni/v1/PosterBodoniBTWGL4W05-Roman.woff2") format("woff2");}
@font-face {font-family: "Proxima-N-W05-Reg"; src: url("//static.parastorage.com/services/santa-resources/resources/viewer/user-site-fonts/fonts/ProximNova/v1/ProximaNovaW05-Regular.woff2") format("woff2");}