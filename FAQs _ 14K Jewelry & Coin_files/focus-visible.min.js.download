!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t():"function"==typeof define&&define.amd?define(t):t()}(0,function(){"use strict";!function(e){var t;function n(){t||(t=!0,e())}"complete"===document.readyState?e():(t=!1,document.addEventListener("DOMContentLoaded",n,!1),window.addEventListener("load",n,!1))}(function(){var e=!0,t=!1,n=null,o={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function d(t){e=!1}function i(){document.addEventListener("mousemove",u),document.addEventListener("mousedown",u),document.addEventListener("mouseup",u),document.addEventListener("pointermove",u),document.addEventListener("pointerdown",u),document.addEventListener("pointerup",u),document.addEventListener("touchmove",u),document.addEventListener("touchstart",u),document.addEventListener("touchend",u)}function u(t){"html"!==t.target.nodeName.toLowerCase()&&(e=!1,document.removeEventListener("mousemove",u),document.removeEventListener("mousedown",u),document.removeEventListener("mouseup",u),document.removeEventListener("pointermove",u),document.removeEventListener("pointerdown",u),document.removeEventListener("pointerup",u),document.removeEventListener("touchmove",u),document.removeEventListener("touchstart",u),document.removeEventListener("touchend",u))}document.addEventListener("keydown",function(t){t.altKey||t.ctrlKey||t.metaKey||(e=!0)},!0),document.addEventListener("mousedown",d,!0),document.addEventListener("pointerdown",d,!0),document.addEventListener("touchstart",d,!0),document.addEventListener("focus",function(t){var n,d,i;t.target!=document&&"HTML"!=t.target.nodeName&&(e||(n=t.target,d=n.type,"INPUT"==(i=n.tagName)&&o[d]&&!n.readonly||"TEXTAREA"==i&&!n.readonly||"true"==n.contentEditable))&&(function(e){e.classList.contains("focus-visible")||(e.classList.add("focus-visible"),e.setAttribute("data-focus-visible-added",""))}(t.target),e=!1)},!0),document.addEventListener("blur",function(e){var o;e.target!=document&&"HTML"!=e.target.nodeName&&e.target.classList.contains("focus-visible")&&(t=!0,window.clearTimeout(n),n=window.setTimeout(function(){t=!1,window.clearTimeout(n)},100),(o=e.target).hasAttribute("data-focus-visible-added")&&(o.classList.remove("focus-visible"),o.removeAttribute("data-focus-visible-added")))},!0),document.addEventListener("visibilitychange",function(n){"hidden"==document.visibilityState&&(t&&(e=!0),i())},!0),i(),document.body.classList.add("js-focus-visible")})});
//# sourceMappingURL=focus-visible.min.js.map
