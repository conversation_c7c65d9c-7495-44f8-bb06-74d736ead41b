!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[GoogleMap]",["react","reactDOM"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[GoogleMap]"]=t(require("react"),require("react-dom")):e["rb_wixui.thunderbolt[GoogleMap]"]=t(e.React,e.ReactDOM)}("undefined"!=typeof self?self:this,(function(e,t){return function(){var n={39507:function(e){"use strict";e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var o,r,a;if(Array.isArray(t)){if((o=t.length)!=n.length)return!1;for(r=o;0!=r--;)if(!e(t[r],n[r]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((o=(a=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(r=o;0!=r--;)if(!Object.prototype.hasOwnProperty.call(n,a[r]))return!1;for(r=o;0!=r--;){var l=a[r];if(!e(t[l],n[l]))return!1}return!0}return t!=t&&n!=n}},5329:function(t){"use strict";t.exports=e},95561:function(e){"use strict";e.exports=t},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},o={};function r(e){var t=o[e];if(void 0!==t)return t.exports;var a=o[e]={exports:{}};return n[e](a,a.exports,r),a.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.p="https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/";var a={};return function(){"use strict";r.r(a),r.d(a,{components:function(){return pt}});var e=r(448),t=r.n(e),n=r(5329);function o(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=o(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var l=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=o(e))&&(r&&(r+=" "),r+=t);return r};const i=()=>"undefined"!=typeof window,s=e=>Object.entries(e).reduce(((e,[t,n])=>(t.includes("data-")&&(e[t]=n),e)),{});const c="wixui-",u=(e,...t)=>{const n=[];return e&&n.push(`${c}${e}`),t.forEach((e=>{e&&(n.push(`${c}${e}`),n.push(e))})),n.join(" ")};const d=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),p=(e,t)=>e?{}:t,m=(((e,t,n=[])=>{e.reduce(((e,t)=>({...e,[t]:!0})),{}),n.length})(["isSocialElementsBlocked"],(e=>{let{isSocialElementsBlocked:t}=e;return t?"WithConsentWrapper":void 0})),e=>({consentPolicy:e.currentConsentPolicy,openSettingModal:()=>e.openSettingModal([])}));var g={root:"google-map"};function f(){const e=n.useRef([]);return[n.useCallback((()=>new Promise((t=>e.current.push(t)))),[]),n.useCallback((t=>{e.current.forEach((e=>e(t))),e.current.length=0}),[])]}const h=(e,t)=>{if(e||t)return t?{path:e,fillColor:t,strokeColor:t,fillOpacity:1,scale:.5}:(e=>{if(e&&e.endsWith(".webp"))try{const t=e.match(/media\/[^/]+/)[0].split(".")[1];e=e.replace(/.webp$/,"."+t)}catch(e){}return e})(e)},v=(e,t)=>e.map((e=>({...e,icon:h(e.pinIcon,e.pinColor)||t&&h(t.pinIcon,null==t?void 0:t.pinColor)})));var y="load",b="message",C="SET_INITIAL_LOCATIONS",E="SET_CENTER",w="CENTER_UPDATED",M="SET_ZOOM",k="ZOOM_UPDATED",S="MARKER_CLICKED",O="MAP_CLICKED",L="GET_MARKERS",I="MARKERS",T="FIT_BOUNDS",P="MAP_IDLE",N="OPEN_INFO_WINDOW",_="TILES_LOADED",x="SET_MARKER_ICON",R="SET_MARKER_ICON_FINISHED",A="SET_DIRECTION";const D=["iw","ar","ur","fa"];function z(e,t,o){let{mapData:r,language:a,isEditorMode:l}=t,{onUpdateZoom:s,onUpdateCenter:c,onMarkerClicked:u,onMapClicked:d}=o;const p=(0,n.useRef)([]),m=(0,n.useRef)(null),g=(0,n.useMemo)((()=>{var e,t,n,o;return void 0!==r.defaultLocation?{pinIcon:null!=(e=null==(t=r.locations[r.defaultLocation])?void 0:t.pinIcon)?e:"",pinColor:null!=(n=null==(o=r.locations[r.defaultLocation])?void 0:o.pinColor)?n:""}:void 0}),[r.defaultLocation,r.locations]),[h,z]=f(),[F,B]=f(),[j,G]=f(),[Z,H]=f(),[W,U]=f(),[J,V]=f(),[K,$]=f(),[q,Y]=(0,n.useState)(!1),Q=(e,t)=>{const n=m.current;return v(n||e,t)},X=(0,n.useMemo)((()=>({...r,locations:Q(r.locations,g)})),[r,g]),ee=function(e,t){void 0===t&&(t=null);const o=(0,n.useRef)(t);return(0,n.useEffect)((()=>{o.current=e}),[e]),o.current}(X),te={[w]:e=>null==c?void 0:c(e),[k]:e=>null==s?void 0:s({zoom:e}),[S]:e=>null==u?void 0:u({type:"markerClicked",...e}),[O]:e=>{let{longitude:t,latitude:n,...o}=e;return null==d?void 0:d({type:"mapClicked",location:{longitude:t,latitude:n},...o})},[I]:e=>z(e),[P]:()=>{const e=p.current.shift();null==e||e()},[_]:()=>{q||Y(!0)},[R]:()=>$()},ne={[y]:e=>{let{_sendMessage:t}=e;t({type:C,data:JSON.stringify({...r,locations:Q(r.locations)})},{forceSend:!0}),t({type:A,data:JSON.stringify({direction:D.includes(a)?"rtl":"ltr"})})},[b]:e=>{let{event:t}=e;if("string"==typeof t.payload){var n;const{type:e,data:o}=JSON.parse(t.payload);null==(n=te[e])||n.call(te,o)}}},[oe,re]=function(e){let{reducer:t=()=>({}),iframeLoaded:o}=e;const r=(0,n.useRef)([]),a=(0,n.useRef)(void 0),l=(0,n.useRef)(void 0),s=(0,n.useRef)(void 0),c=(0,n.useCallback)(((e,t)=>{if(a.current&&o||null!=t&&t.forceSend){var n;const t=null==(n=a.current)?void 0:n.contentWindow;return void(null==t||t.postMessage(e,"*"))}if(!a.current||!l.current||!1===o)return void r.current.push(e);const i=a.current.contentWindow;null==i||i.postMessage(e,"*")}),[o]),u=(0,n.useCallback)((()=>{0!==r.current.length&&!1!==o&&(r.current.forEach((e=>c(e))),r.current=[])}),[c,o]),d=(0,n.useCallback)((e=>{if(s.current&&(s.current(),s.current=void 0),!e)return;const n=()=>{l.current=!0,u(),t({type:"load"},c)};e.addEventListener("load",n),a.current=e,s.current=()=>{e.removeEventListener("load",n)}}),[t,c,u]);return(0,n.useEffect)((()=>{o&&u()}),[o,u]),(0,n.useEffect)((()=>{if(!i())return;const e=e=>{var n;e.source&&e.source!==(null==(n=a.current)?void 0:n.contentWindow)||t({type:"message",payload:e.data},c)};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}}),[t,c]),[d,c]}({reducer:(e,t)=>{var n;return null==(n=ne[e.type])?void 0:n.call(ne,{event:e,_sendMessage:t})},iframeLoaded:q});return(0,n.useImperativeHandle)(e,(()=>({setMapCenter:(e,t)=>{const n=F();return re({type:E,data:JSON.stringify({longitude:e,latitude:t})}),p.current.push(B),n},fitBounds:e=>{let{north:t,east:n,west:o,south:r}=e;const a=Z();return re({type:T,data:JSON.stringify({north:t,east:n,west:o,south:r})}),p.current.push(H),a},setMarkerIcon:e=>{let{locations:t,coordinates:n,iconOptions:o}=e;const r=K();return m.current=t,re({type:x,data:JSON.stringify({...n,iconOptions:o})}),r},setMapZoom:e=>{const t=j();return re({type:M,data:e}),p.current.push(G),t},getVisibleMarkers:()=>{const e=h();return re({type:L}),e},openInfoWindow:e=>{if(!X.showDirectionsLink){const t=J();return re({type:N,data:JSON.stringify({locationIndex:e})}),p.current.push(V),t}return new Promise((e=>e()))},setMarkers:(e,t)=>{const n=W();return m.current=e,re(JSON.stringify({...X,locations:v(e,g),openInfoWindow:null==t?void 0:t.openInfoWindow})),p.current.push(U),n}}))),(0,n.useEffect)((()=>{if(!q||!l)return;const e=!!ee&&(t=X.locations,n=ee.locations,t.length===n.length&&t.every(((e,t)=>{const o=Object.keys(e);return o.length===Object.keys(n[t]).length&&o.every((o=>"locationLinkAttributes"===o||e[o]===n[t][o]))})));var t,n;re(JSON.stringify({...X,locations:Q(X.locations,g),shouldKeepMarkers:e}))}),[X,re,g,q,ee,l]),[oe]}var F={GoogleMapSkin:"GRu5Ra",googleMapSkin:"GRu5Ra",mapContainer:"TD54YK",GoogleMapDefault:"H7BYNf",googleMapDefault:"H7BYNf",GoogleMapSloppy:"ATObVg",googleMapSloppy:"ATObVg",brd:"CV2IPZ",one:"l3mQRd",two:"NlKT7Q",GoogleMapLiftedShadow:"aMqF6e",googleMapLiftedShadow:"aMqF6e",shd:"OQGVRy",left:"tlWGDF",right:"U1VCL6"};const B={GoogleMapLiftedShadow:()=>n.createElement(n.Fragment,null,n.createElement("div",{className:l(F.left,F.shd)}),n.createElement("div",{className:l(F.right,F.shd)})),GoogleMapSloppy:()=>n.createElement(n.Fragment,null,n.createElement("div",{className:l(F.brd,F.one)}),n.createElement("div",{className:l(F.brd,F.two)})),GoogleMapSkin:()=>n.createElement(n.Fragment,null),GoogleMapDefault:()=>n.createElement(n.Fragment,null)};var j=r.p+"media/googleMap.ea5928de.html",G=r.p+"media/google-map.min.08346697.js";const Z=e=>e.replace("https://static.parastorage.com/services/","https://editor.wix.com/_partials/"),H=(e,t)=>{const{id:o,className:r,customClassNames:a=[],skin:c,urlQueries:d,mapData:m,translations:f,isConsentPolicyActive:h,onUpdateCenter:v,onUpdateZoom:y,onMarkerClicked:b,onMapClicked:C,onMouseEnter:E,onMouseLeave:w,isDesignerMode:M,isEditorMode:k,language:S,onMapMount:O,lang:L}=e,I=B[c],T=((e,t)=>{const n=t?Z(G):G,o=new URLSearchParams({googleMapsScriptPath:new URL(n).pathname});return i()&&o.append("origin",window.origin),(t?Z(j):j)+"?"+e+"&"+o})(d,M),[P,N]=n.useState(!1),[_]=z(t,{mapData:m,isEditorMode:k,language:S},{onUpdateCenter:v,onUpdateZoom:y,onMarkerClicked:b,onMapClicked:C});n.useEffect((()=>{N(!0)}),[T]),n.useEffect((()=>{null==O||O()}),[O]);const x=f.title,R=p(h,{id:o,className:l(F[c],F.wixIframe,r,u(g.root,...a)),...s(e),lang:L});return n.createElement("div",R,P&&n.createElement("wix-iframe",{"data-src":T},n.createElement(I,null),n.createElement("div",{id:"mapContainer_"+o,onMouseEnter:E,onMouseLeave:w,className:F.mapContainer},n.createElement("iframe",{ref:_,title:x,"aria-label":x,"data-src":T,width:"100%",height:"100%",frameBorder:"0",scrolling:"no",allowFullScreen:!0}))))};var W=n.forwardRef(H),U=r(95561),J=r(39507);function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},V.apply(null,arguments)}function K(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(-1!==t.indexOf(o))continue;n[o]=e[o]}return n}function $(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}const q="NOT_LOADED",Y="LOADING",Q="LOADED",X="FAILED",ee="AUTH_FAILURE";class te{static async load(e,t){var n;const o=e.libraries?e.libraries.split(","):[],r=this.serializeParams(e);this.listeners.push(t),null!=(n=window.google)&&null!=(n=n.maps)&&n.importLibrary?(this.serializedApiParams||(this.loadingStatus=Q),this.notifyLoadingStatusListeners()):(this.serializedApiParams=r,this.initImportLibrary(e)),this.serializedApiParams&&this.serializedApiParams!==r&&console.warn("[google-maps-api-loader] The maps API has already been loaded with different parameters and will not be loaded again. Refresh the page for new values to have effect.");const a=["maps",...o];await Promise.all(a.map((e=>google.maps.importLibrary(e))))}static serializeParams(e){return[e.v,e.key,e.language,e.region,e.authReferrerPolicy,e.solutionChannel].join("/")}static initImportLibrary(e){if(window.google||(window.google={}),window.google.maps||(window.google.maps={}),window.google.maps.importLibrary)return void console.error("[google-maps-api-loader-internal]: initImportLibrary must only be called once");let t=null;const n=()=>t||(t=new Promise(((t,n)=>{var o;const r=document.createElement("script"),a=new URLSearchParams;for(const[t,n]of Object.entries(e)){const e=t.replace(/[A-Z]/g,(e=>"_"+e[0].toLowerCase()));a.set(e,String(n))}a.set("loading","async"),a.set("callback","__googleMapsCallback__"),r.async=!0,r.src="https://maps.googleapis.com/maps/api/js?"+a.toString(),r.nonce=(null==(o=document.querySelector("script[nonce]"))?void 0:o.nonce)||"",r.onerror=()=>{this.loadingStatus=X,this.notifyLoadingStatusListeners(),n(new Error("The Google Maps JavaScript API could not load."))},window.__googleMapsCallback__=()=>{this.loadingStatus=Q,this.notifyLoadingStatusListeners(),t()},window.gm_authFailure=()=>{this.loadingStatus=ee,this.notifyLoadingStatusListeners()},this.loadingStatus=Y,this.notifyLoadingStatusListeners(),document.head.append(r)})),t);google.maps.importLibrary=e=>n().then((()=>google.maps.importLibrary(e)))}static notifyLoadingStatusListeners(){for(const e of this.listeners)e(this.loadingStatus)}}te.loadingStatus=q,te.serializedApiParams=void 0,te.listeners=[];const ne=["onLoad","onError","apiKey","version","libraries"],oe=["children"],re=n.createContext(null);const ae=e=>{const{children:t}=e,o=K(e,oe),{mapInstances:r,addMapInstance:a,removeMapInstance:l,clearMapInstances:i}=function(){const[e,t]=(0,n.useState)({});return{mapInstances:e,addMapInstance:(e,n="default")=>{t((t=>V({},t,{[n]:e})))},removeMapInstance:(e="default")=>{t((t=>K(t,[e].map($))))},clearMapInstances:()=>{t({})}}}(),{status:s,loadedLibraries:c,importLibrary:u}=function(e){const{onLoad:t,onError:o,apiKey:r,version:a,libraries:l=[]}=e,i=K(e,ne),[s,c]=(0,n.useState)(te.loadingStatus),[u,d]=(0,n.useReducer)(((e,t)=>e[t.name]?e:V({},e,{[t.name]:t.value})),{}),p=(0,n.useMemo)((()=>null==l?void 0:l.join(",")),[l]),m=(0,n.useMemo)((()=>JSON.stringify(V({apiKey:r,version:a},i))),[r,a,i]),g=(0,n.useCallback)((async e=>{var t;if(u[e])return u[e];if(null==(t=google)||null==(t=t.maps)||!t.importLibrary)throw new Error("[api-provider-internal] importLibrary was called before google.maps.importLibrary was defined.");const n=await window.google.maps.importLibrary(e);return d({name:e,value:n}),n}),[u]);return(0,n.useEffect)((()=>{(async()=>{try{const e=V({key:r},i);a&&(e.v=a),(null==p?void 0:p.length)>0&&(e.libraries=p),(void 0===e.channel||e.channel<0||e.channel>999)&&delete e.channel,void 0===e.solutionChannel?e.solutionChannel="GMP_visgl_rgmlibrary_v1_default":""===e.solutionChannel&&delete e.solutionChannel,await te.load(e,(e=>c(e)));for(const e of["core","maps",...l])await g(e);t&&t()}catch(e){o?o(e):console.error("<ApiProvider> failed to load the Google Maps JavaScript API",e)}})()}),[r,p,m]),{status:s,loadedLibraries:u,importLibrary:g}}(o),d=(0,n.useMemo)((()=>({mapInstances:r,addMapInstance:a,removeMapInstance:l,clearMapInstances:i,status:s,loadedLibraries:c,importLibrary:u})),[r,a,l,i,s,c,u]);return n.createElement(re.Provider,{value:d},t)};function le(e,t,n){const o={type:e,map:t,detail:{},stoppable:!1,stop:()=>{}};if(se.includes(e)){const e=o,n=t.getCenter(),r=t.getZoom(),a=t.getHeading()||0,l=t.getTilt()||0,i=t.getBounds();return n&&i&&Number.isFinite(r)||console.warn("[createEvent] at least one of the values from the map returned undefined. This is not expected to happen. Please report an issue at https://github.com/visgl/react-google-maps/issues/new"),e.detail={center:(null==n?void 0:n.toJSON())||{lat:0,lng:0},zoom:r||0,heading:a,tilt:l,bounds:(null==i?void 0:i.toJSON())||{north:90,east:180,south:-90,west:-180}},e}if(ce.includes(e)){var r;if(!n)throw new Error("[createEvent] mouse events must provide a srcEvent");const e=o;return e.domEvent=n.domEvent,e.stoppable=!0,e.stop=()=>n.stop(),e.detail={latLng:(null==(r=n.latLng)?void 0:r.toJSON())||null,placeId:n.placeId},e}return o}const ie={onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onContextmenu:"contextmenu",onDblclick:"dblclick",onDrag:"drag",onDragend:"dragend",onDragstart:"dragstart",onHeadingChanged:"heading_changed",onIdle:"idle",onIsFractionalZoomEnabledChanged:"isfractionalzoomenabled_changed",onMapCapabilitiesChanged:"mapcapabilities_changed",onMapTypeIdChanged:"maptypeid_changed",onMousemove:"mousemove",onMouseout:"mouseout",onMouseover:"mouseover",onProjectionChanged:"projection_changed",onRenderingTypeChanged:"renderingtype_changed",onTilesLoaded:"tilesloaded",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed",onCameraChanged:"bounds_changed"},se=["bounds_changed","center_changed","heading_changed","tilt_changed","zoom_changed"],ce=["click","contextmenu","dblclick","mousemove","mouseout","mouseover"],ue=Object.keys(ie);function de(e,t){const o=(0,n.useRef)(void 0);o.current&&J(t,o.current)||(o.current=t),(0,n.useEffect)(e,o.current)}const pe=new Set(["backgroundColor","clickableIcons","controlSize","disableDefaultUI","disableDoubleClickZoom","draggable","draggableCursor","draggingCursor","fullscreenControl","fullscreenControlOptions","gestureHandling","headingInteractionEnabled","isFractionalZoomEnabled","keyboardShortcuts","mapTypeControl","mapTypeControlOptions","mapTypeId","maxZoom","minZoom","noClear","panControl","panControlOptions","restriction","rotateControl","rotateControlOptions","scaleControl","scaleControlOptions","scrollwheel","streetView","streetViewControl","streetViewControlOptions","styles","tiltInteractionEnabled","zoomControl","zoomControlOptions"]);function me(){var e;return(null==(e=(0,n.useContext)(re))?void 0:e.status)||q}function ge(e){return function(e){return!(!e||"object"!=typeof e)&&"lat"in e&&"lng"in e&&Number.isFinite(e.lat)&&Number.isFinite(e.lng)}(e)?e:e.toJSON()}const fe=()=>n.createElement("div",{style:{position:"absolute",top:0,left:0,bottom:0,right:0,zIndex:999,display:"flex",flexFlow:"column nowrap",textAlign:"center",justifyContent:"center",fontSize:".8rem",color:"rgba(0,0,0,0.6)",background:"#dddddd",padding:"1rem 1.5rem"}},n.createElement("h2",null,"Error: AuthFailure"),n.createElement("p",null,"A problem with your API key prevents the map from rendering correctly. Please make sure the value of the ",n.createElement("code",null,"APIProvider.apiKey")," prop is correct. Check the error-message in the console for further details."));function he(){return me()===Q}function ve(e){const t=function(){const[,e]=(0,n.useReducer)((e=>e+1),0);return e}(),o=(0,n.useRef)({center:{lat:0,lng:0},heading:0,tilt:0,zoom:0});return(0,n.useEffect)((()=>{if(!e)return;const n=google.maps.event.addListener(e,"bounds_changed",(()=>{!function(e,t){const n=e.getCenter(),o=e.getZoom(),r=e.getHeading()||0,a=e.getTilt()||0,l=e.getBounds();n&&l&&Number.isFinite(o)||console.warn("[useTrackedCameraState] at least one of the values from the map returned undefined. This is not expected to happen. Please report an issue at https://github.com/visgl/react-google-maps/issues/new"),Object.assign(t.current,{center:(null==n?void 0:n.toJSON())||{lat:0,lng:0},zoom:o||0,heading:r,tilt:a})}(e,o),t()}));return()=>n.remove()}),[e,t]),o}const ye=["id","defaultBounds","defaultCenter","defaultZoom","defaultHeading","defaultTilt","reuseMaps","renderingType","colorScheme"],be=["padding"];class Ce{static has(e){return this.entries[e]&&this.entries[e].length>0}static pop(e){return this.entries[e]&&this.entries[e].pop()||null}static push(e,t){this.entries[e]||(this.entries[e]=[]),this.entries[e].push(t)}}function Ee(e,t){const o=he(),[r,a]=(0,n.useState)(null),[l,i]=function(){const[e,t]=(0,n.useState)(null);return[e,(0,n.useCallback)((e=>t(e)),[t])]}(),s=ve(r),{id:c,defaultBounds:u,defaultCenter:d,defaultZoom:p,defaultHeading:m,defaultTilt:g,reuseMaps:f,renderingType:h,colorScheme:v}=e,y=K(e,ye),b=void 0!==e.zoom||void 0!==e.defaultZoom,C=void 0!==e.center||void 0!==e.defaultCenter;u||b&&C||console.warn("<Map> component is missing configuration. You have to provide zoom and center (via the `zoom`/`defaultZoom` and `center`/`defaultCenter` props) or specify the region to show using `defaultBounds`. See https://visgl.github.io/react-google-maps/docs/api-reference/components/map#required"),!y.center&&d&&(y.center=d),!y.zoom&&Number.isFinite(p)&&(y.zoom=p),!y.heading&&Number.isFinite(m)&&(y.heading=m),!y.tilt&&Number.isFinite(g)&&(y.tilt=g);for(const e of Object.keys(y))void 0===y[e]&&delete y[e];const E=(0,n.useRef)(void 0);return(0,n.useEffect)((()=>{if(!l||!o)return;const{addMapInstance:n,removeMapInstance:r}=t,{mapId:i}=e,d=`${i||"default"}:${h||"default"}:${v||"LIGHT"}`;let p,m;if(f&&Ce.has(d)?(m=Ce.pop(d),p=m.getDiv(),l.appendChild(p),m.setOptions(y),setTimeout((()=>m.setCenter(m.getCenter())),0)):(p=document.createElement("div"),p.style.height="100%",l.appendChild(p),m=new google.maps.Map(p,V({},y,h?{renderingType:h}:{},v?{colorScheme:v}:{}))),a(m),n(m,c),u){const{padding:e}=u,t=K(u,be);m.fitBounds(t,e)}else b&&C||m.fitBounds({east:180,west:-180,south:-90,north:90});if(E.current){const{mapId:e,cameraState:t}=E.current;e!==i&&m.setOptions(t)}return()=>{E.current={mapId:i,cameraState:s.current},p.remove(),f?Ce.push(d,m):google.maps.event.clearInstanceListeners(m),a(null),r(c)}}),[l,o,c,e.mapId,e.renderingType,e.colorScheme]),[r,i,s]}Ce.entries={};const we=n.createContext(null),Me=e=>{const{children:t,id:o,className:r,style:a}=e,l=(0,n.useContext)(re),i=me();if(!l)throw new Error("<Map> can only be used inside an <ApiProvider> component.");const[s,c,u]=Ee(e,l);!function(e,t,o){const r=o.center?ge(o.center):null;let a=null,l=null;r&&Number.isFinite(r.lat)&&Number.isFinite(r.lng)&&(a=r.lat,l=r.lng);const i=Number.isFinite(o.zoom)?o.zoom:null,s=Number.isFinite(o.heading)?o.heading:null,c=Number.isFinite(o.tilt)?o.tilt:null;(0,n.useLayoutEffect)((()=>{if(!e)return;const n={};let o=!1;null===a||null===l||t.current.center.lat===a&&t.current.center.lng===l||(n.center={lat:a,lng:l},o=!0),null!==i&&t.current.zoom!==i&&(n.zoom=i,o=!0),null!==s&&t.current.heading!==s&&(n.heading=s,o=!0),null!==c&&t.current.tilt!==c&&(n.tilt=c,o=!0),o&&e.moveCamera(n)}))}(s,u,e),function(e,t){for(const o of ue){const r=t[o],a=ie[o];(0,n.useEffect)((()=>{if(!e)return;if(!r)return;const t=google.maps.event.addListener(e,a,(t=>{r(le(a,e,t))}));return()=>t.remove()}),[e,a,r])}}(s,e),function(e,t){const n={},o=Object.keys(t);for(const e of o)pe.has(e)&&(n[e]=t[e]);de((()=>{e&&e.setOptions(n)}),[n])}(s,e);const d=function(e,t){const{viewport:o,viewState:r}=t,a=!!o;return(0,n.useLayoutEffect)((()=>{if(!e||!r)return;const{latitude:t,longitude:n,bearing:o,pitch:a,zoom:l}=r;e.moveCamera({center:{lat:t,lng:n},heading:o,tilt:a,zoom:l+1})}),[e,r]),a}(s,e),p=!!e.controlled;(0,n.useEffect)((()=>{if(s)return d&&s.setOptions({disableDefaultUI:!0}),(d||p)&&s.setOptions({gestureHandling:"none",keyboardShortcuts:!1}),()=>{s.setOptions({gestureHandling:e.gestureHandling,keyboardShortcuts:e.keyboardShortcuts})}}),[s,d,p,e.gestureHandling,e.keyboardShortcuts]);const m=e.center?ge(e.center):null;let g=null,f=null;m&&Number.isFinite(m.lat)&&Number.isFinite(m.lng)&&(g=m.lat,f=m.lng);const h=(0,n.useMemo)((()=>{var t,n,o,r,a;return{center:{lat:null!=(t=g)?t:0,lng:null!=(n=f)?n:0},zoom:null!=(o=e.zoom)?o:0,heading:null!=(r=e.heading)?r:0,tilt:null!=(a=e.tilt)?a:0}}),[g,f,e.zoom,e.heading,e.tilt]);(0,n.useLayoutEffect)((()=>{if(!s||!p)return;s.moveCamera(h);const e=s.addListener("bounds_changed",(()=>{s.moveCamera(h)}));return()=>e.remove()}),[s,p,h]);const v=(0,n.useMemo)((()=>V({width:"100%",height:"100%",position:"relative",zIndex:d?-1:0},a)),[a,d]),y=(0,n.useMemo)((()=>({map:s})),[s]);return i===ee?n.createElement("div",{style:V({position:"relative"},r?{}:v),className:r},n.createElement(fe,null)):n.createElement("div",V({ref:c,"data-testid":"map",style:r?void 0:v,className:r},o?{id:o}:{}),s?n.createElement(we.Provider,{value:y},t):null)};Me.deckGLViewProps=!0;const ke=new Set;function Se(...e){const t=JSON.stringify(e);ke.has(t)||(ke.add(t),console.error(...e))}const Oe=(e=null)=>{const t=(0,n.useContext)(re),{map:o}=(0,n.useContext)(we)||{};if(null===t)return Se("useMap(): failed to retrieve APIProviderContext. Make sure that the <APIProvider> component exists and that the component you are calling `useMap()` from is a sibling of the <APIProvider>."),null;const{mapInstances:r}=t;return null!==e?r[e]||null:o||(r.default||null)};function Le(e){const t=he(),o=(0,n.useContext)(re);return(0,n.useEffect)((()=>{t&&o&&o.importLibrary(e)}),[t,o,e]),(null==o?void 0:o.loadedLibraries[e])||null}function Ie(e,t,o){(0,n.useEffect)((()=>{if(!e||!t||!o)return;const n=google.maps.event.addListener(e,t,o);return()=>n.remove()}),[e,t,o])}function Te(e,t,o){(0,n.useEffect)((()=>{e&&(e[t]=o)}),[e,t,o])}function Pe(e,t,o){(0,n.useEffect)((()=>{if(e&&t&&o)return e.addEventListener(t,o),()=>e.removeEventListener(t,o)}),[e,t,o])}const Ne=n.createContext(null),_e={TOP_LEFT:["0%","0%"],TOP_CENTER:["50%","0%"],TOP:["50%","0%"],TOP_RIGHT:["100%","0%"],LEFT_CENTER:["0%","50%"],LEFT_TOP:["0%","0%"],LEFT:["0%","50%"],LEFT_BOTTOM:["0%","100%"],RIGHT_TOP:["100%","0%"],RIGHT:["100%","50%"],RIGHT_CENTER:["100%","50%"],RIGHT_BOTTOM:["100%","100%"],BOTTOM_LEFT:["0%","100%"],BOTTOM_CENTER:["50%","100%"],BOTTOM:["50%","100%"],BOTTOM_RIGHT:["100%","100%"],CENTER:["50%","50%"]},xe=({children:e,styles:t,className:o,anchorPoint:r})=>{const[a,l]=null!=r?r:_e.BOTTOM;let i=`-${a}`,s=`-${l}`;a.trimStart().startsWith("-")&&(i=a.substring(1)),l.trimStart().startsWith("-")&&(s=l.substring(1));const c=`translate(50%, 100%) translate(${i}, ${s})`;return n.createElement("div",{style:{transform:c}},n.createElement("div",{className:o,style:t},e))};function Re(e){const[t,o]=(0,n.useState)(null),[r,a]=(0,n.useState)(null),l=Oe(),i=Le("marker"),{children:s,onClick:c,className:u,onMouseEnter:d,onMouseLeave:p,onDrag:m,onDragStart:g,onDragEnd:f,collisionBehavior:h,clickable:v,draggable:y,position:b,title:C,zIndex:E}=e,w=n.Children.count(s);return(0,n.useEffect)((()=>{if(!l||!i)return;const e=new i.AdvancedMarkerElement;e.map=l,o(e);let t=null;return w>0&&(t=document.createElement("div"),t.isCustomMarker=!0,e.content=t,a(t)),()=>{var n;e.map=null,null==(n=t)||n.remove(),o(null),a(null)}}),[l,i,w]),(0,n.useEffect)((()=>{!t||!t.content||w>0||(t.content.className=u||"")}),[t,u,w]),Te(t,"position",b),Te(t,"title",null!=C?C:""),Te(t,"zIndex",E),Te(t,"collisionBehavior",h),(0,n.useEffect)((()=>{t&&(t.gmpDraggable=void 0!==y?y:!!(m||g||f))}),[t,y,m,f,g]),(0,n.useEffect)((()=>{if(!t)return;const e=void 0!==v||Boolean(c)||Boolean(d)||Boolean(p);t.gmpClickable=e,e&&null!=t&&t.content&&t.content.nodeType===Node.ELEMENT_NODE&&(t.content.style.pointerEvents="none",t.content.firstElementChild&&(t.content.firstElementChild.style.pointerEvents="all"))}),[t,v,c,d,p]),Ie(t,"click",c),Ie(t,"drag",m),Ie(t,"dragstart",g),Ie(t,"dragend",f),Pe(null==t?void 0:t.element,"mouseenter",d),Pe(null==t?void 0:t.element,"mouseleave",p),[t,r]}const Ae=(0,n.forwardRef)(((e,t)=>{const{children:o,style:r,className:a,anchorPoint:l}=e,[i,s]=Re(e),c=(0,n.useMemo)((()=>i?{marker:i}:null),[i]);return(0,n.useImperativeHandle)(t,(()=>i),[i]),s?n.createElement(Ne.Provider,{value:c},(0,U.createPortal)(n.createElement(xe,{anchorPoint:l,styles:r,className:a},o),s)):null}));function De(e,t,n){const o=0===t.indexOf("--");var r;null==n||"boolean"==typeof n||""===n?o?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":o?e.setProperty(t,n):"number"!=typeof n||0===n||(r=t,ze.has(r))?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}const ze=new Set(["animationIterationCount","aspectRatio","borderImageOutset","borderImageSlice","borderImageWidth","boxFlex","boxFlexGroup","boxOrdinalGroup","columnCount","columns","flex","flexGrow","flexPositive","flexShrink","flexNegative","flexOrder","gridArea","gridRow","gridRowEnd","gridRowSpan","gridRowStart","gridColumn","gridColumnEnd","gridColumnSpan","gridColumnStart","fontWeight","lineClamp","lineHeight","opacity","order","orphans","scale","tabSize","widows","zIndex","zoom","fillOpacity","floodOpacity","stopOpacity","strokeDasharray","strokeDashoffset","strokeMiterlimit","strokeOpacity","strokeWidth"]);const Fe=["children","headerContent","style","className","pixelOffset","anchor","shouldFocus","onClose","onCloseClick"],Be=e=>{const{children:t,headerContent:o,style:r,className:a,pixelOffset:l,anchor:i,shouldFocus:s,onClose:c,onCloseClick:u}=e,d=K(e,Fe),p=Le("maps"),[m,g]=(0,n.useState)(null),f=(0,n.useRef)(null),h=(0,n.useRef)(null);(0,n.useEffect)((()=>{if(!p)return;f.current=document.createElement("div"),h.current=document.createElement("div");const e=d;l&&(e.pixelOffset=new google.maps.Size(l[0],l[1])),o&&(e.headerContent="string"==typeof o?o:h.current);const t=new google.maps.InfoWindow(d);return t.setContent(f.current),g(t),()=>{var e,n;t.setContent(null),null==(e=f.current)||e.remove(),null==(n=h.current)||n.remove(),f.current=null,h.current=null,g(null)}}),[p]);const v=(0,n.useRef)(null);(0,n.useEffect)((()=>{m&&f.current&&(!function(e,t,n){if(null!=t&&"object"!=typeof t)throw new Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");const o=e.style;if(null!=n){for(const e in n)!n.hasOwnProperty(e)||null!=t&&t.hasOwnProperty(e)||(0===e.indexOf("--")?o.setProperty(e,""):"float"===e?o.cssFloat="":o[e]="");if(null!=t)for(const e in t){const r=t[e];t.hasOwnProperty(e)&&n[e]!==r&&De(o,e,r)}}else{if(null==t)return;for(const e in t)t.hasOwnProperty(e)&&De(o,e,t[e])}}(f.current,r||null,v.current),v.current=r||null,a!==f.current.className&&(f.current.className=a||""))}),[m,a,r]),de((()=>{if(!m)return;const e=d;e.pixelOffset=l?new google.maps.Size(l[0],l[1]):null,e.headerContent=o?"string"==typeof o?o:h.current:null,m.setOptions(d)}),[d,l,o]),Ie(m,"close",c),Ie(m,"closeclick",u);const y=Oe();return de((()=>{if(!y||!m||null===i)return;const e=!!i,t={map:y};if(i&&(t.anchor=i,void 0!==i.content&&i.content instanceof Element)){const e=i.content,t=null==e?void 0:e.getBoundingClientRect();if(t&&null!=e&&e.isCustomMarker){var n;const e=null==(n=i.content.firstElementChild)?void 0:n.firstElementChild,o=null==e?void 0:e.getBoundingClientRect(),r=o.x-t.x+(o.width-t.width)/2,a=o.y-t.y,s=d;s.pixelOffset=new google.maps.Size(l?l[0]+r:r,l?l[1]+a:a),m.setOptions(s)}}return void 0!==s&&(t.shouldFocus=s),m.open(t),()=>{e&&m.set("anchor",null),m.close()}}),[m,i,y,s,d,l]),n.createElement(n.Fragment,null,f.current&&(0,U.createPortal)(t,f.current),null!==h.current&&(0,U.createPortal)(o,h.current))};const je=["onClick","onDrag","onDragStart","onDragEnd","onMouseOver","onMouseOut"];(0,n.forwardRef)(((e,t)=>{const o=function(e){const[t,o]=(0,n.useState)(null),r=Oe(),{onClick:a,onDrag:l,onDragStart:i,onDragEnd:s,onMouseOver:c,onMouseOut:u}=e,d=K(e,je),{position:p,draggable:m}=d;return(0,n.useEffect)((()=>{if(!r)return void(void 0===r&&console.error("<Marker> has to be inside a Map component."));const e=new google.maps.Marker(d);return e.setMap(r),o(e),()=>{e.setMap(null),o(null)}}),[r]),(0,n.useEffect)((()=>{if(!t)return;const e=t,n=google.maps.event;return a&&n.addListener(e,"click",a),l&&n.addListener(e,"drag",l),i&&n.addListener(e,"dragstart",i),s&&n.addListener(e,"dragend",s),c&&n.addListener(e,"mouseover",c),u&&n.addListener(e,"mouseout",u),t.setDraggable(Boolean(m)),()=>{n.clearInstanceListeners(e)}}),[t,m,a,l,i,s,c,u]),(0,n.useEffect)((()=>{t&&d&&t.setOptions(d)}),[t,d]),(0,n.useEffect)((()=>{!m&&p&&t&&t.setPosition(p)}),[m,p,t]),t}(e);return(0,n.useImperativeHandle)(t,(()=>o),[o]),n.createElement(n.Fragment,null)}));var Ge="kNBbt1",Ze="FkxTlw",He="s0p_l4",We="pWx9TX",Ue="ELGmg6";const Je="97492c268b484efea51bbdb0",Ve="CLASSIC",Ke="SNOWWHITE",$e="GRAYSCALE",qe="SATELLITE",Ye={[Ve]:Je,[Ke]:"97492c268b484efca25c0674",[$e]:"97492c268b484eff57e02cb3",[qe]:Je},Qe={[Ve]:"ROADMAP",[Ke]:"ROADMAP",[$e]:"ROADMAP",[qe]:"HYBRID"},Xe="marker-image",et="marker-svg",tt="marker-link",nt="marker-directions-link",ot=e=>0===e||!!e,rt=e=>{var o;let{location:r,infoWindowOpen:a,onMarkerClick:l,pinIconUrl:i,showDirectionLink:s,allyTargetBlankDescription:c}=e;const[u,d]=function(){const[e,t]=(0,n.useState)(null);return[(0,n.useCallback)((e=>{t(e)}),[]),e]}(),[p,m]=n.useState(a),g=(e=>{if(!e||!e.address)return;const t=new URL("https://www.google.com/maps/dir/");return t.searchParams.set("api","1"),t.searchParams.set("destination",e.address),t.toString()})(r),{isLinkValid:f,anchorProps:h}=(e=>{const t=null==e?void 0:e.locationLinkAttributes,n=t&&"object"==typeof t&&"href"in t&&"string"==typeof t.href&&t.href,o=n?{href:t.href,..."target"in t&&"string"==typeof t.target&&t.target?{target:t.target}:{},..."rel"in t&&"string"==typeof t.rel&&t.rel?{rel:t.rel}:{}}:{};return{isLinkValid:Boolean(n),anchorProps:o}})(r);if(!r||!ot(r.longitude)||!ot(r.latitude))return null;const v={lat:Number(r.latitude),lng:Number(r.longitude)},y=n.createElement("img",{src:r.pinIcon,alt:"","data-testid":Xe}),b=n.createElement("div",{className:We,"aria-hidden":!0},n.createElement("svg",{width:"100%",height:"100%",viewBox:"0 0 64 64","data-testid":et},n.createElement("path",{d:r.pinIcon||"",fill:r.pinColor||"none"}))),C=n.createElement("div",null,r.description&&n.createElement("p",{className:Ze},r.description),r.linkTitle&&f&&n.createElement("a",t()({className:He},h,{"data-testid":tt}),r.linkTitle,"_blank"===(null==(o=r.locationLinkAttributes)?void 0:o.target)&&c&&n.createElement("span",{className:Ue},c)),s&&g&&n.createElement("a",{href:g,target:"_blank",rel:"noopener noreferrer",className:He,"data-testid":nt,"aria-label":r.directionTitle+". "+c},r.directionTitle));return n.createElement(n.Fragment,null,n.createElement(Ae,{ref:u,position:v,title:r.title,onClick:()=>{var e;m((e=>!e)),null==l||l({type:"markerClicked",address:r.address,location:{latitude:r.latitude||0,longitude:r.longitude||0},icon:r.pinIcon||"",link:null==(e=r.locationLinkAttributes)?void 0:e.href,title:r.title,linkTitle:r.linkTitle,description:r.description})}},((e,t)=>{if(!e)return!1;const{mediaRootUrl:n,fileRepoUrl:o}=t;return e.startsWith(n)||e.startsWith(o)})(r.pinIcon,i)?y:b),p&&d&&n.createElement(Be,{anchor:d,maxWidth:300,minWidth:228,headerContent:r.title&&n.createElement("h2",{className:Ge},r.title),onClose:()=>m(!1),disableAutoPan:!0},C))};var at=n.memo(rt);const lt=(e,t,n)=>{const o=((e,t)=>({setMapCenter:(n,o)=>{const r={lat:o,lng:n};return e.current&&(e.current.setCenter(r),t&&t(r,e.current.getZoom()||14)),Promise.resolve()},setMapZoom:n=>{if(e.current&&(e.current.setZoom(n),t)){const o=e.current.getCenter();o&&t({lat:o.lat(),lng:o.lng()},n)}return Promise.resolve()},fitBounds:n=>{if(e.current){const o=new google.maps.LatLngBounds({lat:n.south,lng:n.west},{lat:n.north,lng:n.east});if(e.current.fitBounds(o),t){const n=e.current.getCenter(),o=e.current.getZoom();n&&void 0!==o&&t({lat:n.lat(),lng:n.lng()},o)}}return Promise.resolve()},setMarkers:e=>Promise.resolve(),getVisibleMarkers:t=>{if(e.current){const n=e.current.getBounds();if(n){const e=t.filter((e=>{const t=new google.maps.LatLng(e.latitude,e.longitude);return n.contains(t)})).map((e=>({title:e.title,location:{latitude:e.latitude,longitude:e.longitude}})));return Promise.resolve(e)}}return Promise.resolve(t.map((e=>({title:e.title,location:{latitude:e.latitude,longitude:e.longitude}}))))},setMarkerIcon:e=>Promise.resolve()}))(e,t);return{setMapCenter:(e,t)=>o.setMapCenter(e,t),setMapZoom:e=>o.setMapZoom(e),fitBounds:e=>o.fitBounds(e),setMarkers:e=>o.setMarkers(e),setMarkerIcon:e=>o.setMarkerIcon(e),getVisibleMarkers:()=>o.getVisibleMarkers(n),getCenter:()=>{if(e.current&&e.current.getCenter){const t=e.current.getCenter();if(t)return{lat:()=>t.lat(),lng:()=>t.lng()}}return{lat:()=>0,lng:()=>0}},getZoom:()=>{if(e.current&&e.current.getZoom){const t=e.current.getZoom();return void 0!==t?t:14}return 14}}};var it={mapContainer:"zf7Baq"};const st=(e,o)=>{const{id:r,className:a,customClassNames:i=[],skin:c,mapData:d,translations:m,isConsentPolicyActive:f,onUpdateCenter:h,onUpdateZoom:v,onMarkerClicked:y,onMapClicked:b,onMouseEnter:C,onMouseLeave:E,language:w,onMapMount:M,lang:k,pinIconUrl:S}=e,O=Oe(),L=n.useRef(O),[I,T]=n.useState((()=>{var e,t,n,o;return{center:{lat:null!=(e=null==(t=d.locations[0])?void 0:t.latitude)?e:0,lng:null!=(n=null==(o=d.locations[0])?void 0:o.longitude)?n:0},zoom:d.zoom||14}})),P=n.useCallback(((e,t)=>{T((n=>n.center.lat!==e.lat||n.center.lng!==e.lng||n.zoom!==t?{center:e,zoom:t}:n))}),[]),N=n.useCallback((e=>{try{const t=e.detail.center,n=e.detail.zoom;t&&void 0!==n&&(P(t,n),null==h||h({latitude:t.lat,longitude:t.lng}),null==v||v({zoom:n}))}catch(e){console.error("GoogleMap: Camera change handler error",{error:e instanceof Error?e.message:String(e),componentId:r})}}),[P,h,v,r]),_=n.useCallback((e=>{try{b&&e.detail.latLng&&b({type:"mapClicked",location:{latitude:e.detail.latLng.lat,longitude:e.detail.latLng.lng}})}catch(e){console.error("GoogleMap: Click handler error",{error:e instanceof Error?e.message:String(e),componentId:r})}}),[b,r]);n.useImperativeHandle(o,(()=>lt(L,P,d.locations)),[P,d.locations]),n.useEffect((()=>{null==M||M()}),[M]);const x=n.useMemo((()=>{const e=d.mapStyle;return{mapId:Ye[e]||Ye.CLASSIC,mapTypeId:(Qe[e]||Qe.CLASSIC).toLowerCase()}}),[d.mapStyle]),R=n.useMemo((()=>({zoom:d.zoom,fullscreenControl:!1,clickableIcons:!0,keyboardShortcuts:!0,disableDoubleClickZoom:!1,disableDefaultUI:!0,gestureHandling:d.mapInteractive?"auto":"none",mapTypeControl:d.showMapType,zoomControl:d.showZoom,streetViewControl:d.showStreetView,...x})),[d.zoom,d.mapInteractive,d.showMapType,d.showZoom,d.showStreetView,x]),A={id:r,className:l(it[c],a,u(g.root,...i)),...s(e),lang:k},D=p(f,A);return n.createElement("div",t()({},D,{onMouseEnter:C,onMouseLeave:E}),n.createElement("div",{className:it.mapContainer,"aria-label":m.title,role:"region"},n.createElement(ae,{apiKey:"AIzaSyCu6mU4lWgbxynxwy3yYJZcEZKO0QYnFu4",language:w},n.createElement(Me,t()({},R,{onCameraChanged:N,onClick:_,center:I.center,style:{width:"100%",height:"100%"}}),d.locations.map(((e,t)=>n.createElement(at,{key:t+"-"+e.latitude+"-"+e.longitude,location:e,infoWindowOpen:0===t,onMarkerClick:y,pinIconUrl:S,showDirectionLink:d.showDirectionsLink,allyTargetBlankDescription:m.allyTargetBlankDescription})))))))};var ct=n.memo(n.forwardRef(st));const ut=(e,o)=>{const{isReactGoogleMap:r,...a}=e;return r?n.createElement(ct,t()({},a,{ref:o})):n.createElement(W,t()({},a,{ref:o}))};var dt;const pt={GoogleMap:{component:n.forwardRef(ut),controller:(dt=e=>{let{stateValues:t,mapperProps:n}=e;return{...n,...m(t)}},{useComponentProps:(e,t,n)=>{const o=(e=>({...e,updateStyles:t=>{const n=Object.entries(t).reduce(((e,[t,n])=>{return{...e,[(o=t,o.startsWith("--")?t:d(t))]:void 0===n?null:n};var o}),{});e.updateStyles(n)}}))(n);return dt({mapperProps:e,stateValues:t,controllerUtils:o})}})}}}(),a}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[GoogleMap].232ecb88.bundle.min.js.map