//esm-shim-webpack-plugin-imports
/*! For license information please see editorSDK.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.editorSDK=t():e.editorSDK=t()}("undefined"!=typeof self?self:this,(()=>(()=>{var e={88:(e,t,n)=>{var o=n(4700);e.exports=function(e){return o(this,e).get(e)}},125:(e,t,n)=>{var o=n(9032),r=n(5288);e.exports=function(e,t){return e&&o(t,r(t),e)}},155:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},195:(e,t,n)=>{var o=n(4882),r=n(8121),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return r(e);var t=[];for(var n in Object(e))i.call(e,n)&&"constructor"!=n&&t.push(n);return t}},358:(e,t,n)=>{var o=n(6137),r=n(3283),i=n(3142),a=n(5853),c=n(9632),s=n(8666),u=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=i(e),p=!n&&r(e),d=!n&&!p&&a(e),f=!n&&!p&&!d&&s(e),l=n||p||d||f,g=l?o(e.length,String):[],m=g.length;for(var T in e)!t&&!u.call(e,T)||l&&("length"==T||d&&("offset"==T||"parent"==T)||f&&("buffer"==T||"byteLength"==T||"byteOffset"==T)||c(T,m))||g.push(T);return g}},393:(e,t,n)=>{var o=n(8244),r=n(7979),i=n(1211);e.exports=function(e){return o(e,i,r)}},424:(e,t,n)=>{var o=n(8898),r=n(2480),i=n(8340),a=n(8069),c=n(2264);e.exports=function(e,t,n){var s=e.constructor;switch(t){case"[object ArrayBuffer]":return o(e);case"[object Boolean]":case"[object Date]":return new s(+e);case"[object DataView]":return r(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return c(e,n);case"[object Map]":case"[object Set]":return new s;case"[object Number]":case"[object String]":return new s(e);case"[object RegExp]":return i(e);case"[object Symbol]":return a(e)}}},547:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},674:(e,t,n)=>{var o=n(7379),r=n(5387),i=n(547),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&r(e.length)&&!!a[o(e)]}},777:(e,t,n)=>{var o=n(8486),r=n(547);e.exports=function(e){return r(e)&&"[object Map]"==o(e)}},878:(e,t)=>{"use strict";t.Lm=t.xG=t.wv=void 0,t.yy=t.m2=t.hQ=t.W=t.OE=t.oQ=t.nx=t.t0=t.SP=t.Ys=void 0,t.wv="14cc59bc-f0b7-15b8-e1c7-89ce41d0e0c9",t.xG="b976560c-3122-4351-878f-453f337b7245",t.Lm="14f25924-5664-31b2-9568-f9c5ed98c9b1",t.Ys="2bef2abe-7abe-43da-889c-53c1500a328c",t.SP="14dbef06-cc42-5583-32a7-3abd44da4908",t.t0="14cffd81-5215-0a7f-22f8-074b0e2401fb",t.nx="14ce28f7-7eb0-3745-22f8-074b0e2401fb",t.oQ="cffc6740-8042-48cc-a35b-d3fd03a69f0c",t.OE="14ebe801-d78a-daa9-c9e5-0286a891e46f",t.W="16ed1ac6-01cb-4fb6-a59e-c215cce8fdf6",t.hQ="4aebd0cb-fbdb-4da7-b5d1-d05660a30172",t.m2="1505b775-e885-eb1b-b665-1e485d9bf90e",t.yy="14f25dc5-6af3-5420-9568-f9c5ed98c9b1"},1112:(e,t,n)=>{var o=n(4497),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(o){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return r.call(t,e)?t[e]:void 0}},1129:e=>{e.exports=function(e,t){for(var n=-1,o=t.length,r=e.length;++n<o;)e[r+n]=t[n];return e}},1211:(e,t,n)=>{var o=n(358),r=n(195),i=n(6529);e.exports=function(e){return i(e)?o(e):r(e)}},1340:(e,t,n)=>{var o=n(1386),r=n(4103),i=n(1779),a=n(4162),c=n(7462),s=n(6638);function u(e){var t=this.__data__=new o(e);this.size=t.size}u.prototype.clear=r,u.prototype.delete=i,u.prototype.get=a,u.prototype.has=c,u.prototype.set=s,e.exports=u},1386:(e,t,n)=>{var o=n(2393),r=n(2049),i=n(7144),a=n(7452),c=n(3964);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}s.prototype.clear=o,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},1580:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},1623:(e,t,n)=>{var o=n(8942).Uint8Array;e.exports=o},1779:e=>{e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},1935:(e,t,n)=>{var o=n(8486),r=n(547);e.exports=function(e){return r(e)&&"[object Set]"==o(e)}},1950:(e,t,n)=>{var o=n(8942)["__core-js_shared__"];e.exports=o},2049:(e,t,n)=>{var o=n(7034),r=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=o(t,e);return!(n<0||(n==t.length-1?t.pop():r.call(t,n,1),--this.size,0))}},2264:(e,t,n)=>{var o=n(8898);e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}},2306:(e,t,n)=>{e=n.nmd(e);var o=n(4967),r=t&&!t.nodeType&&t,i=r&&e&&!e.nodeType&&e,a=i&&i.exports===r&&o.process,c=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c},2393:e=>{e.exports=function(){this.__data__=[],this.size=0}},2480:(e,t,n)=>{var o=n(8898);e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}},2532:(e,t,n)=>{var o=n(4715),r=function(){try{var e=o(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=r},3103:(e,t,n)=>{var o=n(4715)(n(8942),"DataView");e.exports=o},3142:e=>{var t=Array.isArray;e.exports=t},3283:(e,t,n)=>{var o=n(6027),r=n(547),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,s=o(function(){return arguments}())?o:function(e){return r(e)&&a.call(e,"callee")&&!c.call(e,"callee")};e.exports=s},3305:(e,t,n)=>{var o=n(4497);e.exports=function(){this.__data__=o?o(null):{},this.size=0}},3422:(e,t,n)=>{var o=n(7073),r=n(6285),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,n){var a=e[t];i.call(e,t)&&r(a,n)&&(void 0!==n||t in e)||o(e,t,n)}},3546:(e,t,n)=>{var o=n(9032),r=n(7979);e.exports=function(e,t){return o(e,r(e),t)}},3650:(e,t,n)=>{var o=n(8244),r=n(5832),i=n(5288);e.exports=function(e){return o(e,i,r)}},3655:(e,t,n)=>{var o=n(7379),r=n(1580);e.exports=function(e){if(!r(e))return!1;var t=o(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},3766:e=>{e.exports=function(e,t){return function(n){return e(t(n))}}},3943:(e,t,n)=>{var o=n(1935),r=n(9460),i=n(2306),a=i&&i.isSet,c=a?r(a):o;e.exports=c},3964:(e,t,n)=>{var o=n(7034);e.exports=function(e,t){var n=this.__data__,r=o(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}},4066:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},4103:(e,t,n)=>{var o=n(1386);e.exports=function(){this.__data__=new o,this.size=0}},4162:e=>{e.exports=function(e){return this.__data__.get(e)}},4354:e=>{e.exports=function(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t}},4497:(e,t,n)=>{var o=n(4715)(Object,"create");e.exports=o},4512:(e,t,n)=>{var o=n(4715)(n(8942),"Set");e.exports=o},4700:(e,t,n)=>{var o=n(9067);e.exports=function(e,t){var n=e.__data__;return o(t)?n["string"==typeof t?"string":"hash"]:n.map}},4715:(e,t,n)=>{var o=n(9624),r=n(155);e.exports=function(e,t){var n=r(e,t);return o(n)?n:void 0}},4732:(e,t,n)=>{var o=n(4700);e.exports=function(e){return o(this,e).has(e)}},4733:(e,t,n)=>{var o=n(9032),r=n(5832);e.exports=function(e,t){return o(e,r(e),t)}},4759:(e,t,n)=>{var o,r=n(1950),i=(o=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||""))?"Symbol(src)_1."+o:"";e.exports=function(e){return!!i&&i in e}},4772:e=>{e.exports=function(){return!1}},4784:(e,t,n)=>{var o=n(3766)(Object.getPrototypeOf,Object);e.exports=o},4882:e=>{var t=Object.prototype;e.exports=function(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}},4967:(e,t,n)=>{var o="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=o},5071:(e,t,n)=>{var o=n(4497);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=o&&void 0===t?"__lodash_hash_undefined__":t,this}},5098:(e,t,n)=>{var o=n(3305),r=n(9361),i=n(1112),a=n(5276),c=n(5071);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}s.prototype.clear=o,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},5141:function(e,t,n){"undefined"!=typeof self?self:void 0!==n.g&&n.g,e.exports=function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:o})},n.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=137)}([function(e,t,n){var o=n(47),r="object"==typeof self&&self&&self.Object===Object&&self,i=o||r||Function("return this")();e.exports=i},function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},function(e,t,n){var o=n(124),r=n(121);e.exports=function(e,t){var n=r(e,t);return o(n)?n:void 0}},function(e,t,n){var o=n(13),r=n(132),i=n(131),a=o?o.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?r(e):i(e)}},function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},function(e,t){var n=Array.isArray;e.exports=n},function(e,t,n){var o=n(26),r=n(41);e.exports=function(e,t,n,i){var a=!n;n||(n={});for(var c=-1,s=t.length;++c<s;){var u=t[c],p=i?i(n[u],e[u],u,n,e):void 0;void 0===p&&(p=e[u]),a?r(n,u,p):o(n,u,p)}return n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={INVOKE:"invoke",RPC_RESOLVE:"rpc-resolve",RPC_REJECT:"rpc-reject",API_DESCRIPTION:"api-desc",INVOKE_FUNCTION:"invoke-func",RESOLVE:"resolve",REJECT:"reject",REQUEST_API:"request-api"}},function(e,t,n){var o=n(110);e.exports=function(e,t){var n=e.__data__;return o(t)?n["string"==typeof t?"string":"hash"]:n.map}},function(e,t,n){var o=n(44);e.exports=function(e,t){for(var n=e.length;n--;)if(o(e[n][0],t))return n;return-1}},function(e,t,n){var o=n(116),r=n(115),i=n(114),a=n(113),c=n(112);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}s.prototype.clear=o,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},function(e,t,n){var o=n(3),r=n(4);e.exports=function(e){if(!r(e))return!1;var t=o(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},function(e,t,n){var o=n(2)(Object,"create");e.exports=o},function(e,t,n){var o=n(0).Symbol;e.exports=o},function(e,t,n){var o=n(135);e.exports=function(e,t,n){var r=null==e?void 0:o(e,t);return void 0===r?n:r}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.deserialize=t.serialize=void 0;var o=i(n(14)),r=i(n(52));function i(e){return e&&e.__esModule?e:{default:e}}t.serialize=function(e){return Promise.reject((0,r.default)(e)?function(e){return{type:e.constructor.name,message:e.message,stack:e.stack}}(e):e)},t.deserialize=function(e){return function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).type;return"Error"===e||(0,r.default)((0,o.default)(globalThis,[e,"prototype"]))}(e)?function(e){var t=e.type,n=e.stack,o=e.message,r=new(globalThis[t]||Error)(o);return r.stack=n,r}(e):e}},function(e,t,n){var o=n(66);e.exports=function(e){var t=new e.constructor(e.byteLength);return new o(t).set(new o(e)),t}},function(e,t,n){var o=n(72),r=n(28),i=n(71),a=n(70),c=n(69),s=n(3),u=n(45),p=u(o),d=u(r),f=u(i),l=u(a),g=u(c),m=s;(o&&"[object DataView]"!=m(new o(new ArrayBuffer(1)))||r&&"[object Map]"!=m(new r)||i&&"[object Promise]"!=m(i.resolve())||a&&"[object Set]"!=m(new a)||c&&"[object WeakMap]"!=m(new c))&&(m=function(e){var t=s(e),n="[object Object]"==t?e.constructor:void 0,o=n?u(n):"";if(o)switch(o){case p:return"[object DataView]";case d:return"[object Map]";case f:return"[object Promise]";case l:return"[object Set]";case g:return"[object WeakMap]"}return t}),e.exports=m},function(e,t,n){var o=n(36)(Object.getPrototypeOf,Object);e.exports=o},function(e,t,n){var o=n(76),r=n(34),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(e){return null==e?[]:(e=Object(e),o(a(e),(function(t){return i.call(e,t)})))}:r;e.exports=c},function(e,t,n){var o=n(40),r=n(81),i=n(35);e.exports=function(e){return i(e)?o(e,!0):r(e)}},function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},function(e,t,n){(function(e){var o=n(47),r="object"==typeof t&&t&&!t.nodeType&&t,i=r&&"object"==typeof e&&e&&!e.nodeType&&e,a=i&&i.exports===r&&o.process,c=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c}).call(this,n(24)(e))},function(e,t){e.exports=function(e){return function(t){return e(t)}}},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){var o=n(40),r=n(84),i=n(35);e.exports=function(e){return i(e)?o(e):r(e)}},function(e,t,n){var o=n(41),r=n(44),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,n){var a=e[t];i.call(e,t)&&r(a,n)&&(void 0!==n||t in e)||o(e,t,n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sendResponse=t.send=void 0;var o=n(42),r=function(e){return e&&e.__esModule?e:{default:e}}(n(101));function i(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function a(e,t,n,r){(0,o.isWebWorker)()||e instanceof Worker||e instanceof MessagePort?e.postMessage(t,r):e.postMessage(t,n,r)}t.send=function(e,t){var n=t.target,c=t.targetOrigin,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return new Promise((function(t){var u=new MessageChannel,p=u.port1,d=u.port2,f=function e(n){var r=n.data;t(r),(0,o.isWebWorker)()?p.onmessage=null:p.removeEventListener("message",e)};(0,o.isWebWorker)()||(0,o.isBrowser)()?p.onmessage=f:p.addEventListener("message",f),e.__port=d;try{a(n,e,c,[d].concat(i(s)))}catch(t){if(!t||"DataCloneError"!==t.name)throw t;var l=(0,r.default)(e);l.__port=d,a(n,l,c,[d].concat(i(s)))}}))},t.sendResponse=function(e,t){return function(n){return e.postMessage({intent:t,result:n})}}},function(e,t,n){var o=n(2)(n(0),"Map");e.exports=o},function(e,t,n){var o=n(3),r=n(1);e.exports=function(e){return"symbol"==typeof e||r(e)&&"[object Symbol]"==o(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){return e&&e.__esModule?e:{default:e}}(n(7)),r=n(15);t.default=function(e){var t=e.intent,n=e.result;switch(t){case o.default.RESOLVE:return n;case o.default.REJECT:return Promise.reject((0,r.deserialize)(n));default:return Promise.reject()}}},function(e,t,n){var o=n(32),r=n(5);e.exports=function(e,t,n){var i=t(e);return r(e)?i:o(i,n(e))}},function(e,t){e.exports=function(e,t){for(var n=-1,o=t.length,r=e.length;++n<o;)e[r+n]=t[n];return e}},function(e,t,n){var o=n(32),r=n(18),i=n(19),a=n(34),c=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)o(t,i(e)),e=r(e);return t}:a;e.exports=c},function(e,t){e.exports=function(){return[]}},function(e,t,n){var o=n(11),r=n(37);e.exports=function(e){return null!=e&&r(e.length)&&!o(e)}},function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},function(e,t){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},function(e,t){var n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var o=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==o||"symbol"!=o&&n.test(e))&&e>-1&&e%1==0&&e<t}},function(e,t,n){(function(e){var o=n(0),r=n(87),i="object"==typeof t&&t&&!t.nodeType&&t,a=i&&"object"==typeof e&&e&&!e.nodeType&&e,c=a&&a.exports===i?o.Buffer:void 0,s=(c?c.isBuffer:void 0)||r;e.exports=s}).call(this,n(24)(e))},function(e,t,n){var o=n(90),r=n(89),i=n(5),a=n(39),c=n(38),s=n(86),u=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=i(e),p=!n&&r(e),d=!n&&!p&&a(e),f=!n&&!p&&!d&&s(e),l=n||p||d||f,g=l?o(e.length,String):[],m=g.length;for(var T in e)!t&&!u.call(e,T)||l&&("length"==T||d&&("offset"==T||"parent"==T)||f&&("buffer"==T||"byteLength"==T||"byteOffset"==T)||c(T,m))||g.push(T);return g}},function(e,t,n){var o=n(92);e.exports=function(e,t,n){"__proto__"==t&&o?o(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isWebWorker=function(){return"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope},t.isBrowser=function(){return"undefined"!=typeof window},t.getChildFrameById=function(e){return document.getElementById(e)}},function(e,t,n){var o=n(29),r=1/0;e.exports=function(e){if("string"==typeof e||o(e))return e;var t=e+"";return"0"==t&&1/e==-r?"-0":t}},function(e,t){e.exports=function(e,t){return e===t||e!=e&&t!=t}},function(e,t){var n=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return n.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},function(e,t,n){var o=n(127),r=n(111),i=n(109),a=n(108),c=n(107);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}s.prototype.clear=o,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},function(e,t,n){(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e.exports=n}).call(this,n(133))},function(e,t,n){var o=n(5),r=n(134),i=n(130),a=n(106);e.exports=function(e,t){return o(e)?e:r(e,t)?[e]:i(a(e))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTargetInfoFromDef=void 0;var o=n(42);t.getTargetInfoFromDef=function(e){var t=e.target,n=e.initiator;switch(!0){case"undefined"!=typeof MessagePort&&t instanceof MessagePort:case"undefined"!=typeof Worker&&t instanceof Worker:return{target:t};case"undefined"!=typeof parent&&t===parent:return{target:parent,targetOrigin:"*"};case Boolean(t):return t.contentWindow?{target:t.contentWindow,targetOrigin:t.src}:{target:t,targetOrigin:"*"};case(0,o.isWebWorker)():return{target:self,targetOrigin:"*"};case Boolean(n):var r=(0,o.getChildFrameById)(n);return{target:r.contentWindow,targetOrigin:r.src};default:throw new Error("Invalid target")}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=!1;t.addSingleHandler=function(e,t){o||(o=!0,self.addEventListener("message",e)),t&&t.forEach((function(t){return t.addEventListener("message",e)}))},t.removeSingleHandler=function(e){self.removeEventListener("message",e),o=!1}},function(e,t,n){var o=n(3),r=n(18),i=n(1),a=Function.prototype,c=Object.prototype,s=a.toString,u=c.hasOwnProperty,p=s.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=o(e))return!1;var t=r(e);if(null===t)return!0;var n=u.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&s.call(n)==p}},function(e,t,n){var o=n(3),r=n(1),i=n(51);e.exports=function(e){if(!r(e))return!1;var t=o(e);return"[object Error]"==t||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!i(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.deserialize=t.serialize=void 0;var o=c(n(11)),r=n(27),i=c(n(7)),a=c(n(30));function c(e){return e&&e.__esModule?e:{default:e}}t.serialize=function(e){return e.reduce((function(e,t){var n=e.args,a=e.transfer;if((0,o.default)(t)){var c=new MessageChannel,s=c.port1,u=c.port2;s.onmessage=function(e){var n=e.data,o=n.__port;Promise.resolve(t.apply(void 0,function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(n))).then((0,r.sendResponse)(o,i.default.RESOLVE),(0,r.sendResponse)(o,i.default.REJECT))};var p=a.length;n.push({type:"function",index:p,port:u}),a.push(u)}else n.push({type:"value",value:t});return{args:n,transfer:a}}),{args:[],transfer:[]})},t.deserialize=function(e){return e.map((function(e){return"value"===e.type?e.value:function(){for(var t=e.port,n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return(0,r.send)(o,{target:t}).then(a.default)}}))}},function(e,t,n){var o=n(26),r=n(48),i=n(38),a=n(4),c=n(43);e.exports=function(e,t,n,s){if(!a(e))return e;for(var u=-1,p=(t=r(t,e)).length,d=p-1,f=e;null!=f&&++u<p;){var l=c(t[u]),g=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(u!=d){var m=f[l];void 0===(g=s?s(m,l,f):void 0)&&(g=a(m)?m:i(t[u+1])?[]:{})}o(f,l,g),f=f[l]}return e}},function(e,t,n){var o=n(54);e.exports=function(e,t,n){return null==e?e:o(e,t,n)}},function(e,t,n){var o=n(17),r=n(1);e.exports=function(e){return r(e)&&"[object Set]"==o(e)}},function(e,t,n){var o=n(56),r=n(23),i=n(22),a=i&&i.isSet,c=a?r(a):o;e.exports=c},function(e,t,n){var o=n(17),r=n(1);e.exports=function(e){return r(e)&&"[object Map]"==o(e)}},function(e,t,n){var o=n(58),r=n(23),i=n(22),a=i&&i.isMap,c=a?r(a):o;e.exports=c},function(e,t,n){var o=n(4),r=Object.create,i=function(){function e(){}return function(t){if(!o(t))return{};if(r)return r(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();e.exports=i},function(e,t,n){var o=n(60),r=n(18),i=n(21);e.exports=function(e){return"function"!=typeof e.constructor||i(e)?{}:o(r(e))}},function(e,t,n){var o=n(16);e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}},function(e,t,n){var o=n(13),r=o?o.prototype:void 0,i=r?r.valueOf:void 0;e.exports=function(e){return i?Object(i.call(e)):{}}},function(e,t){var n=/\w*$/;e.exports=function(e){var t=new e.constructor(e.source,n.exec(e));return t.lastIndex=e.lastIndex,t}},function(e,t,n){var o=n(16);e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}},function(e,t,n){var o=n(0).Uint8Array;e.exports=o},function(e,t,n){var o=n(16),r=n(65),i=n(64),a=n(63),c=n(62);e.exports=function(e,t,n){var s=e.constructor;switch(t){case"[object ArrayBuffer]":return o(e);case"[object Boolean]":case"[object Date]":return new s(+e);case"[object DataView]":return r(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return c(e,n);case"[object Map]":case"[object Set]":return new s;case"[object Number]":case"[object String]":return new s(e);case"[object RegExp]":return i(e);case"[object Symbol]":return a(e)}}},function(e,t){var n=Object.prototype.hasOwnProperty;e.exports=function(e){var t=e.length,o=new e.constructor(t);return t&&"string"==typeof e[0]&&n.call(e,"index")&&(o.index=e.index,o.input=e.input),o}},function(e,t,n){var o=n(2)(n(0),"WeakMap");e.exports=o},function(e,t,n){var o=n(2)(n(0),"Set");e.exports=o},function(e,t,n){var o=n(2)(n(0),"Promise");e.exports=o},function(e,t,n){var o=n(2)(n(0),"DataView");e.exports=o},function(e,t,n){var o=n(31),r=n(33),i=n(20);e.exports=function(e){return o(e,i,r)}},function(e,t,n){var o=n(31),r=n(19),i=n(25);e.exports=function(e){return o(e,i,r)}},function(e,t,n){var o=n(6),r=n(33);e.exports=function(e,t){return o(e,r(e),t)}},function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length,r=0,i=[];++n<o;){var a=e[n];t(a,n,e)&&(i[r++]=a)}return i}},function(e,t,n){var o=n(6),r=n(19);e.exports=function(e,t){return o(e,r(e),t)}},function(e,t){e.exports=function(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t}},function(e,t,n){(function(e){var o=n(0),r="object"==typeof t&&t&&!t.nodeType&&t,i=r&&"object"==typeof e&&e&&!e.nodeType&&e,a=i&&i.exports===r?o.Buffer:void 0,c=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,o=c?c(n):new e.constructor(n);return e.copy(o),o}}).call(this,n(24)(e))},function(e,t){e.exports=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}},function(e,t,n){var o=n(4),r=n(21),i=n(80),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return i(e);var t=r(e),n=[];for(var c in e)("constructor"!=c||!t&&a.call(e,c))&&n.push(c);return n}},function(e,t,n){var o=n(6),r=n(20);e.exports=function(e,t){return e&&o(t,r(t),e)}},function(e,t,n){var o=n(36)(Object.keys,Object);e.exports=o},function(e,t,n){var o=n(21),r=n(83),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return r(e);var t=[];for(var n in Object(e))i.call(e,n)&&"constructor"!=n&&t.push(n);return t}},function(e,t,n){var o=n(3),r=n(37),i=n(1),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&r(e.length)&&!!a[o(e)]}},function(e,t,n){var o=n(85),r=n(23),i=n(22),a=i&&i.isTypedArray,c=a?r(a):o;e.exports=c},function(e,t){e.exports=function(){return!1}},function(e,t,n){var o=n(3),r=n(1);e.exports=function(e){return r(e)&&"[object Arguments]"==o(e)}},function(e,t,n){var o=n(88),r=n(1),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,s=o(function(){return arguments}())?o:function(e){return r(e)&&a.call(e,"callee")&&!c.call(e,"callee")};e.exports=s},function(e,t){e.exports=function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}},function(e,t,n){var o=n(6),r=n(25);e.exports=function(e,t){return e&&o(t,r(t),e)}},function(e,t,n){var o=n(2),r=function(){try{var e=o(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=r},function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}},function(e,t,n){var o=n(10),r=n(28),i=n(46);e.exports=function(e,t){var n=this.__data__;if(n instanceof o){var a=n.__data__;if(!r||a.length<199)return a.push([e,t]),this.size=++n.size,this;n=this.__data__=new i(a)}return n.set(e,t),this.size=n.size,this}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t){e.exports=function(e){return this.__data__.get(e)}},function(e,t){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},function(e,t,n){var o=n(10);e.exports=function(){this.__data__=new o,this.size=0}},function(e,t,n){var o=n(10),r=n(98),i=n(97),a=n(96),c=n(95),s=n(94);function u(e){var t=this.__data__=new o(e);this.size=t.size}u.prototype.clear=r,u.prototype.delete=i,u.prototype.get=a,u.prototype.has=c,u.prototype.set=s,e.exports=u},function(e,t,n){var o=n(99),r=n(93),i=n(26),a=n(91),c=n(82),s=n(79),u=n(78),p=n(77),d=n(75),f=n(74),l=n(73),g=n(17),m=n(68),T=n(67),y=n(61),v=n(5),h=n(39),P=n(59),A=n(4),w=n(57),I=n(25),S=n(20),E="[object Arguments]",b="[object Function]",C="[object Object]",_={};_[E]=_["[object Array]"]=_["[object ArrayBuffer]"]=_["[object DataView]"]=_["[object Boolean]"]=_["[object Date]"]=_["[object Float32Array]"]=_["[object Float64Array]"]=_["[object Int8Array]"]=_["[object Int16Array]"]=_["[object Int32Array]"]=_["[object Map]"]=_["[object Number]"]=_[C]=_["[object RegExp]"]=_["[object Set]"]=_["[object String]"]=_["[object Symbol]"]=_["[object Uint8Array]"]=_["[object Uint8ClampedArray]"]=_["[object Uint16Array]"]=_["[object Uint32Array]"]=!0,_["[object Error]"]=_[b]=_["[object WeakMap]"]=!1,e.exports=function e(t,n,R,O,x,N){var j,D=1&n,M=2&n,G=4&n;if(R&&(j=x?R(t,O,x,N):R(t)),void 0!==j)return j;if(!A(t))return t;var W=v(t);if(W){if(j=m(t),!D)return u(t,j)}else{var Y=g(t),k=Y==b||"[object GeneratorFunction]"==Y;if(h(t))return s(t,D);if(Y==C||Y==E||k&&!x){if(j=M||k?{}:y(t),!D)return M?d(t,c(j,t)):p(t,a(j,t))}else{if(!_[Y])return x?t:{};j=T(t,Y,D)}}N||(N=new o);var U=N.get(t);if(U)return U;N.set(t,j),w(t)?t.forEach((function(o){j.add(e(o,n,R,o,t,N))})):P(t)&&t.forEach((function(o,r){j.set(r,e(o,n,R,r,t,N))}));var L=W?void 0:(G?M?l:f:M?S:I)(t);return r(L||t,(function(o,r){L&&(o=t[r=o]),i(j,r,e(o,n,R,r,t,N))})),j}},function(e,t,n){var o=n(100);e.exports=function(e){return o(e,5)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.invokeApiFunction=t.getDescription=t.buildApiFromDescription=void 0;var o=d(n(7)),r=n(27),i=d(n(14)),a=d(n(55)),c=d(n(11)),s=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(53)),u=n(15),p=d(n(30));function d(e){return e&&e.__esModule?e:{default:e}}function f(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}t.buildApiFromDescription=function(e,t,n){return Object.keys(t).sort().reduce((function(t,i){return(0,a.default)(t,i,function(e,t,n){return function(){for(var i=arguments.length,a=Array(i),c=0;c<i;c++)a[c]=arguments[c];var u=s.serialize(a),d=u.args,f=u.transfer;return(0,r.send)({appId:e,call:n,args:d,intent:o.default.INVOKE_FUNCTION},t,f).then(p.default)}}(e,n,i))}),{})};var l=function e(t,n){var o=function(e,t){return 0===t.length?e:(0,i.default)(e,t)}(n,t),r=Object.keys(o).reduce((function(o,r){return Object.assign(o,e([].concat(f(t),[r]),n))}),{});return Object.assign(function(e,t){return(0,c.default)(e)?function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},t.join("."),!0):{}}(o,t),r)};t.getDescription=function(e){return l([],e)},t.invokeApiFunction=function(e,t){try{var n=s.deserialize(t);return Promise.resolve(e.apply(void 0,f(n))).catch(u.serialize)}catch(e){return(0,u.serialize)(e)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isEmpty=t.unregisterApp=t.hasApp=t.getAppData=t.getAppById=t.registerApp=void 0;var o=function(e){return e&&e.__esModule?e:{default:e}}(n(14)),r={};t.registerApp=function(e,t,n,o){r[e]={app:t,onApiCall:n,onApiSettled:o}},t.getAppById=function(e){return(0,o.default)(r,[e,"app"])},t.getAppData=function(e){return r[e]},t.hasApp=function(e){return Boolean(r[e])},t.unregisterApp=function(e){return delete r[e]},t.isEmpty=function(){return 0===Object.keys(r).length}},function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length,r=Array(o);++n<o;)r[n]=t(e[n],n,e);return r}},function(e,t,n){var o=n(13),r=n(104),i=n(5),a=n(29),c=1/0,s=o?o.prototype:void 0,u=s?s.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return r(t,e)+"";if(a(t))return u?u.call(t):"";var n=t+"";return"0"==n&&1/t==-c?"-0":n}},function(e,t,n){var o=n(105);e.exports=function(e){return null==e?"":o(e)}},function(e,t,n){var o=n(8);e.exports=function(e,t){var n=o(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}},function(e,t,n){var o=n(8);e.exports=function(e){return o(this,e).has(e)}},function(e,t,n){var o=n(8);e.exports=function(e){return o(this,e).get(e)}},function(e,t){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},function(e,t,n){var o=n(8);e.exports=function(e){var t=o(this,e).delete(e);return this.size-=t?1:0,t}},function(e,t,n){var o=n(9);e.exports=function(e,t){var n=this.__data__,r=o(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}},function(e,t,n){var o=n(9);e.exports=function(e){return o(this.__data__,e)>-1}},function(e,t,n){var o=n(9);e.exports=function(e){var t=this.__data__,n=o(t,e);return n<0?void 0:t[n][1]}},function(e,t,n){var o=n(9),r=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=o(t,e);return!(n<0||(n==t.length-1?t.pop():r.call(t,n,1),--this.size,0))}},function(e,t){e.exports=function(){this.__data__=[],this.size=0}},function(e,t,n){var o=n(12);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=o&&void 0===t?"__lodash_hash_undefined__":t,this}},function(e,t,n){var o=n(12),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return o?void 0!==t[e]:r.call(t,e)}},function(e,t,n){var o=n(12),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(o){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return r.call(t,e)?t[e]:void 0}},function(e,t){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},function(e,t){e.exports=function(e,t){return null==e?void 0:e[t]}},function(e,t,n){var o=n(0)["__core-js_shared__"];e.exports=o},function(e,t,n){var o=n(122),r=function(){var e=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!r&&r in e}},function(e,t,n){var o=n(11),r=n(123),i=n(4),a=n(45),c=/^\[object .+?Constructor\]$/,s=Function.prototype,u=Object.prototype,p=s.toString,d=u.hasOwnProperty,f=RegExp("^"+p.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||r(e))&&(o(e)?f:c).test(a(e))}},function(e,t,n){var o=n(12);e.exports=function(){this.__data__=o?o(null):{},this.size=0}},function(e,t,n){var o=n(125),r=n(120),i=n(119),a=n(118),c=n(117);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}s.prototype.clear=o,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},function(e,t,n){var o=n(126),r=n(10),i=n(28);e.exports=function(){this.size=0,this.__data__={hash:new o,map:new(i||r),string:new o}}},function(e,t,n){var o=n(46);function r(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var o=arguments,r=t?t.apply(this,o):o[0],i=n.cache;if(i.has(r))return i.get(r);var a=e.apply(this,o);return n.cache=i.set(r,a)||i,a};return n.cache=new(r.Cache||o),n}r.Cache=o,e.exports=r},function(e,t,n){var o=n(128);e.exports=function(e){var t=o(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}},function(e,t,n){var o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,i=n(129)((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,(function(e,n,o,i){t.push(o?i.replace(r,"$1"):n||e)})),t}));e.exports=i},function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},function(e,t,n){var o=n(13),r=Object.prototype,i=r.hasOwnProperty,a=r.toString,c=o?o.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),n=e[c];try{e[c]=void 0;var o=!0}catch(e){}var r=a.call(e);return o&&(t?e[c]=n:delete e[c]),r}},function(e,t){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){var o=n(5),r=n(29),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(o(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!r(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},function(e,t,n){var o=n(48),r=n(43);e.exports=function(e,t){for(var n=0,i=(t=o(t,e)).length;null!=e&&n<i;)e=e[r(t[n++])];return n&&n==i?e:void 0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unset=t.request=t.set=void 0;var o=f(n(14)),r=d(n(103)),i=f(n(7)),a=n(102),c=n(27),s=d(n(50)),u=n(15),p=n(49);function d(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function f(e){return e&&e.__esModule?e:{default:e}}var l=function(e){var t=e.data,n=t.appId,s=t.intent,p=t.call,d=t.args,f=t.__port;switch(s){case i.default.REQUEST_API:var l=r.getAppById(n),g=l?(0,a.getDescription)(l):null;return f.postMessage(g),Promise.resolve();case i.default.INVOKE_FUNCTION:var m=r.getAppData(n);if(!m){var T=new Error("The API for "+n+" has been removed");return(0,c.sendResponse)(f,i.default.REJECT)((0,u.serialize)(T))}var y=void 0;m.onApiCall&&(y=m.onApiCall({appId:n,call:p,args:d}));var v=(0,o.default)(m.app,p);return(0,a.invokeApiFunction)(v,d).then((0,c.sendResponse)(f,i.default.RESOLVE),(0,c.sendResponse)(f,i.default.REJECT)).then((function(){m.onApiSettled&&m.onApiSettled({appId:n,call:p,args:d,onApiCallResult:y})}))}return Promise.resolve()};t.set=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.onApiCall,i=n.onApiSettled,a=n.workers,c=arguments[3];c&&(a=c),r.hasApp(e)&&r.unregisterApp(e),r.registerApp(e,t,o,i),s.addSingleHandler(l,a)},t.request=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s.addSingleHandler(l);var n=(0,p.getTargetInfoFromDef)(t);return(0,c.send)({intent:i.default.REQUEST_API,appId:e},n).then((function(t){return t?(0,a.buildApiFromDescription)(e,t,n):Promise.reject(new Error("App with ID "+e+" not found"))}))},t.unset=function(e){r.unregisterApp(e),s.removeSingleHandler(l)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.api=void 0;var o=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(136));t.api=o}])},5276:(e,t,n)=>{var o=n(4497),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return o?void 0!==t[e]:r.call(t,e)}},5288:(e,t,n)=>{var o=n(358),r=n(7200),i=n(6529);e.exports=function(e){return i(e)?o(e,!0):r(e)}},5387:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},5650:(e,t,n)=>{var o=n(8942).Symbol;e.exports=o},5681:(e,t,n)=>{var o=n(4700);e.exports=function(e){var t=o(this,e).delete(e);return this.size-=t?1:0,t}},5832:(e,t,n)=>{var o=n(1129),r=n(4784),i=n(7979),a=n(9306),c=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)o(t,i(e)),e=r(e);return t}:a;e.exports=c},5853:(e,t,n)=>{e=n.nmd(e);var o=n(8942),r=n(4772),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,c=a&&a.exports===i?o.Buffer:void 0,s=(c?c.isBuffer:void 0)||r;e.exports=s},5964:(e,t,n)=>{var o=n(9747),r=n(4784),i=n(4882);e.exports=function(e){return"function"!=typeof e.constructor||i(e)?{}:o(r(e))}},6027:(e,t,n)=>{var o=n(7379),r=n(547);e.exports=function(e){return r(e)&&"[object Arguments]"==o(e)}},6108:(e,t,n)=>{var o=n(9334);e.exports=function(e){return o(e,5)}},6137:e=>{e.exports=function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}},6285:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},6529:(e,t,n)=>{var o=n(3655),r=n(5387);e.exports=function(e){return null!=e&&r(e.length)&&!o(e)}},6638:(e,t,n)=>{var o=n(1386),r=n(9770),i=n(8250);e.exports=function(e,t){var n=this.__data__;if(n instanceof o){var a=n.__data__;if(!r||a.length<199)return a.push([e,t]),this.size=++n.size,this;n=this.__data__=new i(a)}return n.set(e,t),this.size=n.size,this}},7034:(e,t,n)=>{var o=n(6285);e.exports=function(e,t){for(var n=e.length;n--;)if(o(e[n][0],t))return n;return-1}},7073:(e,t,n)=>{var o=n(2532);e.exports=function(e,t,n){"__proto__"==t&&o?o(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},7099:(e,t,n)=>{e=n.nmd(e);var o=n(8942),r=t&&!t.nodeType&&t,i=r&&e&&!e.nodeType&&e,a=i&&i.exports===r?o.Buffer:void 0,c=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,o=c?c(n):new e.constructor(n);return e.copy(o),o}},7144:(e,t,n)=>{var o=n(7034);e.exports=function(e){var t=this.__data__,n=o(t,e);return n<0?void 0:t[n][1]}},7200:(e,t,n)=>{var o=n(1580),r=n(4882),i=n(8546),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return i(e);var t=r(e),n=[];for(var c in e)("constructor"!=c||!t&&a.call(e,c))&&n.push(c);return n}},7350:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e){var n=e.length,o=new e.constructor(n);return n&&"string"==typeof e[0]&&t.call(e,"index")&&(o.index=e.index,o.input=e.input),o}},7379:(e,t,n)=>{var o=n(5650),r=n(8870),i=n(9005),a=o?o.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?r(e):i(e)}},7452:(e,t,n)=>{var o=n(7034);e.exports=function(e){return o(this.__data__,e)>-1}},7462:e=>{e.exports=function(e){return this.__data__.has(e)}},7681:(e,t,n)=>{var o=n(777),r=n(9460),i=n(2306),a=i&&i.isMap,c=a?r(a):o;e.exports=c},7930:(e,t,n)=>{var o=n(9032),r=n(1211);e.exports=function(e,t){return e&&o(t,r(t),e)}},7979:(e,t,n)=>{var o=n(9847),r=n(9306),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(e){return null==e?[]:(e=Object(e),o(a(e),(function(t){return i.call(e,t)})))}:r;e.exports=c},8069:(e,t,n)=>{var o=n(5650),r=o?o.prototype:void 0,i=r?r.valueOf:void 0;e.exports=function(e){return i?Object(i.call(e)):{}}},8121:(e,t,n)=>{var o=n(3766)(Object.keys,Object);e.exports=o},8244:(e,t,n)=>{var o=n(1129),r=n(3142);e.exports=function(e,t,n){var i=t(e);return r(e)?i:o(i,n(e))}},8250:(e,t,n)=>{var o=n(9753),r=n(5681),i=n(88),a=n(4732),c=n(9068);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}s.prototype.clear=o,s.prototype.delete=r,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},8340:e=>{var t=/\w*$/;e.exports=function(e){var n=new e.constructor(e.source,t.exec(e));return n.lastIndex=e.lastIndex,n}},8486:(e,t,n)=>{var o=n(3103),r=n(9770),i=n(9413),a=n(4512),c=n(9270),s=n(7379),u=n(4066),p="[object Map]",d="[object Promise]",f="[object Set]",l="[object WeakMap]",g="[object DataView]",m=u(o),T=u(r),y=u(i),v=u(a),h=u(c),P=s;(o&&P(new o(new ArrayBuffer(1)))!=g||r&&P(new r)!=p||i&&P(i.resolve())!=d||a&&P(new a)!=f||c&&P(new c)!=l)&&(P=function(e){var t=s(e),n="[object Object]"==t?e.constructor:void 0,o=n?u(n):"";if(o)switch(o){case m:return g;case T:return p;case y:return d;case v:return f;case h:return l}return t}),e.exports=P},8546:e=>{e.exports=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}},8666:(e,t,n)=>{var o=n(674),r=n(9460),i=n(2306),a=i&&i.isTypedArray,c=a?r(a):o;e.exports=c},8870:(e,t,n)=>{var o=n(5650),r=Object.prototype,i=r.hasOwnProperty,a=r.toString,c=o?o.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),n=e[c];try{e[c]=void 0;var o=!0}catch(e){}var r=a.call(e);return o&&(t?e[c]=n:delete e[c]),r}},8898:(e,t,n)=>{var o=n(1623);e.exports=function(e){var t=new e.constructor(e.byteLength);return new o(t).set(new o(e)),t}},8942:(e,t,n)=>{var o=n(4967),r="object"==typeof self&&self&&self.Object===Object&&self,i=o||r||Function("return this")();e.exports=i},9005:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},9032:(e,t,n)=>{var o=n(3422),r=n(7073);e.exports=function(e,t,n,i){var a=!n;n||(n={});for(var c=-1,s=t.length;++c<s;){var u=t[c],p=i?i(n[u],e[u],u,n,e):void 0;void 0===p&&(p=e[u]),a?r(n,u,p):o(n,u,p)}return n}},9067:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},9068:(e,t,n)=>{var o=n(4700);e.exports=function(e,t){var n=o(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}},9270:(e,t,n)=>{var o=n(4715)(n(8942),"WeakMap");e.exports=o},9306:e=>{e.exports=function(){return[]}},9334:(e,t,n)=>{var o=n(1340),r=n(9968),i=n(3422),a=n(7930),c=n(125),s=n(7099),u=n(4354),p=n(3546),d=n(4733),f=n(393),l=n(3650),g=n(8486),m=n(7350),T=n(424),y=n(5964),v=n(3142),h=n(5853),P=n(7681),A=n(1580),w=n(3943),I=n(1211),S=n(5288),E="[object Arguments]",b="[object Function]",C="[object Object]",_={};_[E]=_["[object Array]"]=_["[object ArrayBuffer]"]=_["[object DataView]"]=_["[object Boolean]"]=_["[object Date]"]=_["[object Float32Array]"]=_["[object Float64Array]"]=_["[object Int8Array]"]=_["[object Int16Array]"]=_["[object Int32Array]"]=_["[object Map]"]=_["[object Number]"]=_[C]=_["[object RegExp]"]=_["[object Set]"]=_["[object String]"]=_["[object Symbol]"]=_["[object Uint8Array]"]=_["[object Uint8ClampedArray]"]=_["[object Uint16Array]"]=_["[object Uint32Array]"]=!0,_["[object Error]"]=_[b]=_["[object WeakMap]"]=!1,e.exports=function e(t,n,R,O,x,N){var j,D=1&n,M=2&n,G=4&n;if(R&&(j=x?R(t,O,x,N):R(t)),void 0!==j)return j;if(!A(t))return t;var W=v(t);if(W){if(j=m(t),!D)return u(t,j)}else{var Y=g(t),k=Y==b||"[object GeneratorFunction]"==Y;if(h(t))return s(t,D);if(Y==C||Y==E||k&&!x){if(j=M||k?{}:y(t),!D)return M?d(t,c(j,t)):p(t,a(j,t))}else{if(!_[Y])return x?t:{};j=T(t,Y,D)}}N||(N=new o);var U=N.get(t);if(U)return U;N.set(t,j),w(t)?t.forEach((function(o){j.add(e(o,n,R,o,t,N))})):P(t)&&t.forEach((function(o,r){j.set(r,e(o,n,R,r,t,N))}));var L=W?void 0:(G?M?l:f:M?S:I)(t);return r(L||t,(function(o,r){L&&(o=t[r=o]),i(j,r,e(o,n,R,r,t,N))})),j}},9361:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},9413:(e,t,n)=>{var o=n(4715)(n(8942),"Promise");e.exports=o},9460:e=>{e.exports=function(e){return function(t){return e(t)}}},9624:(e,t,n)=>{var o=n(3655),r=n(4759),i=n(1580),a=n(4066),c=/^\[object .+?Constructor\]$/,s=Function.prototype,u=Object.prototype,p=s.toString,d=u.hasOwnProperty,f=RegExp("^"+p.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||r(e))&&(o(e)?f:c).test(a(e))}},9632:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,n){var o=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==o||"symbol"!=o&&t.test(e))&&e>-1&&e%1==0&&e<n}},9747:(e,t,n)=>{var o=n(1580),r=Object.create,i=function(){function e(){}return function(t){if(!o(t))return{};if(r)return r(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();e.exports=i},9753:(e,t,n)=>{var o=n(5098),r=n(1386),i=n(9770);e.exports=function(){this.size=0,this.__data__={hash:new o,map:new(i||r),string:new o}}},9770:(e,t,n)=>{var o=n(4715)(n(8942),"Map");e.exports=o},9847:e=>{e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length,r=0,i=[];++n<o;){var a=e[n];t(a,n,e)&&(i[r++]=a)}return i}},9968:e=>{e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={id:o,loaded:!1,exports:{}};return e[o].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var o={};return(()=>{"use strict";n.r(o),n.d(o,{__init:()=>rn,__initMainFrame:()=>cn,__initWithTarget:()=>an,__setAppExportedAPIs:()=>sn,__unsetAppExportedAPIs:()=>un,accessibility:()=>Bn,addEventListener:()=>ln,appBuilder:()=>yn,appSettings:()=>vn,components:()=>Rn,controllers:()=>_n,document:()=>mn,editor:()=>Tn,environment:()=>In,errors:()=>Ln,fonts:()=>Yn,getBoundedSDK:()=>dn,history:()=>On,ide:()=>An,info:()=>kn,language:()=>Dn,menu:()=>Nn,pages:()=>bn,pagesGroup:()=>jn,panel:()=>hn,preview:()=>Pn,removeEventListener:()=>gn,responsiveLayout:()=>Un,routers:()=>Cn,selection:()=>wn,siteMembers:()=>Gn,siteSegments:()=>Mn,theme:()=>Wn,tpa:()=>xn,userPreferences:()=>Sn,vfs:()=>En,wixapps:()=>Fn});let e,t=[],r=0;const i={COMPS_AND_TYPE:"compsAndType",COMPS:"comps",TYPE:"type",NONE:"none"};function a(e){const n=Array.isArray(e)?e:[e];if(n){const e=n.map((e=>e.id));return t.filter((t=>e.includes(t.id)))}return[]}function c(e){const n=Array.isArray(e)?e:[e];return n.includes(e.ALL)?t:t.filter((e=>function(e,t){return t.some((t=>e.includes(t)))}(e.operationTypes,n)))}function s(n){return new Promise((o=>{n.length?e((()=>{!function(e){const n=new Set(e.map((e=>e.handle)));n.size&&(t=t.filter((e=>!n.has(e.handle))))}(n),o()})):o()}))}const u={setWaitChangesApplied:function(t){e=t},waitForChanges:function(e,t,n){let{compRefsToAwait:o,operationTypes:r,waitingType:u}=e;return new Promise((e=>{switch(u){case i.COMPS:o?s(a(o)).then((()=>e(t()))):(console.error("missing params for editorSDK API (comp refs) - error comes from call to: ",n),e(t()));break;case i.TYPE:r?s(c(r)).then((()=>e(t()))):(console.error("missing params for editorSDK API (comp types) - error comes from call to: ",n),e(t()));break;case i.COMPS_AND_TYPE:s([...a(o),...c(r)]).then((()=>e(t())));break;default:e(t())}}))},addOperation:function(e,n){r++;const o=Array.isArray(n)?n:[n],i={id:e.id?e.id:e,operationTypes:o,handle:r};t.push(i),s([i])},OPERATION_TYPES:{ALL:"all",COMP:"component",ROUTER:"router",MENU:"menu"},WAITING_TYPES:i,waitForAllChanges:function(){return s(t)},clearQueue:function(){t=[]}};let p,d;const f=function(e,t,n){const o=t.split(".");let r=e;return o.forEach(((e,t,o)=>{t===o.length-1&&(r[e]=n),r[e]||(r[e]={}),r=r[e]})),e};function l(){return new Promise((function(e){p?e(p):d=e}))}function g(e){if(!e)return p=void 0,void(d=void 0);p=function(e){let t={};for(const n in e)e.hasOwnProperty(n)&&(t=f(t,n,e[n]));return t}(e),u.setWaitChangesApplied(p.document.waitForChangesApplied),d&&d(p)}async function m(e){return{isSilent:await async function(e){return"function"==typeof e&&await e()}(e.editor.info.isSilentInstall)}}async function T(){const e=await l();return{api:e,platformContext:await m(e)}}var y=n(5141);const v={PUBLIC:"public",PRIVATE:"private",EDITOR:"editor"},h=()=>"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,P=()=>"undefined"!=typeof process,A=()=>h()||P(),w=(e,t)=>"platform_".concat(t,"_").concat(e),I=async(e,t,n,o)=>{const r=await e.document.components.getType(o,t,{componentRef:n.documentPointer||n});if("wysiwyg.viewer.components.RefComponent"===r||"platform.components.AppWidget"===r){const r=await e.document.components.data.get(o,t,{componentRef:n});return(null==o?void 0:o.appDefinitionId)===(null==r?void 0:r.appDefinitionId)}const i=(await e.document.application.getAppDataByAppDefId(o,t,o.appDefinitionId)).components.some((e=>{var t;let{type:n,data:o}=e;return"STUDIO_WIDGET"===n&&(null==o||null===(t=o.componentModel)||void 0===t?void 0:t.componentType)===r}));return i},S=e=>e.startsWith("platform.builder"),E=new Set(["appInstalled","appUpdated","removeApp","removeAppCompleted","beforeRemoveApp","migrate"]),b=(e,t)=>y.api.request(e,t?{target:t}:void 0).then(g);let C=()=>Promise.resolve();const _={},{PUBLIC:R,PRIVATE:O,EDITOR:x}=v,N=[R,O,x],j={[R]:{},[O]:{},[x]:{beforeComponentRemoved:()=>{},canAddWidget:()=>!0,canAddPage:()=>!0,appInstalled:()=>{},appUpdated:()=>{},removeApp:()=>{},removeAppCompleted:()=>{},beforeRemoveApp:()=>{},migrate:()=>{},beforeWidgetAdded:()=>{},getCustomDeletePanelContent:()=>({}),getCustomCannotDeletePanelContent:()=>({}),getSelectedWidgetDesignPresetId:()=>{},getWidgetUpdateFlowContent:()=>{},getPageUpdateFlowContent:()=>{},getDynamicPageNavBarConfig:()=>{}}},D={[R]:e=>e,[O]:e=>e,[x]:(e,t,n,o)=>{const r={beforeComponentRemoved:()=>{},canAddWidget:()=>!0,canAddPage:()=>!0,appInstalled:()=>{},appUpdated:()=>{},removeApp:()=>{},removeAppCompleted:()=>{},beforeRemoveApp:()=>{},migrate:()=>{},beforeWidgetAdded:()=>{},getCustomDeletePanelContent:()=>({}),getCustomCannotDeletePanelContent:()=>({}),getSelectedWidgetDesignPresetId:()=>{},getWidgetUpdateFlowContent:()=>{},getPageUpdateFlowContent:()=>{},getDynamicPageNavBarConfig:()=>{}};return Object.keys(r).forEach((i=>{const a=n&&E.has(i)?e=>n({type:i,payload:e},o):null,c=e[i]||a||r[i];r[i]=function(){try{return c(...arguments)}catch(e){const n=(null==e?void 0:e.message)?e:new Error;throw n.message="Failed executing ".concat(i," for app ").concat(t,": ").concat((null==e?void 0:e.message)||e),n}}})),r}},M=(e,t,n,o,r)=>{const i=D[n](t,e,o,r)||j[n];_[e]=_[e]||{},_[e][n]=i;const a=w(e,n);return y.api.set(a,i),a},G=e=>_[e],W=e=>C(e);function Y(e){return{getLocale:function(t){return l().then((t=>t.editor.environment.getLocale(e)))}}}function k(e){return{isOpen:function(t){return l().then((n=>n.editor.ide.treePane.isOpen(e,t)))},toggle:function(t){return l().then((n=>n.editor.ide.treePane.toggle(e,t)))}}}function U(e){return{addTypeDefinitions:function(t,n){return l().then((o=>o.editor.ide.addTypeDefinitions(e,t,n)))},removeTypeDefinitions:function(t,n){return l().then((o=>o.editor.ide.removeTypeDefinitions(e,t,n)))},openFile:function(t,n){return l().then((o=>o.editor.ide.openFile(e,t,n)))},navigateToCollection:function(t,n){return l().then((o=>o.editor.ide.navigateToCollection(e,t,n)))},treePane:k(e)}}function L(e){if(e){const t="string"==typeof e?JSON.parse(decodeURIComponent(e)):e;self.commonConfig=t}}let F=[],B=[],V=null;function z(e){if(!e||!e.data)return;const{event:t,intent:n}=e.data;"PLATFORM_ON_EVENT"===n&&("startConfiguration"===t.eventType&&(L(t.eventPayload.commonConfig),V=t),F.length>0?F.forEach((e=>e(t))):B.push({event:t,intent:n}))}function H(e){return{onEvent:function(e){!function(e,t){if(B.length>0)for(;B.length;){const{event:e}=B.shift();t(e)}else V&&t(V);F.push(t)}(0,e)}}}function K(e){return{setAdvancedSettings:function(t,n){return l().then((o=>o.editor.accessibility.setAdvancedSettings(e,t,{value:n})))},getAdvancedSettings:function(t){return l().then((n=>n.editor.accessibility.getAdvancedSettings(e,t)))}}}function $(e){return{getDashboardUrl:function(t,n){return l().then((o=>o.editor.info.getDashboardUrl(e,t,n)))},getBusinessManagerUrl:function(t){return l().then((n=>n.editor.info.getBusinessManagerUrl(e,t)))},getAscendUpgradeUrl:function(t,n){return l().then((o=>o.editor.info.getAscendUpgradeUrl(e,t,n)))},getEditorSessionId:function(t){return l().then((n=>n.editor.info.getEditorSessionId(e,t)))},getEditorReadyTimestamp:function(t){return l().then((n=>n.editor.info.getEditorReadyTimestamp(e,t)))},getEditorMode:function(t){return l().then((n=>n.editor.info.getEditorMode(e,t)))},getEditorReferrer:function(t){return l().then((n=>n.editor.info.getEditorReferrer(e,t)))},getConsentPolicy:function(t){return l().then((n=>n.editor.info.getConsentPolicy(e,t)))},getCurrentViewport:function(t){return l().then((n=>n.editor.info.getCurrentViewport(e,t)))}}}function q(e){return{moveToFooter:function(t,n){return l().then((o=>o.editor.components.moveToFooter(e,t,n)))}}}function Q(e){return{navigateTo:function(t,n){return l().then((o=>o.editor.preview.navigateTo(e,t,n)))}}}const X={dsSetter:function(e,t){const n=l().then((n=>u.waitForChanges(e,(()=>t(n)),t.toString())));return n.then((t=>u.addOperation(t.id?t.id:t,e.operationTypes))),n},dsGetter:function(e,t){return l().then((n=>u.waitForChanges(e,(()=>t(n)),t.toString())))},dsUpdater:function(e,t){const n=l().then((n=>u.waitForChanges(e,(()=>t(n)),t.toString())));return n.then((()=>{e.compRefsToAwait&&(e.compRefsToAwait=Array.isArray(e.compRefsToAwait)?e.compRefsToAwait:[e.compRefsToAwait],e.compRefsToAwait.forEach((t=>u.addOperation(t,e.operationTypes))))})),n},OPERATION_TYPES:u.OPERATION_TYPES,WAITING_TYPES:u.WAITING_TYPES,waitForAllChanges:u.waitForAllChanges,clearQueue:u.clearQueue};function J(e){const t=X.OPERATION_TYPES.COMP;return{getSelectedComponents:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.editor.selection.getSelectedComponents(e,n)))},deselectComponents:function(n,o){return X.dsUpdater({compRefsToAwait:o.compsToDeselect,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.editor.selection.deselectComponents(e,n,o)))},selectComponentByCompRef:function(n,o){return X.dsUpdater({compRefsToAwait:o.compsToSelect,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.editor.selection.selectComponentByCompRef(e,n,o)))},locateAndHighlightComponentByCompRef:function(t,n){return l().then((o=>o.editor.selection.locateAndHighlightComponentByCompRef(e,t,n)))},clearHighlights:function(e){return l().then((e=>e.editor.selection.clearHighlights()))}}}function Z(e){return{isEnabled:function(t){return l().then((n=>n.editor.developerMode.isEnabled(e,t)))},isReadOnly:function(t){return l().then((n=>n.editor.developerMode.isReadOnly(e,t)))}}}function ee(e){return{add:function(t,n){return l().then((t=>t.editor.canvasOverlay.add(e,n.url)))},remove:function(){return l().then((t=>t.editor.canvasOverlay.remove(e)))}}}function te(e){return{get:{styleDefinition:function(t,n){return l().then((o=>o.editor.getStyleDefinitionFromManifest(e,t,n)))}}}}function ne(e){return{set:function(t,n){return l().then((o=>o.editor.userPreferences.site.set(e,t,n)))},get:function(t,n){return l().then((o=>o.editor.userPreferences.site.get(e,t,n)))}}}function oe(e){return{check:function(t,n){if(arguments.length<2)throw new Error('this function accepts at least two arguments. did you forget about "token"?');return l().then((o=>o.editor.deeplink.check(e,t,n)))},show:function(t,n){if(arguments.length<2)throw new Error('this function accepts at least two arguments. did you forget about "token"?');return l().then((o=>o.editor.deeplink.show(e,t,n)))}}}function re(e){return{getLinkAsString:function(t,n){return l().then((o=>o.editor.utils.getLinkAsString(e,t,n)))},getCsrfToken:async function(e){return(await l()).editor.utils.getCsrfToken()},translate:async function(e){return(await l()).editor.utils.translate(e)},bulkTranslate:async function(e){return(await l()).editor.utils.translate(e)}}}var ie,ae,ce,se,ue,pe,de,fe,le,ge,me,Te,ye,ve,he,Pe,Ae,we,Ie;function Se(e){const t=X.OPERATION_TYPES.COMP,n=X.OPERATION_TYPES.ROUTER;return{refresh:function(t,n){return l().then((o=>o.editor.routers.refresh(e,t,n)))},setReplacerPage:function(e,o){return X.dsUpdater({operationTypes:[n,t],waitingType:X.WAITING_TYPES.TYPE},(e=>e.editor.routers.setReplacerPage(o.pageRef,o.replacerPageRef)))},addVariantToPage:function(e){return X.dsUpdater({operationTypes:[n],waitingType:X.WAITING_TYPES.TYPE},(t=>t.editor.routers.addVariantToPage(e.pageRef,e.variantPageRef,e.rule,e.variantId,e.biFlowId)))},getVariantPageRule:function(e){return X.dsUpdater({operationTypes:[n],waitingType:X.WAITING_TYPES.TYPE},(t=>t.editor.routers.getVariantPageRule(e.variantPageRef)))},updateVariantPageRule:function(e){return X.dsUpdater({operationTypes:[n],waitingType:X.WAITING_TYPES.TYPE},(t=>t.editor.routers.updateVariantPageRule(e.variantPageRef,e.updatedRule,e.biFlowId)))}}}function Ee(e){return{getViewState:async function(t,n){return(await l()).editor.widgets.getViewState(e,n.componentRef)},setViewState:async function(t,n){const{componentRef:o,viewState:r}=n,i=await l();await i.editor.widgets.setViewState(e,o,r)}}}!function(e){e.MEMBER="member",e.FUNCTION="function"}(ie||(ie={})),function(e){e.NoLink="NoLink",e.PageLink="PageLink",e.AnchorLink="AnchorLink",e.ExternalLink="ExternalLink",e.DocumentLink="DocumentLink",e.PhoneLink="PhoneLink",e.EmailLink="EmailLink",e.LoginToWixLink="LoginToWixLink",e.DynamicPageLink="DynamicPageLink",e.EdgeAnchorLink="EdgeAnchorLinks",e.PopupLink="PopupLink",e.FormSubmitButtonLink="FormSubmitButtonLink"}(ae||(ae={})),function(e){e.Desktop="desktop",e.Mobile="mobile"}(ce||(ce={})),function(e){e.Page="PAGE",e.Widget="WIDGET"}(se||(se={})),function(e){e.Open="open",e.Closed="closed"}(ue||(ue={})),function(e){e.AllBreakpoints="all-breakpoints"}(pe||(pe={})),function(e){e.Image="IMAGE",e.Video="VIDEO",e.Document="DOCUMENT"}(de||(de={})),function(e){e.Success="success",e.Error="error",e.Warning="warning",e.Info="info"}(fe||(fe={})),function(e){e.REPEATER_EDITOR_MAX_ITEMS="repeaterMaxItemsEditorLimitation"}(le||(le={})),function(e){e.Classic="CLASSIC",e.Responsive="RESPONSIVE",e.ADI="ADI",e.ADI_MA="ADI_MA",e.ADI_TEMPLATE="ADI_TEMPLATE",e.Blocks="blocks"}(ge||(ge={})),function(e){e.EditorX="EDITOR_X",e.Studio="STUDIO"}(me||(me={})),function(e){e.Editor="EDITOR",e.App="APP",e.Dependency_Service="Dependency_Service"}(Te||(Te={})),function(e){e.AppMarket="APP_MARKET",e.AddPanel="ADD_PANEL",e.AppPanel="APP_PANEL",e.PageSettingsPanel="PAGE_SETTINGS_PANEL",e.PresetService="PRESET_SERVICE",e.SITE_CREATION="SITE_CREATION",e.SITE_GENERATION="SITE_GENERATION",e.SILENT_INSTALL_SITE_CREATION="SILENT_INSTALL_SITE_CREATION",e.SILENT_INSTALL="SILENT_INSTALL",e.INTENT_INSTALL="INTENT_INSTALL",e.ADD_SECTION_PANEL="ADD_SECTION_PANEL",e.ADD_PAGE_PANEL="ADD_PAGE_PANEL",e.COPY_PASTE="COPY_PASTE",e.INTRO_FUNNEL="INTRO_FUNNEL",e.PAGES_PANEL="PAGES_PANEL",e.PAGE_SETTINGS_PANEL="PAGE_SETTINGS_PANEL",e.IMPORT_PANEL="IMPORT_PANEL",e.MY_BUSINESS="MY_BUSINESS",e.IFRAME_JS_SDK="IFRAME_JS_SDK",e.UPDATE_PANEL="UPDATE_PANEL",e.ADDONS_MARKET="ADDONS_MARKET",e.PLUGINS_MARKET="PLUGINS_MARKET"}(ye||(ye={})),function(e){e.Settings="settings",e.Layout="layout",e.Design="design",e.Upgrade="upgrade",e.Add="add"}(ve||(ve={})),function(e){e.MAIN_ACTION="mainActionClicked",e.SECONDARY_ACTION="secActionClicked",e.CLOSE_ACTION="closeActionClicked"}(he||(he={})),function(e){e.PageInfo="page_info",e.Layout="layout",e.Permissions="permissions",e.SEO="seo"}(Pe||(Pe={})),function(e){e.Custom="Custom"}(Ae||(Ae={})),function(e){e.Neutral="NEUTRAL",e.Free="FREE",e.Paid="PAID"}(we||(we={})),function(e){e.appMenuReorder="appMenuReorder",e.componentDeleted="componentDeleted",e.componentGfppClicked="componentGfppClicked",e.appActionClicked="appActionClicked",e.connectedComponentPasted="connectedComponentPasted",e.connectedComponentDuplicated="connectedComponentDuplicated",e.concurrentPanelEdit="concurrentPanelEdit",e.widgetPasted="widgetPasted",e.widgetDuplicated="widgetDuplicated",e.widgetAdded="widgetAdded",e.controllerAdded="controllerAdded",e.controllerSettingsButtonClicked="controllerSettingsButtonClicked",e.controllerGfppClicked="controllerGfppClicked",e.stateChanged="stateChanged",e.stateAdded="stateAdded",e.stateDuplicated="stateDuplicated",e.pageDeleted="pageDeleted",e.siteWasPublished="siteWasPublished",e.siteWasFirstSaved="siteWasFirstSaved",e.siteWasSaved="siteWasSaved",e.startConfiguration="startConfiguration",e.welcomeSectionMainActionClicked="welcomeSectionMainActionClicked",e.widgetGfppClicked="widgetGfppClicked",e.appUpgradeCompleted="appUpgradeCompleted",e.instanceChanged="instanceChanged",e.componentSelectionChanged="componentSelectionChanged",e.globalDesignPresetChanged="globalDesignPresetChanged",e.developerModeChanged="developerModeChanged",e.developerModeStatusChanged="developerModeStatusChanged",e.focusedPageChanged="focusedPageChanged",e.componentAddedToStage="componentAddedToStage",e.connectedComponentAddedToStage="connectedComponentAddedToStage",e.presetChanged="presetChanged",e.anyComponentAddedToStage="anyComponentAddedToStage",e.appUpdateCompleted="appUpdateCompleted",e.appRefreshCompleted="appRefreshCompleted",e.componentAnimationChanged="componentAnimationChanged",e.componentDataChanged="componentDataChanged",e.componentDesignChanged="componentDesignChanged",e.componentStyleChanged="componentStyleChanged",e.switchedFromPreview="switchedFromPreview",e.componentArrangementChanged="componentArrangementChanged",e.componentDragEnded="componentDragEnded",e.componentResizeStarted="componentResizeStarted",e.componentRotateEnded="componentRotateEnded",e.sitePublishedDialogClosed="sitePublishedDialogClosed",e.pageBackgroundChanged="pageBackgroundChanged",e.mobileTextScaleChanged="mobileTextScaleChanged",e.componentCropSaved="componentCropSaved",e.toggleBackToTopButtonOn="toggleBackToTopButtonOn",e.componentBehaviorChanged="componentBehaviorChanged",e.componentPropsChanged="componentPropsChanged",e.switchedToMobileView="switchedToMobileView",e.switchedToDesktopView="switchedToDesktopView",e.textEditBoxClosed="textEditBoxClosed",e.hideMobileElement="hideMobileElement",e.showMobileElement="showMobileElement",e.pageRenamed="pageRenamed",e.navBarMainActionClicked="navBarMainActionClicked",e.addDynamicPageClicked="addDynamicPageClicked",e.consentPolicyChanged="consentPolicyChanged",e.pageDuplicated="pageDuplicated",e.pageAdded="pageAdded",e.undo="undo",e.redo="redo",e.addElementsCompClicked="addElementsCompClicked",e.addElementsAllCompsClicked="addElementsAllCompsClicked",e.addElementsResetClicked="addElementsResetClicked",e.appVisitedInDashboard="appVisitedInDashboard",e.componentAddedToApp="componentAddedToApp",e.documentOperationError="documentOperationError",e.solveAddWidgetLimitation="solveAddWidgetLimitation",e.componentConnected="componentConnected",e.componentDisconnected="componentDisconnected",e.panelHeaderButtonClicked="panelHeaderButtonClicked",e.themeChanged="themeChanged",e.viewStateChanged="viewStateChanged",e.revokeApp="revokeApp",e.grantApp="grantApp",e.pageVariantSelected="pageVariantSelected",e.resetWidgetOverrides="resetWidgetOverrides",e.widgetPluginAdded="widgetPluginAdded",e.widgetPluginRemoved="widgetPluginRemoved",e.widgetPluginShowOnPageClicked="widgetPluginShowOnPageClicked",e.appInstalled="appInstalled",e.removeAppCompleted="removeAppCompleted",e.siteLanguageChanged="siteLanguageChanged"}(Ie||(Ie={})),Ie.componentSelectionChanged,Ie.focusedPageChanged,Ie.anyComponentAddedToStage,Ie.appUpdateCompleted,Ie.componentAnimationChanged,Ie.componentDataChanged,Ie.componentDesignChanged,Ie.componentStyleChanged,Ie.switchedFromPreview,Ie.componentArrangementChanged,Ie.componentDragEnded,Ie.componentResizeStarted,Ie.componentRotateEnded,Ie.sitePublishedDialogClosed,Ie.pageBackgroundChanged,Ie.mobileTextScaleChanged,Ie.componentCropSaved,Ie.toggleBackToTopButtonOn,Ie.componentBehaviorChanged,Ie.appRefreshCompleted,Ie.componentPropsChanged,Ie.switchedToMobileView,Ie.switchedToDesktopView,Ie.textEditBoxClosed,Ie.hideMobileElement,Ie.showMobileElement,Ie.undo,Ie.redo,Ie.appVisitedInDashboard,Ie.developerModeStatusChanged,Ie.componentConnected,Ie.componentDisconnected,Ie.solveAddWidgetLimitation,Ie.pageDuplicated,Ie.pageAdded,Ie.themeChanged,Ie.viewStateChanged,Ie.pageVariantSelected,Ie.siteLanguageChanged,Ie.appMenuReorder,Ie.componentDeleted,Ie.componentGfppClicked,Ie.appActionClicked,Ie.connectedComponentPasted,Ie.connectedComponentDuplicated,Ie.concurrentPanelEdit,Ie.widgetPasted,Ie.widgetDuplicated,Ie.widgetAdded,Ie.resetWidgetOverrides,Ie.widgetPluginAdded,Ie.widgetPluginRemoved,Ie.widgetPluginShowOnPageClicked,Ie.controllerAdded,Ie.controllerSettingsButtonClicked,Ie.controllerGfppClicked,Ie.presetChanged,Ie.stateChanged,Ie.stateAdded,Ie.stateDuplicated,Ie.pageDeleted,Ie.siteWasPublished,Ie.siteWasFirstSaved,Ie.siteWasSaved,Ie.startConfiguration,Ie.welcomeSectionMainActionClicked,Ie.widgetGfppClicked,Ie.appUpgradeCompleted,Ie.instanceChanged,Ie.globalDesignPresetChanged,Ie.developerModeChanged,Ie.connectedComponentAddedToStage,Ie.pageRenamed,Ie.addDynamicPageClicked,Ie.consentPolicyChanged,Ie.addElementsCompClicked,Ie.addElementsAllCompsClicked,Ie.addElementsResetClicked,Ie.componentAddedToApp,Ie.documentOperationError,Ie.panelHeaderButtonClicked,Ie.grantApp,Ie.revokeApp,Ie.appInstalled,Ie.removeAppCompleted,Ie.navBarMainActionClicked;const be="registerError_",Ce=Object.freeze({EXTENSION:"extension",DEFAULT:"default"}),_e=e=>e.context===Ce.EXTENSION;function Re(e,t,n){let{isRequired:o}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{isRequired:!1};const r=_e(e)?null==t?void 0:t[n]:null==e?void 0:e[n];if(o&&void 0===r)throw new Error("option ".concat(n," is required"));return r}function Oe(e){console.warn("editorSDK.".concat(e," has been deprecated - please do not use it anymore. For more information, see the API reference."))}function xe(e){function t(t,n){return l().then((o=>o.editor.openChangeVariationsPanel(e,t,n)))}function n(t,n,o){return l().then((r=>r.editor.openComponentPanel(e,t,n,o)))}function o(t,n,o){return l().then((t=>t.editor.openNativeComponentPanel(e,n,o)))}function r(t,n,o){return l().then((r=>r.editor.openPanel(e,t,n,o)))}function i(t,n){return l().then((o=>o.editor.openModalPanel(e,t,n)))}function a(t,n){return l().then((o=>o.editor.openToolPanel(e,t,n)))}function c(t,n){const o={displayAboveModals:!0};return l().then((r=>r.editor.openSidePanel(e,t,{...o,...n})))}function s(t,n){return l().then((o=>o.editor.openFullStagePanel(e,t,n)))}function u(t,n){return l().then((o=>o.editor.openLinkPanel(e,t,n)))}function p(t,n,o){return l().then((r=>r.editor.openColorPicker(e,t,n,o)))}function d(t,n,o,r){return l().then((i=>i.editor.openFontPicker(e,t,n,o,r)))}function f(t,n){return l().then((o=>o.editor.openMediaPanel(e,t,n)))}function g(t,n){return l().then((o=>o.editor.openPagePanel(e,t,n)))}function m(t,n){return l().then((o=>o.editor.openPagesPanel(e,t,n)))}function h(e){return{..."widgetId"in e?{widgetId:e.widgetId}:{},..."appDefinitionId"in e?{appDefinitionId:e.appDefinitionId}:{},..."componentRef"in e?{compRef:e.componentRef}:{}}}function P(t,n){return l().then((o=>o.editor.openHelpPanel(e,t,n)))}function I(t,n){return l().then((o=>{o.editor.closePanel(e,t,n)}))}function S(t,n){return l().then((o=>{o.editor.updatePanelOptions(e,t,n)}))}function E(t,n){return l().then((o=>o.editor.openBillingPage(e,t,n)))}function b(t,n){return l().then((o=>o.editor.openDashboardPanel(e,t,n)))}function C(t,n){return l().then((o=>o.editor.openProgressBar(e,t,n)))}function _(t,n){return l().then((o=>o.editor.updateProgressBar(e,t,n)))}function R(t,n){return l().then((o=>o.editor.closeProgressBar(e,t,n)))}function O(t,n){return l().then((o=>o.editor.openErrorPanel(e,t,n)))}function x(t,n){return l().then((o=>o.editor.openConfirmationPanel(e,t,n)))}function N(t,n){return l().then((o=>o.editor.openManageContentPanel(e,t,n)))}function j(t,n){return l().then((o=>o.editor.openPromotionalPanel(e,t,n)))}function D(t,n){return l().then((o=>o.editor.openAddElementsPanel(e,t,n)))}function W(t,n){const{addComponentHandler:o,removeComponentHandler:r,...i}=n;return i.withDivider=!0,l().then((n=>n.editor.openElementsPanel(e,t,i,o,r)))}const k=function(e){const t={...e};for(const n of Object.keys(t))t[n]=function(){for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return T().then((t=>{let{platformContext:r}=t;return r.isSilent?Promise.resolve():e[n](...o)}))};return t}({openBillingPage:E,openHelpPanel:P,closePanel:I,updatePanelOptions:S,openComponentPanel:n,openComponentPanelWithSiteContext:function(t,n,o){return l().then((r=>r.editor.openComponentPanelWithSiteContext(e,t,n,o)))},showPanelPreloader:function(t){return l().then((n=>n.editor.showPanelPreloader(e,t)))},hidePanelPreloader:function(t){return l().then((n=>n.editor.hidePanelPreloader(e,t)))},openNativeComponentPanel:o,openChangeVariationsPanel:t,openPanel:r,openToolPanel:a,openSidePanel:c,openFullStagePanel:s,openLinkPanel:u,openColorPicker:p,openFontPicker:d,openMediaPanel:f,openPagePanel:g,openPagesPanel:m,closePagesPanel:function(){return l().then((t=>t.editor.closePagesPanel(e)))},openModalPanel:i,openProgressBar:C,openSettingsPanel:function(t,n){return l().then((t=>t.editor.openSettingsPanel(e,h(n))))},closeProgressBar:R,closeSettingsPanel:function(t,n){return l().then((t=>{t.editor.closeSettingsPanel(e,h(n))}))},openDashboardPanel:b,updateProgressBar:_,openErrorPanel:O,openConfirmationPanel:x,openManageContentPanel:N,openPromotionalPanel:j,openAddElementsPanel:D,openElementsPanel:W});return{accessibility:K(e),info:$(e),preview:Q(e),selection:J(e),panel:H(),environment:Y(e),components:q(e),ide:U(e),utils:re(e),userPreferences:{site:ne(e)},developerMode:Z(e),routers:Se(e),deeplink:oe(e),canvasOverlay:ee(e),widgets:Ee(e),manifest:te(e),openComponentPanel:n,openNativeComponentPanel:o,openChangeVariationsPanel:t,openPanel:r,openToolPanel:a,registerToolsPanel:function(t,n,o){return l().then((t=>t.editor.registerToolsPanel(e,n,o)))},unregisterToolsPanel:function(t){return l().then((t=>t.editor.unregisterToolsPanel(e)))},openSidePanel:c,openFullStagePanel:s,openLinkPanel:u,openColorPicker:p,openFontPicker:d,openMediaPanel:f,openPagePanel:g,openPagesPanel:m,canAddStaticPage:function(t){return l().then((n=>n.editor.canAddStaticPage(e,t)))},setAppAPI:function(t,n,o){return l().then((r=>{const i=Re(e,o,"appDefinitionId",{isRequired:!0}),a=M(i,n,v.PRIVATE);return y.api.set(String(e.appDefinitionId),n),y.api.set(String(e.applicationId),n),r.editor.setAppAPI(e,t,Object.assign(o||{},{apiName:a}))}))},getAppAPI:function(t){return A()?(G(e.appDefinitionId)||{})[v.PRIVATE]:new Promise((n=>{n(Re(e,t,"appDefinitionId",{isRequired:!0}))})).then((e=>y.api.request(w(e,v.PRIVATE),{target:parent})))},save:function(e){return Oe("editor.save()"),Promise.resolve()},showNotification:function(t,n){return l().then((o=>o.editor.showNotification(e,t,n)))},showUserActionNotification:function(t,n){const{link:o,...r}=n,{caption:i,onClick:a}=o;return l().then((n=>n.editor.showUserActionNotification(e,t,{...r,link:{caption:i}},a)))},showPresetUserActionNotification:function(t,n){return l().then((o=>o.editor.showPresetUserActionNotification(e,t,n)))},updateAppState:function(t){return l().then((n=>n.editor.updateAppState(e,t)))},PanelType:ve,MediaType:de,PagesPanelTabType:Pe,NotificationType:fe,NotificationPresetTypes:le,PremiumIntent:we,openBillingPage:E,openHelpPanel:P,closePanel:I,updatePanelOptions:S,openModalPanel:i,openProgressBar:C,openDashboardPanel:b,updateProgressBar:_,closeProgressBar:R,openErrorPanel:O,openConfirmationPanel:x,openManageContentPanel:N,openPromotionalPanel:j,openAddElementsPanel:D,openElementsPanel:W,requestFeedback:async function(t,n){return(await l()).editor.feedback.open({appDefinitionId:e.appDefinitionId,...n})},getZoomFactor:async function(){return(await l()).editor.getSiteScale()},...k}}let Ne="";function je(e){Ne=e}function De(){return Ne}function Me(e){return{refresh:function(t,n){return l().then((o=>o.document.application.livePreview.refresh(e,t,n)))}}}function Ge(e){return{update:function(t,n){return l().then((o=>o.document.application.sessionState.update(e,t,n)))}}}var We=n(878);const Ye=[We.t0,We.nx,We.SP,We.oQ,We.OE,We.xG,We.Lm,We.Ys,We.W,We.hQ,We.m2,We.yy],ke=X.OPERATION_TYPES.COMP;function Ue(e){return{getPublicAPI:function(e,t){return A()?(G(t.appDefinitionId)||{})[v.PUBLIC]:y.api.request(w(t.appDefinitionId,v.PUBLIC),{target:parent})},reloadManifest:function(){return A()?W(e):Promise.reject(new Error("reloadManifest can only be called from worker"))},add:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;const a={...n,editorType:De()};return i.isSilent&&(a.isSilent=!0,a.shouldNavigate=!1,a.disableAddPanel=!0,a.showPageAddedPanel=!1),r.document.tpa.add.application(e,t,a)}))},install:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;const a={...n,editorType:De()};return i.isSilent&&(a.isSilent=!0),r.document.application.install(e,t,a)}))},uninstall:function(t,n){return l().then((async o=>{const{appDefinitionId:r}=n;let i=e;if(e.appDefinitionId===We.wv&&Ye.includes(r))i=await o.document.tpa.app.getAppDataByAppDefId(e,t,r);else if(e.context===Ce.DEFAULT&&r)throw new Error("Uninstall different application is prohibited");return o.document.application.uninstall(i,t,n)}))},isApplicationInstalled:function(t,n){return l().then((o=>o.document.application.isApplicationInstalled(e,t,n)))},registerToCustomEvents:function(t,n){return l().then((o=>o.document.application.registerToCustomEvents(e,t,n)))},appStudioWidgets:{changeVariation:function(t,n){return l().then((o=>o.document.application.appStudioWidgets.changeVariation(e,t,n)))},changePreset:function(t,n){return l().then((async o=>{if(!await I(o,t,n.componentRef,e))throw new Error("Can not change the preset of another application's widget");return o.document.application.appStudioWidgets.changePreset(e,t,n)}))},getPreset:function(t,n){return l().then((o=>o.document.application.appStudioWidgets.getPreset(e,t,n)))},addWidget:function(t,n){return X.dsSetter({compRefsToAwait:n.containerRef,operationTypes:ke,waitingType:X.WAITING_TYPES.COMPS},(o=>o.document.application.appStudioWidgets.addWidget(e,t,n)))},props:{get:function(t,n){return l().then((o=>o.document.application.appStudioWidgets.props.get(e,t,n)))},set:function(t,n){return l().then((o=>o.document.application.appStudioWidgets.props.set(e,t,n)))}},designVariables:{get:function(t,n){return l().then((async o=>{const{widgetRef:r}=n,i=await o.document.components.getType(e,t,{componentRef:r});if(S(i)){const n=await o.editor.getStyleDefinitionFromManifest(e,t,{editorPointer:{documentPointer:r}}),i=await o.document.components.style.getStructuredStyle(e,t,{componentRef:r});return Object.entries(n).reduce(((e,t)=>{let[n]=t;var o;return e[n]=null!==(o=null==i?void 0:i.style[n])&&void 0!==o?o:{},e}),{})}return o.document.application.appStudioWidgets.designVariables.get(e,t,n)}))},set:function(t,n){return l().then((async o=>{const{widgetRef:r,newValues:i}=n,a=await o.document.components.getType(e,t,{componentRef:r});if(S(a)){const n=await o.document.components.style.getStructuredStyle(e,t,{componentRef:r});return o.document.components.style.setStructuredStyle(e,t,{style:{...null==n?void 0:n.style,...i},componentRef:r})}return o.document.application.appStudioWidgets.designVariables.set(e,t,n)}))}}},sessionState:Ge(e),livePreview:Me(e)}}function Le(e){const t=X.OPERATION_TYPES.COMP;return{listControllers:function(n,o){const r=o?o.pageRef:null;return X.dsGetter({compRefsToAwait:r,operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.controllers.listControllers(e,n,o)))},listConnectableControllers:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.controllers.listConnectableControllers(e,n,o)))},connect:function(n,o){return X.dsUpdater({compRefsToAwait:[o.connectToRef,o.controllerRef],operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.connect(e,n,o)))},disconnect:function(n,o){return X.dsUpdater({compRefsToAwait:[o.connectToRef,o.controllerRef],operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.disconnect(e,n,o)))},getData:function(n,o){return X.dsGetter({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.getData(e,n,o)))},saveConfiguration:function(n,o){return X.dsUpdater({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.saveConfiguration(e,n,o)))},getControllerConnections:function(n,o){return X.dsGetter({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.listControllerConnections(e,n,o)))},listControllerConnections:function(n,o){return X.dsGetter({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.listControllerConnections(e,n,o)))},listConnections:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.controllers.listConnections(e,n,o)))},listConnectedComponents:function(n,o){return X.dsGetter({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.controllers.listConnectedComponents(e,n,o)))},setDisplayName:function(n,o){return X.dsUpdater({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.NONE},(t=>t.document.controllers.setDisplayName(e,n,o)))},setState:function(n,o){const r=Object.keys(o.state).map((e=>o.state[e])).reduce(((e,t)=>e.concat(t)),[]);return X.dsUpdater({compRefsToAwait:r,operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.controllers.setState(e,n,o)))},listAllControllers:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.controllers.listAllControllers(e,n,o)))},isControllerExists:function(n,o){return X.dsGetter({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(async t=>(await t.document.controllers.findAllByType(e,n,o)).length>0))},findAllByType:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.controllers.findAllByType(e,n,o)))},moveAppControllerToMasterPage:function(n,o){return X.dsGetter({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.moveAppControllerToMasterPage(e,n,o)))},moveAppControllerToPage:function(n,o){return X.dsGetter({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.controllers.moveAppControllerToPage(e,n,o)))}}}function Fe(e){return{add:function(t,n){return l().then((o=>o.document.history.add(e,t,n)))}}}function Be(e){const t=X.OPERATION_TYPES.COMP;return{getNickname:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.code.getNickname(e,n,o)))},setNickname:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(async t=>(await async function(e,t,n,o){var r,i;const[a]=await n.document.components.get(e,t,{componentRefs:o,properties:["data"]}),c=null===(r=null==a?void 0:a.data)||void 0===r?void 0:r.appDefinitionId,s=null===(i=null==a?void 0:a.data)||void 0===i?void 0:i.applicationId;if(e.appDefinitionId&&e.appDefinitionId!==c&&e.appDefinitionId!==s)throw new Error("Can not call for applicationId: ".concat(e.applicationId," because componentRef belongs to applicationId: ").concat(s," and appDefinitionId: ").concat(c))}(e,n,t,o.componentRef),t.document.components.code.setNickname(e,n,o))))}}}function Ve(e){const t=X.OPERATION_TYPES.COMP;return{get:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.data.get(e,n,o)))},update:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.data.update(e,n,o)))},updateInLanguage:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.data.updateInLanguage(e,n,o)))}}}function ze(e){const t=X.OPERATION_TYPES.COMP;return{showComponentOnlyOnPagesGroup:function(n,o){const r=o.componentRef?o.componentRef:o.componentPointer;return X.dsUpdater({compRefsToAwait:[r,o.pagesGroupPointer],operationTypes:t},(t=>t.document.components.modes.showComponentOnlyOnPagesGroup(e,n,o)))},applyCurrentToAllModes:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.modes.applyCurrentToAllModes(e,n,o)))},activateComponentMode:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.modes.activateComponentMode(e,n,o)))},getModes:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.modes.getModes(e,n,o)))}}}function He(e){const t=X.OPERATION_TYPES.COMP;return{removeAllOverrides:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.refComponents.removeAllOverrides(e,n,o)))},removeOverrides:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.refComponents.removeOverrides(e,n,o)))},getAllAppRefComponents:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.refComponents.getAllAppRefComponents(e,n,o)))},getTemplateComponent:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.refComponents.getTemplateComponent(e,n,o)))},getHostComponentRef:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.refComponents.getHostComponentRef(e,n,o)))},isRefComponentCollapsed:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.refComponents.isRefComponentCollapsed(e,n,o)))},getCollapsedRefComponents:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.refComponents.getCollapsedRefComponents(e,n,o)))},collapseReferredComponent:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.refComponents.collapseReferredComponent(e,n,o)))},expandReferredComponent:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.refComponents.expandReferredComponent(e,n,o)))},hasOverrides:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.refComponents.hasOverrides(e,n,o)))}}}function Ke(e){const t=X.OPERATION_TYPES.COMP;return{get:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.properties.get(e,n,o)))},update:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.properties.update(e,n,o)))}}}function $e(e){const t=X.OPERATION_TYPES.COMP;return{get:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.style.get(e,n,o)))},update:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.style.update(e,n,o)))},updateFull:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.style.updateFull(e,n,o)))},getStructuredStyle:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.style.getStructuredStyle(e,n,o)))},setStructuredStyle:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.style.setStructuredStyle(e,n,o.style)))}}}function qe(e){const t=X.OPERATION_TYPES.COMP;return{get:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.layout.get(e,n,o)))},update:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.layout.update(e,n,o)))}}}function Qe(e){return{getRuntimeState:function(t,n){return l().then((o=>o.document.components.behaviors.getRuntimeState(e,t,n)))},execute:function(t,n){return l().then((o=>o.document.components.behaviors.execute(e,t,n)))},get:function(t,n){return l().then((o=>o.document.components.behaviors.get(e,t,n)))},update:function(t,n){return l().then((o=>o.document.components.behaviors.update(e,t,n)))},remove:function(t,n){return l().then((o=>o.document.components.behaviors.remove(e,t,n)))}}}function Xe(e){return{getIndex:function(t,n){return l().then((o=>o.document.components.arrangement.getIndex(e,t,n)))},moveToIndex:function(t,n){return X.dsUpdater({compRefsToAwait:n.componentRef,operationTypes:X.OPERATION_TYPES.COMP},(o=>o.document.components.arrangement.moveToIndex(e,t,n)))}}}function Je(e){const t=X.OPERATION_TYPES.COMP;return{get:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.design.get(e,n,o)))},update:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.design.update(e,n,o)))}}}function Ze(e){const t=X.OPERATION_TYPES.COMP;return{mergeStylesheets:async function(e,t){let{destination:n,source:o}=t;return(await l()).document.components.stylable.mergeStylesheets(n,o)},update:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.stylable.update(e,n,o)))}}}const et="platform.components.AppController",tt="platform.components.AppWidget",nt="mobile.core.components.Page";function ot(e){function t(t,n,o){const r=t.document.components.buildDefaultComponentStructure(e,n,{componentType:"responsive.components.Section"});return r.style=o.style||r.style,r.layoutResponsive=o.layoutResponsive||r.layoutResponsive,r.components=o.components||r.components,r}const n={[et]:async function(t,n,o){const r=await t.document.components.buildDefaultComponentStructure(e,n,{componentType:et});return r.data={...r.data,controllerType:o.controllerType,applicationId:o.appDefinitionId,name:o.name,settings:JSON.stringify(o.settings||{})},r.layout=o.layout||r.layout,r},[tt]:async function(t,n,o){const r=await t.document.components.buildDefaultComponentStructure(e,n,{componentType:tt});return r.data={...r.data,controllerType:o.controllerType,name:o.name,applicationId:o.appDefinitionId},r.components=o.children,r.layout=o.layout||r.layout,r},[nt]:async function(n,o,r){const i=await n.document.components.buildDefaultComponentStructure(e,o,{componentType:nt});i.data={...i.data,...r.data};const a=r.responsiveSectionOptions?[t(n,o,{...r.responsiveSectionOptions,components:r.components})]:r.components;return i.components=a||i.components,i}};return{createDefinition:function(t,o){return l().then((r=>{const i=n[o.componentType];return i?i(r,t,o):r.document.components.buildDefaultComponentStructure(e,t,{componentType:o.componentType})}))}}}function rt(e){const t=X.OPERATION_TYPES.COMP;return{code:Be(e),layout:qe(e),modes:ze(e),properties:Ke(e),style:$e(e),data:Ve(e),behaviors:Qe(e),design:Je(e),stylable:Ze(e),add:function(n,o){return X.dsSetter({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.add(e,n,o)))},addAndAdjustLayout:function(n,o){return X.dsSetter({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.addAndAdjustLayout(e,n,o)))},remove:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.components.remove(e,n,o)))},getPage:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.getPage(e,n,o)))},getAllComponents:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.getAllComponents(e,n)))},findAllByRole:function(n,o){return X.dsGetter({compRefsToAwait:o.controllerRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(async t=>(await t.document.controllers.listControllerConnections(e,n,{controllerRef:o.controllerRef})).filter((e=>{let{connection:t}=e;return t.role===o.role&&(!o.subRole||t.subRole===o.subRole)})).map((e=>{let{componentRef:t}=e;return t}))))},findAllByType:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.findAllByType(e,n,o)))},getById:function(n,o){return X.dsGetter({compRefsToAwait:{id:o.id},operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.getById(e,n,o)))},getType:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.getType(e,n,o)))},isRepeatedComponent:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.isRepeatedComponent(e,n,o)))},getChildren:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.getChildren(e,n,o)))},getAncestors:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.components.getAncestors(e,n,o)))},get:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRefs,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.get(e,n,o)))},serialize:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.serialize(e,n,o)))},migrate:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.migrate(e,n,o)))},isFullWidth:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.isFullWidth(e,n,o)))},setFullWidth:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.components.setFullWidth(e,n,o)))},createDefinition:ot(e).createDefinition,arrangement:Xe(e),refComponents:He(e)}}function it(e){return{getFontsOptions:function(t){return X.dsGetter({waitingType:X.WAITING_TYPES.NONE},(n=>n.document.fonts.getFontsOptions(e,t)))}}}function at(e){return{getMetaSiteId:function(t){return l().then((t=>t.document.info.getMetaSiteId(e)))},getPublicUrl:function(t){return l().then((t=>t.document.info.getPublicUrl(e)))},getSiteId:function(t){return l().then((t=>t.document.info.getSiteId(e)))},getUserId:function(t){return l().then((t=>t.document.info.getUserId(e)))},getUserInfo:function(t){return l().then((t=>t.document.info.getUserInfo(e)))},getAppDefinitionId:function(t){if(_e(e))throw new Error("appDefinitionId is meaningless in Extensions context");return Promise.resolve(e.appDefinitionId)},isSitePublished:function(t){return l().then((t=>t.document.info.isSitePublished(e)))},getEntityIdFromRef:function(){var e;const t=(e=arguments.length-1)<0||arguments.length<=e?void 0:arguments[e];return t&&t.id&&t.type?t.id:null},getLanguage:function(t){return l().then((t=>t.document.info.getLanguage(e)))},getRegionalLanguage:function(t){return l().then((t=>t.document.info.getRegionalLanguage(e)))},isSiteSaved:function(t){return l().then((t=>t.document.info.isSiteSaved(e)))},isHttpsEnabled:function(t){return l().then((t=>t.document.info.isHttpsEnabled(e)))},getSdkVersion:function(e){return self.editorSDK&&self.editorSDK.sdkVersionInfo},getCurrency:function(t){return l().then((t=>t.document.info.getCurrency(e)))},getTimeZone:function(t){return l().then((t=>t.document.info.getTimeZone(e)))},getAppInstance:function(t,n){return l().then((o=>o.document.info.getAppInstance(e,t,n)))},getAppInstancePayload:function(t){return l().then((n=>n.document.info.getAppInstancePayload(e,t)))},getAppInstanceId:function(t,n){return l().then((o=>o.document.info.getAppInstanceId(e,t,n)))},getSiteAssetsScriptVersion:function(e,t){return l().then((e=>e.document.info.getSiteAssetsScriptVersion(t)))},getVendorProductId:function(t,n){return l().then((o=>o.document.info.getVendorProductId(e,t,n)))},getSiteRegion:function(t){return l().then((t=>t.document.info.getRegion(e)))},getArtifactsUrls:function(t,n){return l().then((t=>t.document.info.getArtifactsUrls(e,n)))},getArtifactOverrides:function(e){return l().then((e=>e.document.info.getArtifactOverrides()))},getSiteDisplayName:function(){return l().then((e=>e.document.info.getSiteDisplayName()))},getStageViewMode:function(){return l().then((e=>e.document.info.getStageViewMode()))},getSiteMap:function(t){return l().then((n=>n.document.info.getSiteMap(e,t)))},getUserGEO:function(t){return l().then((t=>t.document.info.getUserGEO(e)))},getAppRevisionOverrides:function(e){return l().then((e=>e.document.info.getAppRevisionOverrides()))}}}function ct(e){return{add:function(t,n){return l().then((o=>o.document.language.add(e,t,n)))},component:{getTranslations:function(t,n){return X.dsGetter({compRefsToAwait:n.componentRef,operationTypes:X.OPERATION_TYPES.COMP,waitingType:X.WAITING_TYPES.COMPS},(o=>o.document.language.component.getTranslations(e,t,n)))},data:{remove:function(t,n){return l().then((o=>o.document.language.component.data.remove(e,t,n.languageCode,n.componentRef)))}}},multilingual:{isEnabled:function(t){return l().then((t=>t.document.language.multilingual.isEnabled(e)))},languages:{remove:function(t,n){return l().then((o=>o.document.language.multilingual.languages.remove(e,t,n.languageCode)))},removeAll:function(t){return l().then((n=>n.document.language.multilingual.languages.removeAll(e,t)))}}},current:st(e),original:{get:function(e){return l().then((e=>e.document.language.original.get()))}},setLanguages:function(e,t){return l().then((e=>e.document.language.setLanguages(t)))},getTranslationLanguages:function(e){return l().then((e=>e.document.language.getTranslationLanguages()))},autoTranslate:function(e,t){const{languageCode:n,origin:o}=t||{};return l().then((e=>e.document.language.autoTranslate({languageCode:n,origin:o})))}}}function st(e){return{set:function(t,n){return l().then((o=>o.document.language.current.set(e,t,n)))},get:function(t){return l().then((n=>n.document.language.current.get(e,t)))}}}function ut(e){const t=X.OPERATION_TYPES.MENU;return{connect:function(n,o){return X.dsUpdater({compRefsToAwait:[o.menuCompPointer,{id:o.menuId}],operationTypes:t,waitingType:X.WAITING_TYPES.COMPS_AND_TYPE},(t=>t.document.menu.connect(e,n,o)))},update:function(n,o){return X.dsUpdater({compRefsToAwait:{id:o.menuId},operationTypes:t,waitingType:[X.WAITING_TYPES.TYPE]},(t=>t.document.menu.update(e,n,o)))},addItem:function(n,o){return X.dsUpdater({compRefsToAwait:{id:o.menuId},operationTypes:t,waitingType:[X.WAITING_TYPES.TYPE]},(t=>t.document.menu.addItem(e,n,o)))},getById:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.menu.getById(e,n,o)))},remove:function(n,o){return X.dsUpdater({compRefsToAwait:{id:o.menuId},operationTypes:t,waitingType:[X.WAITING_TYPES.TYPE]},(t=>t.document.menu.remove(e,n,o)))},create:function(n,o){return X.dsSetter({operationTypes:t,waitingType:X.WAITING_TYPES.NONE},(t=>t.document.menu.create(e,n,o)))},getDefaultMenuId:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.menu.getDefaultMenuId(e,n)))}}}function pt(e){const t=X.OPERATION_TYPES.COMP;return{hasPassword:function(n,o){return X.dsGetter({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.permissions.hasPassword(e,n,o)))},updatePagePassword:function(n,o){return X.dsUpdater({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.permissions.updatePagePassword(e,n,o)))},removePagePassword:function(n,o){return X.dsUpdater({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.permissions.removePagePassword(e,n,o)))},duplicatePagePassword:function(n,o){return X.dsUpdater({compRefsToAwait:[o.sourcePageRef,o.destinationPageRef],operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.permissions.duplicatePagePassword(e,n,o)))},duplicateGroupsPermissions:function(n,o){return X.dsUpdater({compRefsToAwait:[o.sourcePageRef,o.destinationPageRef],operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.permissions.duplicateGroupsPermissions(e,n,o)))}}}function dt(e){return{add:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;return i.isSilent&&(n.shouldNavigateToPage=!1),r.document.pages.popupPages.add(e,t,n)}))},addConnected:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;return i.isSilent&&(n.shouldNavigateToPage=!1),r.document.pages.popupPages.addConnected(e,t,n)}))},open:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;return i.isSilent?Promise.resolve():r.document.pages.popupPages.open(e,t,n)}))},close:function(t){return l().then((n=>n.document.pages.popupPages.close(e,t)))},isPopupOpened:function(t){return l().then((n=>n.document.pages.popupPages.isPopupOpened(e,t)))},getApplicationPopups:function(t,n){return l().then((o=>o.document.pages.popupPages.getApplicationPopups(e,t,n)))},updateBehavior:function(t,n){return l().then((o=>{o.document.pages.popupPages.updateBehavior(e,t,n)}))}}}function ft(e){const t=X.OPERATION_TYPES.COMP;return{update:function(n,o){return X.dsUpdater({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.data.update(e,n,o)))},get:function(n,o){return X.dsGetter({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.data.get(e,n,o)))},getAll:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.pages.data.getAll(e,n)))}}}function lt(e){return{convertPageNameToUrl:function(t,n){return l().then((o=>o.document.pages.pageUriSEO.convertPageNameToUrl(e,t,n)))}}}function gt(e){const t=X.OPERATION_TYPES.COMP;return{data:ft(e),permissions:pt(e),popupPages:dt(e),navigateTo:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;return i.isSilent?Promise.resolve():r.document.pages.navigateTo(e,t,n)}))},rename:function(t,n){return new Promise(((o,r)=>{l().then((i=>{i.document.pages.rename(e,t,n).then((e=>{e?r(e):o()}))}))}))},add:function(n,o){return X.dsSetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(async t=>((await m(t)).isSilent&&(o.shouldNavigateToPage=!1),t.document.pages.add(e,n,o))))},duplicate:function(t,n){return l().then((o=>o.document.pages.duplicate(e,t,n)))},getCurrent:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.pages.getCurrent(e,n)))},getPrimary:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.pages.getPrimary(e,n)))},getPageData:function(n,o){return X.dsGetter({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.getPageData(e,n,o)))},getApplicationPages:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.pages.getApplicationPages(e,n,o)))},remove:function(n,o){return X.dsUpdater({compRefsToAwait:o.pageRef,operationTypes:t},(t=>t.document.pages.remove(e,n,o)))},getHomePage:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.pages.getHomePage(e,n)))},setState:function(n,o){const r=Object.keys(o.state).map((e=>o.state[e])).reduce(((e,t)=>e.concat(t)),[]);return X.dsUpdater({compRefsToAwait:r,operationTypes:t},(t=>t.document.pages.setState(e,n,o)))},serialize:function(n,o){return X.dsGetter({compRefsToAwait:o.pageRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pages.serialize(e,n,o)))},pageUriSEO:lt(e),isDynamicPage:function(t,n){return l().then((o=>o.document.pages.isDynamicPage(e,t,n)))}}}function mt(e){const t=X.OPERATION_TYPES.COMP;return{create:function(n,o){return X.dsSetter({operationTypes:t,waitingType:X.WAITING_TYPES.NONE},(t=>t.document.pagesGroup.create(e,n,o)))},addPageToPagesGroup:function(n,o){const r={id:o.pageId},i=o.pagesGroupPointer;return X.dsUpdater({compRefsToAwait:[r,i],operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pagesGroup.addPageToPagesGroup(e,n,o)))},getById:function(n,o){return X.dsGetter({compRefsToAwait:{id:o.pagesGroupId},operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.pagesGroup.getById(e,n,o)))},getByGroupName:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.pagesGroup.getByGroupName(e,n,o)))},getAll:function(n){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.pagesGroup.getAll(e,n)))},remove:function(n,o){return X.dsUpdater({compRefsToAwait:o.pagesGroupPointer,operationTypes:t,waitingType:X.WAITING_TYPES.NONE},(t=>t.document.pagesGroup.remove(e,n,o)))}}}function Tt(e){const t=X.OPERATION_TYPES.COMP,n=X.OPERATION_TYPES.ROUTER;return{add:function(o,r){var i;if(!r.routerRef||!(null===(i=r.routerRef)||void 0===i?void 0:i.id))throw new Error("invalid routerRef ".concat(r.routerRef," received."));return X.dsSetter({compRefsToAwait:r.routerRef,operationTypes:[t,n],waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.routers.pages.add(e,o,r)))},remove:function(o,r){return X.dsUpdater({compRefsToAwait:[r.pageRef,r.routerRef],operationTypes:[t,n],waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.routers.pages.delete(e,o,r)))},disconnect:function(o,r){return X.dsUpdater({compRefsToAwait:[r.pageRef,r.routerRef],operationTypes:[t,n],waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.routers.pages.disconnect(e,o,r)))},move:function(o,r){return X.dsUpdater({compRefsToAwait:[r.fromRouterRef,r.toRouterRef,r.pageRef],operationTypes:[t,n],waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.routers.pages.move(e,o,r)))},listConnectablePages:function(o){return X.dsGetter({operationTypes:[n,t],waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.routers.pages.listConnectablePages(e,o)))},connect:function(o,r){var i,a;if(!r.pageRef||!(null===(i=r.pageRef)||void 0===i?void 0:i.id))throw new Error("invalid pageRef ".concat(r.pageRef," received."));if(!r.routerRef||!(null===(a=r.routerRef)||void 0===a?void 0:a.id))throw new Error("invalid routerRef ".concat(r.routerRef," received."));return X.dsUpdater({compRefsToAwait:[r.pageRef,r.routerRef],operationTypes:[t,n],waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.routers.pages.connect(e,o,r)))}}}function yt(e){const t=X.OPERATION_TYPES.ROUTER,n=X.OPERATION_TYPES.COMP;return{add:function(n,o){return X.dsSetter({operationTypes:t,waitingType:X.WAITING_TYPES.NONE},(t=>t.document.routers.add(e,n,o)))},get:function(n,o){var r;if(!o.routerRef||!(null===(r=o.routerRef)||void 0===r?void 0:r.id))throw new Error("invalid routerRef ".concat(o.routerRef," received."));return X.dsGetter({compRefsToAwait:o.routerRef,operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.routers.get(e,n,o)))},getAll:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.routers.getAll(e,n,o)))},remove:function(n,o){return X.dsUpdater({compRefsToAwait:o.routerRef,operationTypes:t,waitingType:X.WAITING_TYPES.NONE},(t=>t.document.routers.remove(e,n,o)))},update:function(n,o){var r;if(!o.routerRef||!(null===(r=o.routerRef)||void 0===r?void 0:r.id))throw new Error("invalid routerRef ".concat(o.routerRef," received."));return X.dsUpdater({compRefsToAwait:o.routerRef,operationTypes:t,waitingType:X.WAITING_TYPES.NONE},(t=>t.document.routers.update(e,n,o)))},getByPage:function(t,n){return X.dsGetter({compRefsToAwait:n.pageRef,waitingType:X.WAITING_TYPES.COMPS},(o=>o.document.routers.getByPage(e,t,n)))},getByPrefix:function(n,o){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.routers.getByPrefix(e,n,o)))},isValidPrefix:function(o,r){return X.dsGetter({operationTypes:[t,n],waitingType:X.WAITING_TYPES.TYPE},(t=>t.document.routers.isValidPrefix(e,o,r)))},getCurrentDynamicRouting:function(e){return X.dsGetter({operationTypes:t,waitingType:X.WAITING_TYPES.TYPE},(e=>e.document.routers.getCurrentDynamicRouting()))},InvalidPrefixReason:{PREFIX_CAN_NOT_BE_EMPTY:1,PREFIX_IS_TOO_LONG:2,PREFIX_IS_DUPLICATE_OF_URI_SEO:3,PREFIX_CONTAINS_INVALID_CHARACTERS:4,PREFIX_IS_FORBIDDEN_WORD:5,PREFIX_IS_IN_USE_BY_ANOTHER_APPLICATION:6},pages:Tt(e)}}function vt(e){return{getFooter:function(t){return l().then((n=>n.document.siteSegments.getFooter(e,t)))},getHeader:function(t){return l().then((n=>n.document.siteSegments.getHeader(e,t)))},getPagesContainer:function(t){return l().then((n=>n.document.siteSegments.getPagesContainer(e,t)))},getSiteStructure:function(t){return l().then((n=>n.document.siteSegments.getSiteStructure(e,t)))}}}function ht(e){return{getCustomSignupPageId:function(t){return l().then((t=>t.document.siteMembers.getCustomSignupPageId(e)))},getCustomSignInPageId:function(t){return l().then((t=>t.document.siteMembers.getCustomSignInPageId(e)))},setCustomSignupPageId:function(e,t){return l().then((n=>n.document.siteMembers.setCustomSignupPageId(e,t.pageId)))},setCustomSignInPageId:function(e,t){return l().then((n=>n.document.siteMembers.setCustomSignInPageId(e,t.pageId)))},getCustomNoPermissionsPageId:function(t){return l().then((t=>t.document.siteMembers.getCustomNoPermissionsPageId(e)))},setCustomNoPermissionsPageId:function(e,t){return l().then((n=>n.document.siteMembers.setCustomNoPermissionsPageId(e,t.pageId)))},getPrivacyNoteType:function(e){return l().then((e=>e.document.siteMembers.getPrivacyNoteType()))},setPrivacyNoteType:function(e,t){return l().then((e=>e.document.siteMembers.setPrivacyNoteType(t)))}}}function Pt(e){return{getAll:function(t){return X.dsGetter({waitingType:X.WAITING_TYPES.NONE},(n=>n.document.theme.colors.getAll(e,t)))}}}function At(e){return{getMap:function(t){return X.dsGetter({waitingType:X.WAITING_TYPES.NONE},(n=>n.document.theme.fonts.getMap(e,t)))}}}function wt(e){return{getAll:function(t){return X.dsGetter({waitingType:X.WAITING_TYPES.NONE},(n=>n.document.theme.styles.getAll(e,t)))}}}function It(e){return{styles:wt(e),colors:Pt(e),fonts:At(e)}}function St(e){return{application:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;return i.isSilent&&(n.shouldNavigate=!1,n.disableAddPanel=!0,n.showPageAddedPanel=!1,n.isSilent=!0),r.document.tpa.add.application(e,t,n)}))},component:function(t,n){return T().then((o=>{let{api:r,platformContext:i}=o;return i.isSilent&&((null==n?void 0:n.widget)&&(n.widget.shouldNavigate=!1),(null==n?void 0:n.page)&&(n.page.shouldNavigate=!1)),r.document.tpa.add.component(e,t,n)}))}}}function Et(e){return{getDataByAppDefId:function(t,n){return l().then((o=>o.document.tpa.app.getDataByAppDefId(e,t,n)))},getAllCompsByApplicationId:function(t,n){return l().then((o=>o.document.tpa.app.getAllCompsByApplicationId(e,t,n)))},getAllComps:function(t,n){return l().then((o=>o.document.tpa.app.getAllComps(e,t,n)))},refreshApp:function(t,n){return l().then((t=>t.document.tpa.app.refreshApp(e,n)))},delete:function(t,n){return l().then((t=>t.document.tpa.app.delete(e,n)))},add:function(t,n){return l().then((o=>o.document.tpa.app.add(e,t,n)))}}}function bt(e){return{setAppPublicData:function(t,n){return l().then((o=>o.document.tpa.data.setAppPublicData(e,t,n)))},getAppPublicData:function(t,n){return l().then((o=>o.document.tpa.data.getAppPublicData(e,t,n)))},getAll:function(t,n){return l().then((o=>o.document.tpa.data.getAll(e,t,n)))},set:function(t,n){return l().then((o=>o.document.tpa.data.set(e,t,n)))},remove:function(t,n){return l().then((o=>o.document.tpa.data.remove(e,t,n)))}}}function Ct(e){const t="_vs_";async function n(e,n,o,r){var i;const a=function(e){const n=e.id.includes(t)?t:"_r_";return e.id.split(n)[0]}(r),c=await o.document.components.getById(e,n,{id:a}),[s]=await o.document.components.get(e,n,{componentRefs:c,properties:["data"]}),u=null===(i=null==s?void 0:s.data)||void 0===i?void 0:i.appDefinitionId;if(!u||e.appDefinitionId!==u)throw new Error("Can not call for appDefinitionId: ".concat(e.appDefinitionId," because componentRef belongs to appDefinitionId: ").concat(u))}async function o(e,t,n){var o;return null===(o=(await i(e,{widgetId:n})).find((e=>{let{placement:n}=e;return n.slotId===t})))||void 0===o?void 0:o.compRef}function r(e){return"slotRole"in e?(console.warn('Parameter "slotRole" is deprecated, use "slotId" instead'),e.slotRole):e.slotId}function i(t,n){const o=n.widgetRef&&n.widgetId,r=!n.widgetRef&&!n.widgetId;if(o||r)throw new Error("To define a host widget please provide either a widgetId or a widgetRef.");return X.dsGetter({operationTypes:X.OPERATION_TYPES.COMP,waitingType:X.WAITING_TYPES.TYPE},(async function(o){let r;return"widgetRef"in n&&(r=n.widgetRef),"widgetId"in n&&(r=(await async function(e,t,n,o){const r=await n.document.components.getAllComponents(e,t),i=[];await Promise.all(r.map((async o=>{const r=await n.document.components.getType(e,t,{componentRef:o});["wysiwyg.viewer.components.RefComponent","wysiwyg.viewer.components.tpapps.TPAWidget","wysiwyg.viewer.components.tpapps.TPAMultiSection","wysiwyg.viewer.components.tpapps.TPASection"].includes(r)&&i.push(o)})));const a=(await Promise.all(i.map((async o=>({compRef:o,data:await n.document.components.data.get(e,t,{componentRef:o})}))))).find((e=>e.data.widgetId===o));if(!a)throw new Error("Can not find widgetRef for widgetId: ".concat(o,"}"));return{widgetRef:a.compRef,isOOI:"TPAWidget"===a.data.type}}(e,t,o,n.widgetId)).widgetRef),o.document.tpa.widgetPlugins.getWidgetSlots(e,t,{widgetRef:r})}))}function a(e){const t=e.widgetId&&(e.slotRole||e.slotId)&&!e.slotCompRef,n=e.slotCompRef&&!e.widgetId&&!e.slotId&&!e.slotRole;if(!t&&!n)throw new Error("To define a slot please provide either a slotCompRef or a pair of slotId and widgetId.")}return{addWidgetPlugin:function(t,i){return a(i),X.dsSetter({compRefsToAwait:"slotCompRef"in i?i.slotCompRef:void 0,operationTypes:X.OPERATION_TYPES.COMP,waitingType:X.WAITING_TYPES.TYPE},(async function(a){const c=await async function(e,t,n,o){const r=await n.document.tpa.app.getAppDataByAppDefId(e,t,o);return void 0!==(null==r?void 0:r.isWixTPA)?r.isWixTPA:"Wix"===(await n.document.tpa.app.getAppMarketDataByAppDefId(e,t,o)).by}(e,t,a,i.widgetPluginPointer.appDefinitionId);if(!c)throw new Error("Can not add non Wix App plugin via platform SDK");let s;return s="slotCompRef"in i?i.slotCompRef:await o(t,r(i),i.widgetId),await n(e,t,a,s),a.document.tpa.widgetPlugins.addWidgetPlugin(e,t,{slotCompRef:s,widgetPluginPointer:i.widgetPluginPointer},{origin:"sdk"})}))},getWidgetSlots:i,removeWidgetPlugin:function(t,i){return a(i),X.dsUpdater({compRefsToAwait:"slotCompRef"in i?i.slotCompRef:void 0,operationTypes:X.OPERATION_TYPES.COMP,waitingType:X.WAITING_TYPES.TYPE},(async function(a){let c;return c="slotCompRef"in i?i.slotCompRef:await o(t,r(i),i.widgetId),await n(e,t,a,c),a.document.tpa.widgetPlugins.removeWidgetPlugin(e,t,{slotCompRef:c})}))}}}function _t(e){return{add:St(e),app:Et(e),data:bt(e),widgetPlugins:Ct(e),isApplicationInstalled:function(t,n){return l().then((o=>o.document.tpa.isApplicationInstalled(e,t,n)))},isAppSectionInstalled:function(t,n){return l().then((o=>o.document.tpa.isAppSectionInstalled(e,t,n)))},setStyleParams:function(t,n){return l().then((o=>o.document.tpa.setStyleParams(e,t,n)))},getStyleParams:function(t,n){return l().then((o=>o.document.tpa.getStyleParams(e,t,n)))},getSiteTextPresets:function(t,n){return l().then((o=>o.document.tpa.getSiteTextPresets(e,t,n)))},getSiteColors:function(t,n){return l().then((o=>o.document.tpa.getSiteColors(e,t,n)))},getPageRefByTPAPageId:function(t,n){return l().then((o=>o.document.tpa.getPageRefByTPAPageId(e,t,n)))},TPAComponentType:se}}function Rt(e){return{listChildren:function(t,n){return l().then((o=>o.document.vfs.listChildren(e,t,n)))},readFile:function(t,n){return l().then((o=>o.document.vfs.readFile(e,t,n)))},writeFile:function(t,n){return l().then((o=>o.document.vfs.writeFile(e,t,n)))},deleteFile:function(t,n){return l().then((o=>o.document.vfs.deleteFile(e,t,n)))},flush:function(t){return l().then((n=>n.document.vfs.flush(e,t)))},getViewerInfo:function(t){return l().then((n=>n.document.vfs.getViewerInfo(e,t)))}}}function Ot(e){return{hiddenComponents:{show:function(t,n){return l().then((o=>o.document.mobile.hiddenComponents.show(e,t,n)))}}}}function xt(e){const t=X.OPERATION_TYPES.COMP;return{get:function(n,o){return X.dsGetter({compRefsToAwait:o.componentRef,operationTypes:t,waitingType:X.WAITING_TYPES.COMPS},(t=>t.document.responsiveLayout.get(e,n,o)))},update:function(n,o){return X.dsUpdater({compRefsToAwait:o.componentRef,operationTypes:t},(t=>t.document.responsiveLayout.update(e,n,o)))}}}function Nt(e){return{registerToErrors:function(t,n){return l().then((t=>{const o=be+e.appDefinitionId;return y.api.set(o,n),t.document.errors.registerToErrorThrownForContext(e)}))},unRegisterToErrors:function(t){return l().then((n=>{const o=be+e.appDefinitionId;y.api.unset(o),n.document.errors.unRegisterToErrorThrownForContext(e,t)}))}}}function jt(e){return{convertAppPage:function(t,n){return l().then((o=>o.document.wixapps.migrate.convertAppPage(e,t,n)))}}}function Dt(e){return{getSkipToContent:function(t){return l().then((n=>n.document.accessibility.getSkipToContent(e,t)))},setSkipToContent:function(t,n){return l().then((o=>o.document.accessibility.setSkipToContent(e,t,n)))},getVisualFocus:function(t){return l().then((n=>n.document.accessibility.getVisualFocus(e,t)))},setVisualFocus:function(t,n){return l().then((o=>o.document.accessibility.setVisualFocus(e,t,n)))},getAutoDomReorder:function(t){return l().then((n=>n.document.accessibility.getAutoDomReorder(e,t)))},setAutoDomReorder:function(t,n){return l().then((o=>o.document.accessibility.setAutoDomReorder(e,t,n)))}}}function Mt(e){return{runAndWaitForApproval:function(t,n){return X.waitForAllChanges().then((()=>l().then((o=>o.document.transactions.runAndWaitForApproval(e,t,n).catch((e=>(X.clearQueue(),Promise.reject(e))))))))},isConflictError:function(e,t){const n="TransactionRejectionError"===t.type;return Promise.resolve(n)}}}function Gt(e){return{isGroupsPermissionsGranted:function(t,n){return l().then((o=>o.document.permissions.isGroupsPermissionsGranted(e,t,n)))},isCustomPermissionsGranted:function(t,n){return l().then((o=>o.document.permissions.isCustomPermissionsGranted(e,t,n)))}}}function Wt(e){return{save:function(){return Oe("document.save()"),Promise.resolve()},application:Ue(e),controllers:Le(e),history:Fe(e),info:at(e),language:ct(e),menu:ut(e),pages:gt(e),pagesGroup:mt(e),siteSegments:vt(e),siteMembers:ht(e),vfs:Rt(e),components:rt(e),fonts:it(e),routers:yt(e),theme:It(e),tpa:_t(e),mobile:Ot(e),responsiveLayout:xt(e),errors:Nt(e),wixapps:jt(e),accessibility:Dt(e),account:{isCoBranded:function(){return l().then((e=>e.document.account.isCoBranded()))}},environment:{screen:{getScreenResolution:function(){return l().then((e=>e.document.environment.screen.getScreenResolution()))}}},transactions:Mt(e),permissions:Gt(e)}}function Yt(e){function t(){return l().then((e=>e.appBuilder.appInfo.getName()))}function n(){return l().then((e=>e.appBuilder.appInfo.getNamespace()))}return{getAppName:t,getAppNamespace:n,appInfo:{getName:t,getNamespace:n,getAppDefId:function(){return l().then((e=>e.appBuilder.appInfo.getAppDefId()))}},widgets:{getAllSerialized:function(e){return l().then((e=>e.appBuilder.widgets.getAllSerialized()))}},definitions:{getAllSerialized:function(e){return l().then((e=>e.appBuilder.definitions.getAllSerialized()))}}}}function kt(e){return{grid:{addGridContainer:function(t,n){return l().then((o=>o.responsive.grid.addGridContainer(e,t,n)))},getLayouts:function(t,n){return l().then((o=>o.responsive.grid.getLayouts(e,t,n)))},updateLayout:function(t,n){return l().then((o=>o.responsive.grid.updateLayout(e,t,n)))},updateGaps:function(t,n){return l().then((o=>o.responsive.grid.updateGaps(e,t,n)))},addRow:function(t,n){return l().then((o=>o.responsive.grid.addRow(e,t,n)))},addColumn:function(t,n){return l().then((o=>o.responsive.grid.addColumn(e,t,n)))},removeRow:function(t,n){return l().then((o=>o.responsive.grid.removeRow(e,t,n)))},removeColumn:function(t,n){return l().then((o=>o.responsive.grid.removeColumn(e,t,n)))},getChildPositions:function(t,n){return l().then((o=>o.responsive.grid.getChildPositions(e,t,n)))},updateChildPositions:function(t,n){return l().then((o=>o.responsive.grid.updateChildPositions(e,t,n)))},deleteChild:function(t,n){return l().then((o=>o.responsive.grid.deleteChild(e,t,n)))},addChild:function(t,n){return l().then((o=>o.responsive.grid.addChild(e,t,n)))}}}}class Ut{constructor(e){let{editorSdkInstance:t,signedInstance:n}=e;this.editorSdk=t,this.signedInstance=n}getAppDefId(){return this.editorSdk.getAppDefId()}getInstanceId(){return this.editorSdk.getInstanceId()}async getExternalId(){return this.editorSdk.getExternalId()}async setExternalId(e){await this.editorSdk.setExternalId(e)}triggerSettingsUpdated(e,t){const n={scope:t,payload:e,source:"app-settings"};this.editorSdk.triggerUpdate(n)}onSettingsUpdated(){}}function Lt(e){return{createSdkAdapter:function(t,n){return X.dsGetter({compRefsToAwait:n.controllerRef,operationTypes:X.OPERATION_TYPES.COMP,waitingType:X.WAITING_TYPES.COMPS},(o=>Promise.all([o.document.info.getAppInstance(e,n),o.document.info.getAppInstanceId(e,n)]).then((r=>{let[i,a]=r;const c=Re(e,n,"appDefinitionId",{isRequired:!0});return new Ut({signedInstance:i,editorSdkInstance:{getExternalId:()=>o.document.controllers.getExternalId(e,t,{controllerRef:n.controllerRef}),setExternalId:r=>o.document.controllers.setExternalId(e,t,{controllerRef:n.controllerRef,externalId:r}),getAppDefId:()=>c,getInstanceId:()=>a,triggerUpdate:()=>{}}})}))))},getApiUrl:function(){return(h()?self.location.origin:window.location.origin)+"/_api/app-settings-service"}}}var Ft={};try{Ft.EventTarget=(new EventTarget).constructor}catch(e){!function(e,t){var n=e.create,o=e.defineProperty,r=i.prototype;function i(){t.set(this,n(null))}function a(e,t,n){o(e,t,{configurable:!0,writable:!0,value:n})}function c(e){var t=e.options;return t&&t.once&&e.target.removeEventListener(this.type,e.listener),"function"==typeof e.listener?e.listener.call(e.target,this):e.listener.handleEvent(this),this._stopImmediatePropagationFlag}a(r,"addEventListener",(function(e,n,o){for(var r=t.get(this),i=r[e]||(r[e]=[]),a=0,c=i.length;a<c;a++)if(i[a].listener===n)return;i.push({target:this,listener:n,options:o})})),a(r,"dispatchEvent",(function(e){var n=t.get(this)[e.type];return n&&(a(e,"target",this),a(e,"currentTarget",this),n.slice(0).some(c,e),delete e.currentTarget,delete e.target),!0})),a(r,"removeEventListener",(function(e,n){for(var o=t.get(this),r=o[e]||(o[e]=[]),i=0,a=r.length;i<a;i++)if(r[i].listener===n)return void r.splice(i,1)})),Ft.EventTarget=i}(Object,new WeakMap)}const Bt=Ft.EventTarget,Vt={},zt={};let Ht,Kt;function $t(e){return"default"===e.context}function qt(e){return"extension"===e.context}function Qt(e){return qt(e)?function(e){const t=e.appDefinitionId;if(!t)throw new Error("editorSDK needs to be bound to `extenstionName` / `appDefinitionId` to handle events");if(!zt[t]){const e=new Bt;zt[t]=e}return zt[t]}(e):$t(e)?function(e){const t=e.appDefinitionId;if(!t)throw new Error("editorSDK needs to be bound to `appDefinitionId` to handle events");if(!Vt[t]){const e=new Bt;Vt[t]=e}return Vt[t]}(e):Ht||(Ht=new Bt,Ht)}function Xt(e,t){const{addEventListener:n,removeEventListener:o,__dispatchEvent:r,removeAllListeners:i}=function(e){return{__dispatchEvent:async function(t){Qt(e).dispatchEvent(t)},addEventListener:async function(t,n,o){if($t(e)){const n="";(await l()).document.application.registerToCustomEvents(e,n,{eventTypes:[t]})}Qt(e).addEventListener(t,n,o)},removeEventListener:async function(t,n){Qt(e).removeEventListener(t,n)},removeAllListeners:async function(){$t(e)&&Vt[e.appDefinitionId]?delete Vt[e.appDefinitionId]:qt(e)&&zt[e.appDefinitionId]?delete zt[e.appDefinitionId]:Ht=null}}}(e),{getAuthStrategy:a}=function(e){return{getAuthStrategy:async function(){const t=await l();return{getAuthHeaders:async()=>({headers:{Authorization:await t.document.info.getAppInstance(e,"")}})}}}}(e);return Object.assign({editor:xe(e),document:Wt(e),appSettings:Lt(e),getAuthStrategy:a,addEventListener:n,removeEventListener:o,removeAllListeners:i,__dispatchEvent:r,preview:Q(e),selection:J(e),panel:H(),environment:Y(e),ide:U(e),utils:re(e),userPreferences:{site:ne(e)},application:Ue(e),controllers:Le(e),history:Fe(e),language:ct(e),menu:ut(e),pages:gt(e),pagesGroup:mt(e),siteSegments:vt(e),siteMembers:ht(e),vfs:Rt(e),components:rt(e),fonts:it(e),routers:yt(e),theme:It(e),tpa:_t(e),mobile:Ot(e),info:Object.assign($(e),at(e)),responsiveLayout:xt(e),wixapps:jt(e),accessibility:Object.assign(K(e),Dt(e))},function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];const n={appBuilder:Yt(),responsive:kt(e)};return t.reduce(((e,t)=>(n.hasOwnProperty(t)&&(e[t]=n[t]),e)),{})}(e,t))}function Jt(){return F=[],B=[],V=null,function(e){window.addEventListener("message",e,!1)}(z),b("editorAPI",parent).then((()=>{!function(e){var t=parent.postMessage?parent:parent.document.postMessage?parent.document:void 0;e.sourcePanel=window.name,t&&void 0!==t&&t.postMessage(e,"*")}({type:"PLATFORM_SDK_READY",intent:"PLATFORM_PANEL"})}))}function Zt(e){return Kt||(Kt={},(location.search.substring(1)||"").split("&").forEach((function(e){var t=e.split("=");Kt[t[0]]=decodeURIComponent(t[1])}))),Kt[e]||null}n(6108);const en={};"function"==typeof CustomEvent?en.CustomEvent=CustomEvent:"function"==typeof Event?en.CustomEvent=class extends Event{constructor(e,t){super(e,t),this.detail=t&&t.detail||null}}:"undefined"==typeof document?en.CustomEvent=class{constructor(e,t){if(void 0===e)throw new TypeError("Not enough arguments");this.type=e,this.bubbles=t&&!!t.bubbles||!1,this.cancelable=t&&!!t.cancelable||!1,this.composed=t&&!!t.composed||!1,this.detail=t&&t.detail||null}}:en.CustomEvent=function(e){return t[e]=new t("").constructor[e],t;function t(e,t){t||(t={});const n=document.createEvent("CustomEvent");return n.initCustomEvent(e,!!t.bubbles,!!t.cancelable,t.detail),n}}("prototype");const tn=en.CustomEvent,nn="undefined"==typeof globalThis?self:globalThis;let on;const rn=function(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return on=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"").split(","),je(t),L(n),(e=>(C=e,b("editor")))(e)},an=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";await b("editorAPI",e),on=t,je(n),L(o)},cn=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";Jt(),on=e,je(t)},sn=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;_[e]={};const r={};return N.forEach((i=>{r[i]=M(e,t[i]||{},i,n,o)})),r},un=e=>{delete _[e],N.forEach((t=>{const n=w(e,t);y.api.unset(n)}))};function pn(e){if(function(e){return void 0!==e.extensionName&&void 0===e.appDefinitionId}(e)){const{extensionName:t}=e;return{appDefinitionId:t,context:Ce.EXTENSION}}return{appDefinitionId:e.appDefinitionId,applicationId:e.applicationId,context:Ce.DEFAULT}}function dn(){return Xt(pn(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),on)}let fn={};if(!(h()||P()||"undefined"!=typeof window&&window===window.parent)){Jt();const e=Zt("extraNamespaces")||"";je(Zt("editorType"));const t=pn({applicationId:Zt("applicationId"),appDefinitionId:Zt("appDefinitionId")});fn=Xt(t,e.split(",")),function(e,t){let n=null;function o(t){var n;if("event propagation: event from sender"!==(null===(n=t.data)||void 0===n?void 0:n.type))return;const{eventType:o,eventPayload:r}=t.data;e.__dispatchEvent(new tn(o,{detail:r}))}nn.addEventListener("message",(function e(t){var r;"event propagation: connect receiver with sender"===(null===(r=t.data)||void 0===r?void 0:r.type)&&function(e){return!!function(e){var t;return"function"==typeof(null===(t=e.source)||void 0===t?void 0:t.postMessage)}(e)&&function(e){var t;const n=new URL(e);return(null===(t=nn.location)||void 0===t?void 0:t.hostname)===n.hostname||"static.parastorage.com"===n.hostname||n.hostname.endsWith(".wix.com")||n.hostname.endsWith(".editorx.com")||n.hostname.endsWith(".wix.dev")}(e.origin)}(t)&&(nn.removeEventListener("message",e),nn.addEventListener("beforeunload",(()=>{const e={type:"event propagation: receiver close"};null==n||n.postMessage(e)})),n=t.ports[0],n.addEventListener("message",o),n.start())}));const r={type:"event propagation: receiver ready",appDefinitionId:t.appDefinitionId};nn.parent.postMessage(r,"*")}(fn,{appDefinitionId:t.appDefinitionId})}const ln=fn.addEventListener,gn=fn.removeEventListener,mn=fn.document,Tn=fn.editor,yn=fn.appBuilder,vn=fn.appSettings,hn=fn.panel,Pn=fn.preview,An=fn.ide,wn=fn.selection,In=fn.environment,Sn=fn.userPreferences,En=fn.vfs,bn=fn.pages,Cn=fn.routers,_n=fn.controllers,Rn=fn.components,On=fn.history,xn=fn.tpa,Nn=fn.menu,jn=fn.pagesGroup,Dn=fn.language,Mn=fn.siteSegments,Gn=fn.siteMembers,Wn=fn.theme,Yn=fn.fonts,kn=fn.info,Un=fn.responsiveLayout,Ln=fn.errors,Fn=fn.wixapps,Bn=fn.accessibility})(),o})()));
//esm-shim-webpack-plugin-exports

//# sourceMappingURL=editorSDK.min.js.map