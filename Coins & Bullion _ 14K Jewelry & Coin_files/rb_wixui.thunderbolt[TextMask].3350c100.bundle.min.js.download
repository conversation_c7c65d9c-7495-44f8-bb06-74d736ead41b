!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[TextMask]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[TextMask]"]=t(require("react")):e["rb_wixui.thunderbolt[TextMask]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)({}).hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function i(e){var n=a[e];if(void 0!==n)return n.exports;var r=a[e]={exports:{}};return t[e](r,r.exports,i),r.exports}i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,{a:t}),t},i.d=function(e,t){for(var a in t)i.o(t,a)&&!i.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";i.r(n),i.d(n,{components:function(){return la}});var e={};i.r(e),i.d(e,{STATIC_MEDIA_URL:function(){return We},ph:function(){return je}});var t=i(448),a=i.n(t),r=i(5329),o=i.n(r);function c(e){var t,a,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(a=c(e[t]))&&(i&&(i+=" "),i+=a);else for(t in e)e[t]&&(i&&(i+=" "),i+=t);return i}var s=function(){for(var e,t,a=0,i="";a<arguments.length;)(e=arguments[a++])&&(t=c(e))&&(i&&(i+=" "),i+=t);return i},l="jhxvbR";const d="v1",u=2,h=1920,g=1920,m=1e3,p=1e3,f={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},_={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},T={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},E={[T.CENTER]:{x:.5,y:.5},[T.TOP_LEFT]:{x:0,y:0},[T.TOP_RIGHT]:{x:1,y:0},[T.TOP]:{x:.5,y:0},[T.BOTTOM_LEFT]:{x:0,y:1},[T.BOTTOM_RIGHT]:{x:1,y:1},[T.BOTTOM]:{x:.5,y:1},[T.RIGHT]:{x:1,y:.5},[T.LEFT]:{x:0,y:.5}},I={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},L={BG:"bg",IMG:"img",SVG:"svg"},b={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},w={classic:1,super:2},A={radius:"0.66",amount:"1.00",threshold:"0.01"},y={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},M=25e6,v=[1.5,2,4],O={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},S={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},C={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},R={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},G={AVIF:"AVIF",PAVIF:"PAVIF"};R.JPG,R.JPEG,R.JPE,R.PNG,R.GIF,R.WEBP;function N(e,...t){return function(...a){const i=a[a.length-1]||{},n=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?a[t]:i[t];n.push(o,e[r+1])})),n.join("")}}function x(e){return e[e.length-1]}const F=[R.PNG,R.JPEG,R.JPG,R.JPE,R.WIX_ICO_MP,R.WIX_MP,R.WEBP,R.AVIF],P=[R.JPEG,R.JPG,R.JPE];function k(e,t,a){return a&&t&&!(!(i=t.id)||!i.trim()||"none"===i.toLowerCase())&&Object.values(f).includes(e);var i}function B(e,t,a){return function(e,t,a=!1){return!((Y(e)||z(e))&&t&&!a)}(e,t,a)&&(function(e){return F.includes(Z(e))}(e)||function(e,t=!1){return U(e)&&t}(e,a))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function H(e){return Z(e)===R.PNG}function Y(e){return Z(e)===R.WEBP}function U(e){return Z(e)===R.GIF}function z(e){return Z(e)===R.AVIF}const $=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),D=["\\.","\\*"],j="_";function V(e){return function(e){return P.includes(Z(e))}(e)?R.JPG:H(e)?R.PNG:Y(e)?R.WEBP:U(e)?R.GIF:z(e)?R.AVIF:R.UNRECOGNIZED}function Z(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function W(e,t,a,i,n){let r;return r=n===_.FILL?function(e,t,a,i){return Math.max(a/e,i/t)}(e,t,a,i):n===_.FIT?function(e,t,a,i){return Math.min(a/e,i/t)}(e,t,a,i):1,r}function q(e,t,a,i,n,r){e=e||i.width,t=t||i.height;const{scaleFactor:o,width:c,height:s}=function(e,t,a,i,n){let r,o=a,c=i;if(r=W(e,t,a,i,n),n===_.FIT&&(o=e*r,c=t*r),o&&c&&o*c>M){const a=Math.sqrt(M/(o*c));o*=a,c*=a,r=W(e,t,o,c,n)}return{scaleFactor:r,width:o,height:c}}(e,t,i.width*n,i.height*n,a);return function(e,t,a,i,n,r,o){const{optimizedScaleFactor:c,upscaleMethodValue:s,forceUSM:l}=function(e,t,a,i){if("auto"===i)return function(e,t){const a=Q(e,t);return{optimizedScaleFactor:O[a].maxUpscale,upscaleMethodValue:w.classic,forceUSM:!1}}(e,t);if("super"===i)return function(e){return{optimizedScaleFactor:x(v),upscaleMethodValue:w.super,forceUSM:!(v.includes(e)||e>x(v))}}(a);return function(e,t){const a=Q(e,t);return{optimizedScaleFactor:O[a].maxUpscale,upscaleMethodValue:w.classic,forceUSM:!1}}(e,t)}(e,t,r,n);let d=a,u=i;if(r<=c)return{width:d,height:u,scaleFactor:r,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case _.FILL:d=a*(c/r),u=i*(c/r);break;case _.FIT:d=e*c,u=t*c}return{width:d,height:u,scaleFactor:c,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,c,s,r,o,a)}function J(e,t,a,i){const n=X(a)||function(e=T.CENTER){return E[e]}(i);return{x:Math.max(0,Math.min(e.width-t.width,n.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,n.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function K(e){return e.alignment&&I[e.alignment]||I[T.CENTER]}function X(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:ee(Math.max(0,Math.min(100,e.x))/100,2),y:ee(Math.max(0,Math.min(100,e.y))/100,2)}),t}function Q(e,t){const a=e*t;return a>O[S.HIGH].size?S.HIGH:a>O[S.MEDIUM].size?S.MEDIUM:a>O[S.LOW].size?S.LOW:S.TINY}function ee(e,t){const a=Math.pow(10,t||0);return(e*a/a).toFixed(t)}function te(e){return e&&e.upscaleMethod&&b[e.upscaleMethod.toUpperCase()]||b.AUTO}function ae(e,t){const a=Y(e)||z(e);return Z(e)===R.GIF||a&&t}const ie={isMobile:!1},ne=function(e){return ie[e]};function re(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,a=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&a,ie["isMobile"]=e}var e}function oe(e,t){const a={css:{container:{}}},{css:i}=a,{fittingType:n}=e;switch(n){case f.ORIGINAL_SIZE:case f.LEGACY_ORIGINAL_SIZE:case f.LEGACY_STRIP_ORIGINAL_SIZE:i.container.backgroundSize="auto",i.container.backgroundRepeat="no-repeat";break;case f.SCALE_TO_FIT:case f.LEGACY_STRIP_SCALE_TO_FIT:i.container.backgroundSize="contain",i.container.backgroundRepeat="no-repeat";break;case f.STRETCH:i.container.backgroundSize="100% 100%",i.container.backgroundRepeat="no-repeat";break;case f.SCALE_TO_FILL:case f.LEGACY_STRIP_SCALE_TO_FILL:i.container.backgroundSize="cover",i.container.backgroundRepeat="no-repeat";break;case f.TILE_HORIZONTAL:case f.LEGACY_STRIP_TILE_HORIZONTAL:i.container.backgroundSize="auto",i.container.backgroundRepeat="repeat-x";break;case f.TILE_VERTICAL:case f.LEGACY_STRIP_TILE_VERTICAL:i.container.backgroundSize="auto",i.container.backgroundRepeat="repeat-y";break;case f.TILE:case f.LEGACY_STRIP_TILE:i.container.backgroundSize="auto",i.container.backgroundRepeat="repeat";break;case f.LEGACY_STRIP_FIT_AND_TILE:i.container.backgroundSize="contain",i.container.backgroundRepeat="repeat";break;case f.FIT_AND_TILE:case f.LEGACY_BG_FIT_AND_TILE:i.container.backgroundSize="auto",i.container.backgroundRepeat="repeat";break;case f.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:i.container.backgroundSize="auto",i.container.backgroundRepeat="repeat-x";break;case f.LEGACY_BG_FIT_AND_TILE_VERTICAL:i.container.backgroundSize="auto",i.container.backgroundRepeat="repeat-y";break;case f.LEGACY_BG_NORMAL:i.container.backgroundSize="auto",i.container.backgroundRepeat="no-repeat"}switch(t.alignment){case T.CENTER:i.container.backgroundPosition="center center";break;case T.LEFT:i.container.backgroundPosition="left center";break;case T.RIGHT:i.container.backgroundPosition="right center";break;case T.TOP:i.container.backgroundPosition="center top";break;case T.BOTTOM:i.container.backgroundPosition="center bottom";break;case T.TOP_RIGHT:i.container.backgroundPosition="right top";break;case T.TOP_LEFT:i.container.backgroundPosition="left top";break;case T.BOTTOM_RIGHT:i.container.backgroundPosition="right bottom";break;case T.BOTTOM_LEFT:i.container.backgroundPosition="left bottom"}return a}const ce={[T.CENTER]:"center",[T.TOP]:"top",[T.TOP_LEFT]:"top left",[T.TOP_RIGHT]:"top right",[T.BOTTOM]:"bottom",[T.BOTTOM_LEFT]:"bottom left",[T.BOTTOM_RIGHT]:"bottom right",[T.LEFT]:"left",[T.RIGHT]:"right"},se={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function le(e,t){const a={css:{container:{},img:{}}},{css:i}=a,{fittingType:n}=e,r=t.alignment;switch(i.container.position="relative",n){case f.ORIGINAL_SIZE:case f.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(i.img.width=e.parts[0].width,i.img.height=e.parts[0].height):(i.img.width=e.src.width,i.img.height=e.src.height);break;case f.SCALE_TO_FIT:case f.LEGACY_FIT_WIDTH:case f.LEGACY_FIT_HEIGHT:case f.LEGACY_FULL:i.img.width=t.width,i.img.height=t.height,i.img.objectFit="contain",i.img.objectPosition=ce[r]||"unset";break;case f.LEGACY_BG_NORMAL:i.img.width="100%",i.img.height="100%",i.img.objectFit="none",i.img.objectPosition=ce[r]||"unset";break;case f.STRETCH:i.img.width=t.width,i.img.height=t.height,i.img.objectFit="fill";break;case f.SCALE_TO_FILL:i.img.width=t.width,i.img.height=t.height,i.img.objectFit="cover"}if("number"==typeof i.img.width&&"number"==typeof i.img.height&&(i.img.width!==t.width||i.img.height!==t.height)){const e=Math.round((t.height-i.img.height)/2),a=Math.round((t.width-i.img.width)/2);Object.assign(i.img,se,function(e,t,a){return{[T.TOP_LEFT]:{top:0,left:0},[T.TOP_RIGHT]:{top:0,right:0},[T.TOP]:{top:0,left:t},[T.BOTTOM_LEFT]:{bottom:0,left:0},[T.BOTTOM_RIGHT]:{bottom:0,right:0},[T.BOTTOM]:{bottom:0,left:t},[T.RIGHT]:{top:e,right:0},[T.LEFT]:{top:e,left:0},[T.CENTER]:{width:a.width,height:a.height,objectFit:"none"}}}(e,a,t)[r])}return a}function de(e,t){const a={css:{container:{}},attr:{container:{},img:{}}},{css:i,attr:n}=a,{fittingType:r}=e,o=t.alignment,{width:c,height:s}=e.src;let l;switch(i.container.position="relative",r){case f.ORIGINAL_SIZE:case f.LEGACY_ORIGINAL_SIZE:case f.TILE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=c,n.img.height=s),n.img.preserveAspectRatio="xMidYMid slice";break;case f.SCALE_TO_FIT:case f.LEGACY_FIT_WIDTH:case f.LEGACY_FIT_HEIGHT:case f.LEGACY_FULL:n.img.width="100%",n.img.height="100%",n.img.transform="",n.img.preserveAspectRatio="";break;case f.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="none";break;case f.SCALE_TO_FILL:B(e.src.id)?(n.img.width=t.width,n.img.height=t.height):(l=function(e,t,a,i,n){const r=W(e,t,a,i,n);return{width:Math.round(e*r),height:Math.round(t*r)}}(c,s,t.width,t.height,_.FILL),n.img.width=l.width,n.img.height=l.height),n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){let e,a,i=0,c=0;r===f.TILE?(e=t.width%n.img.width,a=t.height%n.img.height):(e=t.width-n.img.width,a=t.height-n.img.height);const s=Math.round(e/2),l=Math.round(a/2);switch(o){case T.TOP_LEFT:i=0,c=0;break;case T.TOP:i=s,c=0;break;case T.TOP_RIGHT:i=e,c=0;break;case T.LEFT:i=0,c=l;break;case T.CENTER:i=s,c=l;break;case T.RIGHT:i=e,c=l;break;case T.BOTTOM_LEFT:i=0,c=a;break;case T.BOTTOM:i=s,c=a;break;case T.BOTTOM_RIGHT:i=e,c=a}n.img.x=i,n.img.y=c}return n.container.width=t.width,n.container.height=t.height,n.container.viewBox=[0,0,t.width,t.height].join(" "),a}function ue(e,t,a){let i;switch(t.crop&&(i=function(e,t){const a=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),i=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return a&&i&&(e.width!==a||e.height!==i)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:a,height:i}:null}(t,t.crop),i&&(e.src.width=i.width,e.src.height=i.height,e.src.isCropped=!0,e.parts.push(ge(i)))),e.fittingType){case f.SCALE_TO_FIT:case f.LEGACY_FIT_WIDTH:case f.LEGACY_FIT_HEIGHT:case f.LEGACY_FULL:case f.FIT_AND_TILE:case f.LEGACY_BG_FIT_AND_TILE:case f.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case f.LEGACY_BG_FIT_AND_TILE_VERTICAL:case f.LEGACY_BG_NORMAL:e.parts.push(he(e,a));break;case f.SCALE_TO_FILL:e.parts.push(function(e,t){const a=q(e.src.width,e.src.height,_.FILL,t,e.devicePixelRatio,e.upscaleMethod),i=X(e.focalPoint);return{transformType:i?_.FILL_FOCAL:_.FILL,width:Math.round(a.width),height:Math.round(a.height),alignment:K(t),focalPointX:i&&i.x,focalPointY:i&&i.y,upscale:a.scaleFactor>1,forceUSM:a.forceUSM,scaleFactor:a.scaleFactor,cssUpscaleNeeded:a.cssUpscaleNeeded,upscaleMethodValue:a.upscaleMethodValue}}(e,a));break;case f.STRETCH:e.parts.push(function(e,t){const a=W(e.src.width,e.src.height,t.width,t.height,_.FILL),i={...t};return i.width=e.src.width*a,i.height=e.src.height*a,he(e,i)}(e,a));break;case f.TILE_HORIZONTAL:case f.TILE_VERTICAL:case f.TILE:case f.LEGACY_ORIGINAL_SIZE:case f.ORIGINAL_SIZE:i=J(e.src,a,e.focalPoint,a.alignment),e.src.isCropped?(Object.assign(e.parts[0],i),e.src.width=i.width,e.src.height=i.height):e.parts.push(ge(i));break;case f.LEGACY_STRIP_TILE_HORIZONTAL:case f.LEGACY_STRIP_TILE_VERTICAL:case f.LEGACY_STRIP_TILE:case f.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:_.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:K(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(a));break;case f.LEGACY_STRIP_SCALE_TO_FIT:case f.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:_.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(a));break;case f.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:_.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:K(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(a))}}function he(e,t){const a=q(e.src.width,e.src.height,_.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?_.FIT:_.FILL,width:Math.round(a.width),height:Math.round(a.height),alignment:I.center,upscale:a.scaleFactor>1,forceUSM:a.forceUSM,scaleFactor:a.scaleFactor,cssUpscaleNeeded:a.cssUpscaleNeeded,upscaleMethodValue:a.upscaleMethodValue}}function ge(e){return{transformType:_.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function me(e,t){t=t||{},e.quality=function(e,t){const a=e.fileType===R.PNG,i=e.fileType===R.JPG,n=e.fileType===R.WEBP,r=e.fileType===R.AVIF,o=i||a||n||r;if(o){const i=x(e.parts),n=(c=i.width,s=i.height,O[Q(c,s)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:n;return r=a?r+5:r,r}var c,s;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,a="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,i="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&a&&i}(t.unsharpMask))return{radius:ee(t.unsharpMask?.radius,2),amount:ee(t.unsharpMask?.amount,2),threshold:ee(t.unsharpMask?.threshold,2)};if(("number"!=typeof(a=(a=t.unsharpMask)||{}).radius||isNaN(a.radius)||0!==a.radius||"number"!=typeof a.amount||isNaN(a.amount)||0!==a.amount||"number"!=typeof a.threshold||isNaN(a.threshold)||0!==a.threshold)&&function(e){const t=x(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===_.FIT}(e))return A;var a;return}(e,t),e.filters=function(e){const t=e.filters||{},a={};pe(t[C.CONTRAST],-100,100)&&(a[C.CONTRAST]=t[C.CONTRAST]);pe(t[C.BRIGHTNESS],-100,100)&&(a[C.BRIGHTNESS]=t[C.BRIGHTNESS]);pe(t[C.SATURATION],-100,100)&&(a[C.SATURATION]=t[C.SATURATION]);pe(t[C.HUE],-180,180)&&(a[C.HUE]=t[C.HUE]);pe(t[C.BLUR],0,100)&&(a[C.BLUR]=t[C.BLUR]);return a}(t)}function pe(e,t,a){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=a}function fe(e,t,a,i){const n=function(e){return e?.isSEOBot??!1}(i),r=V(t.id),o=function(e,t){const a=/\.([^.]*)$/,i=new RegExp(`(${$.concat(D).join("|")})`,"g");if(t&&t.length){let e=t;const n=t.match(a);return n&&F.includes(n[1])&&(e=t.replace(a,"")),encodeURIComponent(e).replace(i,j)}const n=e.match(/\/(.*?)$/);return(n?n[1]:e).replace(a,"")}(t.id,t.name),c=n?1:function(e){return Math.min(e.pixelAspectRatio||1,u)}(a),s=Z(t.id),l=s,d=B(t.id,i?.hasAnimation,i?.allowAnimatedTransform),h={fileName:o,fileExtension:s,fileType:r,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:ae(t.id,i?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:c,quality:0,upscaleMethod:te(i),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:d};return d&&(ue(h,t,a),me(h,i)),h}function _e(e,t,a){const i={...a},n=ne("isMobile");switch(e){case f.LEGACY_BG_FIT_AND_TILE:case f.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case f.LEGACY_BG_FIT_AND_TILE_VERTICAL:case f.LEGACY_BG_NORMAL:const e=n?m:h,a=n?p:g;i.width=Math.min(e,t.width),i.height=Math.min(a,Math.round(i.width/(t.width/t.height))),i.pixelAspectRatio=1}return i}const Te=N`fit/w_${"width"},h_${"height"}`,Ee=N`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Ie=N`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,Le=N`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,be=N`crop/w_${"width"},h_${"height"},al_${"alignment"}`,we=N`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Ae=N`,lg_${"upscaleMethodValue"}`,ye=N`,q_${"quality"}`,Me=N`,quality_auto`,ve=N`,usm_${"radius"}_${"amount"}_${"threshold"}`,Oe=N`,bl`,Se=N`,wm_${"watermark"}`,Ce={[C.CONTRAST]:N`,con_${"contrast"}`,[C.BRIGHTNESS]:N`,br_${"brightness"}`,[C.SATURATION]:N`,sat_${"saturation"}`,[C.HUE]:N`,hue_${"hue"}`,[C.BLUR]:N`,blur_${"blur"}`},Re=N`,enc_auto`,Ge=N`,enc_avif`,Ne=N`,enc_pavif`,xe=N`,pstr`;function Fe(e,t,a,i={},n){if(B(t.id,i?.hasAnimation,i?.allowAnimatedTransform)){if(Y(t.id)||z(t.id)){const{alignment:r,...o}=a;t.focalPoint={x:void 0,y:void 0},delete t?.crop,n=fe(e,t,o,i)}else n=n||fe(e,t,a,i);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case _.CROP:t.push(Le(e));break;case _.LEGACY_CROP:t.push(be(e));break;case _.LEGACY_FILL:let a=we(e);e.upscale&&(a+=Ae(e)),t.push(a);break;case _.FIT:let i=Te(e);e.upscale&&(i+=Ae(e)),t.push(i);break;case _.FILL:let n=Ee(e);e.upscale&&(n+=Ae(e)),t.push(n);break;case _.FILL_FOCAL:let r=Ie(e);e.upscale&&(r+=Ae(e)),t.push(r)}}));let a=t.join("/");return e.quality&&(a+=ye(e)),e.unsharpMask&&(a+=ve(e.unsharpMask)),e.progressive||(a+=Oe(e)),e.watermark&&(a+=Se(e)),e.filters&&(a+=Object.keys(e.filters).map((t=>Ce[t](e.filters))).join("")),e.fileType!==R.GIF&&(e.encoding===G.AVIF?(a+=Ge(e),a+=Me(e)):e.encoding===G.PAVIF?(a+=Ne(e),a+=Me(e)):e.autoEncode&&(a+=Re(e))),e.src?.isAnimated&&e.transformed&&(a+=xe(e)),`${e.src.id}/${d}/${a}/${e.fileName}.${e.preferredExtension}`}(n)}return t.id}const Pe={[T.CENTER]:"50% 50%",[T.TOP_LEFT]:"0% 0%",[T.TOP_RIGHT]:"100% 0%",[T.TOP]:"50% 0%",[T.BOTTOM_LEFT]:"0% 100%",[T.BOTTOM_RIGHT]:"100% 100%",[T.BOTTOM]:"50% 100%",[T.RIGHT]:"100% 50%",[T.LEFT]:"0% 50%"},ke=Object.entries(Pe).reduce(((e,[t,a])=>(e[a]=t,e)),{}),Be=[f.TILE,f.TILE_HORIZONTAL,f.TILE_VERTICAL,f.LEGACY_BG_FIT_AND_TILE,f.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,f.LEGACY_BG_FIT_AND_TILE_VERTICAL],He=[f.LEGACY_ORIGINAL_SIZE,f.ORIGINAL_SIZE,f.LEGACY_BG_NORMAL];function Ye(e,t,{width:a,height:i}){return e===f.TILE&&t.width>a&&t.height>i}function Ue(e,{width:t,height:a}){if(!t||!a){const i=t||Math.min(980,e.width),n=i/e.width;return{width:i,height:a||e.height*n}}return{width:t,height:a}}function ze(e,t,a,i="center"){const n={img:{},container:{}};if(e===f.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return ke[t]||""}(t.focalPoint),r=e||i;t.focalPoint&&!e?n.img={objectPosition:$e(t,a,t.focalPoint)}:n.img={objectPosition:Pe[r]}}else[f.LEGACY_ORIGINAL_SIZE,f.ORIGINAL_SIZE].includes(e)?n.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:Be.includes(e)&&(n.container={backgroundSize:`${t.width}px ${t.height}px`});return n}function $e(e,t,a){const{width:i,height:n}=e,{width:r,height:o}=t,{x:c,y:s}=a;if(!r||!o)return`${c}% ${s}%`;const l=Math.max(r/i,o/n),d=i*l,u=n*l,h=Math.max(0,Math.min(d-r,d*(c/100)-r/2)),g=Math.max(0,Math.min(u-o,u*(s/100)-o/2));return`${h&&Math.floor(h/(d-r)*100)}% ${g&&Math.floor(g/(u-o)*100)}%`}const De={width:"100%",height:"100%"};function je(e,t,a,i={}){const{autoEncode:n=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:c,allowAnimatedTransform:s,encoding:l}=i;if(!k(e,t,a))return y;const d=void 0===s||s,u=B(t.id,c,d);if(!u||o)return Ve(e,t,a,{...i,autoEncode:n,useSrcset:u});const h={...a,...Ue(t,a)},{alignment:g,htmlTag:m}=h,p=Ye(e,t,h),_=function(e,t,{width:a,height:i},n=!1){if(n)return{width:a,height:i};const r=!He.includes(e),o=Ye(e,t,{width:a,height:i}),c=!o&&Be.includes(e),s=c?t.width:a,l=c?t.height:i,d=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(s,H(t.id)):1;return{width:o?1920:s*d,height:l*d}}(e,t,h,r),T=function(e,t,a){return a?0:Be.includes(t)?1:e>200?2:3}(h.width,e,r),E=function(e,t){const a=Be.includes(e)&&!t;return e===f.SCALE_TO_FILL||a?f.SCALE_TO_FIT:e}(e,p),I=ze(e,t,a,g),{uri:L}=Ve(E,t,{..._,alignment:g,htmlTag:m},{autoEncode:n,filters:T?{blur:T}:{},hasAnimation:c,allowAnimatedTransform:d,encoding:l}),{attr:b={},css:w}=Ve(e,t,{...h,alignment:g,htmlTag:m},{});return w.img=w.img||{},w.container=w.container||{},Object.assign(w.img,I.img,De),Object.assign(w.container,I.container),{uri:L,css:w,attr:b,transformed:!0}}function Ve(e,t,a,i){let n={};if(k(e,t,a)){const r=_e(e,t,a),o=fe(e,t,r,i);n.uri=Fe(e,t,r,i,o),i?.useSrcset&&(n.srcset=function(e,t,a,i,n){const r=a.pixelAspectRatio||1;return{dpr:[`${1===r?n.uri:Fe(e,t,{...a,pixelAspectRatio:1},i)} 1x`,`${2===r?n.uri:Fe(e,t,{...a,pixelAspectRatio:2},i)} 2x`]}}(e,t,r,i,n)),Object.assign(n,function(e,t){let a;return a=t.htmlTag===L.BG?oe:t.htmlTag===L.SVG?de:le,a(e,t)}(o,r),{transformed:o.transformed})}else n=y;return n}const Ze="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;re();re();const We=Ze,{STATIC_MEDIA_URL:qe}=e,Je=({fittingType:e,src:t,target:a,options:i})=>{const n=je(e,t,a,{...i,autoEncode:!0});return n?.uri&&!/^[a-z]+:/.test(n.uri)&&(n.uri=`${qe}${n.uri}`),n},Ke=/^[a-z]+:/,Xe=e=>{const{id:t,containerId:a,uri:i,alt:n,name:o="",role:c,width:s,height:d,displayMode:u,devicePixelRatio:h,quality:g,alignType:m,bgEffectName:p="",focalPoint:f,upscaleMethod:_,className:T="",crop:E,imageStyles:I={},targetWidth:L,targetHeight:b,targetScale:w,onLoad:A=()=>{},onError:y=()=>{},shouldUseLQIP:M,containerWidth:v,containerHeight:O,getPlaceholder:S,isInFirstFold:C,placeholderTransition:R,socialAttrs:G,isSEOBot:N,skipMeasure:x,hasAnimation:F,encoding:P}=e,k=r.useRef(null);let B="";const H="blur"===R,Y=r.useRef(null);if(!Y.current)if(S||M||C||N){const e={upscaleMethod:_,...g||{},shouldLoadHQImage:C,isSEOBot:N,hasAnimation:F,encoding:P};Y.current=(S||Je)({fittingType:u,src:{id:i,width:s,height:d,crop:E,name:o,focalPoint:f},target:{width:v,height:O,alignment:m,htmlTag:"img"},options:e}),B=!Y.current.transformed||C||N?"":"true"}else Y.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const U=!N&&(S||M)&&!C&&Y.current.transformed,z=r.useMemo((()=>JSON.stringify({containerId:a,...a&&{containerId:a},...m&&{alignType:m},...x&&{skipMeasure:!0},displayMode:u,...v&&{targetWidth:v},...O&&{targetHeight:O},...L&&{targetWidth:L},...b&&{targetHeight:b},...w&&{targetScale:w},isLQIP:U,isSEOBot:N,lqipTransition:R,encoding:P,imageData:{width:s,height:d,uri:i,name:o,displayMode:u,hasAnimation:F,...g&&{quality:g},...h&&{devicePixelRatio:h},...f&&{focalPoint:f},...E&&{crop:E},..._&&{upscaleMethod:_}}})),[a,m,x,u,v,O,L,b,w,U,N,R,P,s,d,i,o,F,g,h,f,E,_]),$=Y.current,D=$?.uri,j=$?.srcset,V=$.css?.img,Z=`${l} ${T}`;r.useEffect((()=>{const e=k.current;A&&e?.currentSrc&&e?.complete&&A({target:e})}),[]);const W=$&&!$?.transformed?`max(${s}px, 100%)`:L?`${L}px`:null;return r.createElement("wow-image",{id:t,class:Z,"data-image-info":z,"data-motion-part":`BG_IMG ${a}`,"data-bg-effect-name":p,"data-has-ssr-src":B,"data-animate-blur":!N&&U&&H?"":void 0,style:W?{"--wix-img-max-width":W}:{}},r.createElement("img",{src:D,ref:k,alt:n||"",role:c,style:{...V,...I},onLoad:A,onError:y,width:v||void 0,height:O||void 0,...G,srcSet:C?j?.dpr?.map((e=>Ke.test(e)?e:`${qe}${e}`)).join(", "):void 0,fetchpriority:C?"high":void 0,loading:!1===C?"lazy":void 0,suppressHydrationWarning:!0}))};var Qe="Tj01hh";var et=e=>{var t,i;const{id:n,alt:o,role:c,className:l,imageStyles:d={},targetWidth:u,targetHeight:h,onLoad:g,onError:m,containerWidth:p,containerHeight:f,isInFirstFold:_,socialAttrs:T,skipMeasure:E,responsiveImageProps:I,zoomedImageResponsiveOverride:L,displayMode:b}=e,w=u||p,A=h||f,{fallbackSrc:y,srcset:M,sources:v,css:O}=I||{},{width:S,height:C,...R}=(null==I||null==(t=I.css)?void 0:t.img)||{},G="original_size"===b?null==I||null==(i=I.css)?void 0:i.img:R;var N;return y&&M&&O?r.createElement("img",a()({fetchpriority:_?"high":void 0,loading:!1===_?"lazy":void 0,sizes:w+"px",srcSet:E?null==L?void 0:L.srcset:null==I?void 0:I.srcset,id:n,src:y,alt:o||"",role:c,style:{...d,...E?{...null==L||null==(N=L.css)?void 0:N.img}:{...G}},onLoad:g,onError:m,className:s(l,Qe),width:w,height:A},T)):y&&v&&O?r.createElement("picture",null,v.map((e=>{let{srcset:t,media:a,sizes:i}=e;return r.createElement("source",{key:a,srcSet:t,media:a,sizes:i})})),r.createElement("img",a()({fetchpriority:_?"high":void 0,loading:!1===_?"lazy":void 0,id:n,src:v[0].fallbackSrc,alt:o||"",role:c,style:{...d,objectFit:v[0].imgStyle.objectFit,objectPosition:v[0].imgStyle.objectPosition},onLoad:g,onError:m,className:s(l,Qe),width:w,height:A},T))):r.createElement(Xe,e)};var tt=e=>{var t,a,i;const{className:n,customIdPrefix:o,getPlaceholder:c,hasAnimation:s,...l}=e,d=r.useMemo((()=>JSON.stringify({containerId:l.containerId,alignType:l.alignType,fittingType:l.displayMode,hasAnimation:s,imageData:{width:l.width,height:l.height,uri:l.uri,name:l.name,...l.quality&&{quality:l.quality},displayMode:l.displayMode}})),[l,s]),u=r.useRef(null);u.current||(u.current=c?c({fittingType:l.displayMode,src:{id:l.uri,width:l.width,height:l.height,name:l.name},target:{width:l.containerWidth,height:l.containerHeight,alignment:l.alignType,htmlTag:"bg"},options:{hasAnimation:s,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const h=u.current,g=null!=(t=null==h?void 0:h.uri)?t:"",m=null!=(a=null==(i=h.css)?void 0:i.container)?a:{},p=Object.assign(g?{backgroundImage:"url("+g+")"}:{},m);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+l.containerId,class:n,style:p,"data-tiled-image-info":d,"data-has-bg-scroll-effect":l.hasBgScrollEffect||"","data-bg-effect-name":l.bgEffectName||"","data-motion-part":"BG_IMG "+l.containerId})};const at=new RegExp("<%= compId %>","g"),it=(e,t)=>e.replace(at,t);var nt=e=>null==e?void 0:e.replace(":hover",""),rt="bX9O_S",ot="Z_wCwr",ct="Jxk_UL",st="K8MSra",lt="YTb3b4";const dt={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var ut=e=>{const{id:t,videoRef:i,videoInfo:n,posterImageInfo:o,muted:c,preload:l,loop:d,alt:u,isVideoEnabled:h,getPlaceholder:g,extraClassName:m=""}=e;n.containerId=nt(n.containerId);const p=r.useMemo((()=>JSON.stringify(n)),[n]),f=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+n.containerId,className:lt},r.createElement("defs",{dangerouslySetInnerHTML:{__html:it(o.filterEffectSvgString,n.containerId)}})),r.createElement(et,a()({key:n.videoId+"_img",id:o.containerId+"_img",className:s(ot,ct,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,dt,{getPlaceholder:g})));return h?r.createElement("wix-video",{id:t,"data-video-info":p,"data-motion-part":"BG_IMG "+n.containerId,class:s(rt,"bgVideo",m)},r.createElement("video",{key:n.videoId+"_video",ref:i,id:n.containerId+"_video",className:st,crossOrigin:"anonymous","aria-label":u,playsInline:!0,preload:l,muted:c,loop:d}),f):f},ht="SUz0WK";var gt=e=>{const{id:t,containerId:a,pageId:i,children:n,bgEffectName:o="",containerSize:c}=e;return r.createElement("wix-bg-media",{id:t,class:ht,"data-container-id":a,"data-container-size":((null==c?void 0:c.width)||0)+", "+((null==c?void 0:c.height)||0),"data-page-id":i,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+a},n)};const mt="bgOverlay";var pt="m4khSP",ft="FNxOn5";var _t=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":mt,className:pt},t&&r.createElement(tt,a()({customIdPrefix:"bgImgOverlay_",className:ft},t)))};const Tt="bgLayers",Et="colorUnderlay",It="mediaPadding",Lt="canvas";var bt="MW5IWV",wt="N3eg0s",At="Kv1aVt",yt="dLPlxY",Mt="VgO9Yg",vt="LWbAav",Ot="yK6aSC",St="K_YxMd",Ct="NGjcJN",Rt="mNGsUM",Gt="I8xA4L";const Nt="bgImage";var xt=e=>{const{videoRef:t,canvasRef:i,hasBgFullscreenScrollEffect:n,image:o,backgroundImage:c,backgroundMedia:l,video:d,backgroundOverlay:u,shouldPadMedia:h,extraClass:g="",shouldRenderUnderlay:m=!d,reducedMotion:p=!1,getPlaceholder:f,hasCanvasAnimation:_,useWixMediaCanvas:T,onClick:E}=e,{onImageLoad:I}=(e=>{let{onReady:t,image:a}=e;return(0,r.useEffect)((()=>{t&&!a&&t()}),[t,a]),{onImageLoad:e=>{null!=a&&a.onLoad&&a.onLoad(e),t&&t()}}})(e),L=nt(e.containerId),b="img_"+nt(L),w=o&&r.createElement(et,a()({id:b,className:s(At,yt,Rt,Nt),imageStyles:{width:"100%",height:"100%"},getPlaceholder:f},o,{onLoad:I})),A=c&&r.createElement(tt,a()({},c,{containerId:L,className:s(At,yt,Rt,Nt),getPlaceholder:f})),y=d&&r.createElement(ut,a()({id:"videoContainer_"+L},d,{extraClassName:Ot,reducedMotion:p,videoRef:t,getPlaceholder:f})),M=T&&i||_?r.createElement("wix-media-canvas",{"data-container-id":L,class:_?Gt:""},w,A,y,r.createElement("canvas",{id:L+"webglcanvas",className:s(St,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Lt})):r.createElement(r.Fragment,null,w,A,y,i&&r.createElement("canvas",{id:L+"webglcanvas",ref:i,className:s(St,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":Lt})),v=l?r.createElement(gt,a()({id:"bgMedia_"+L},l),M):r.createElement("div",{id:"bgMedia_"+L,"data-motion-part":"BG_MEDIA "+L,className:Mt},M),O=u&&r.createElement(_t,u);return r.createElement("div",{id:Tt+"_"+L,"data-hook":Tt,"data-motion-part":"BG_LAYER "+L,className:s(bt,g,{[wt]:n}),onClick:E},m&&r.createElement("div",{"data-testid":Et,className:s(vt,At)}),h?r.createElement("div",{"data-testid":It,className:Ct},v,O):r.createElement(r.Fragment,null,v,O))};const Ft=(e,t=0,{leading:a=!1,trailing:i=!0}={})=>{let n=null;return function(...r){a&&null===n&&e.apply(this,r),n&&clearTimeout(n),n=i&&a&&!n?setTimeout((()=>{n=null}),t):setTimeout((()=>{i&&e.apply(this,r),n=null}),t)}},Pt=e=>Object.entries(e).reduce(((e,[t,a])=>(t.includes("data-")&&(e[t]=a),e)),{});const kt=13,Bt=27;function Ht(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const Yt=Ht(32),Ut=Ht(kt),zt=e=>{Ut(e),Yt(e)},$t=(Ht(Bt),{root:"linkElement"}),Dt=(e,t)=>{const{href:i,role:n,target:o,rel:c,className:s="",children:l,linkPopupId:d,anchorDataId:u,anchorCompId:h,tabIndex:g,dataTestId:m=$t.root,title:p,onClick:f,onDoubleClick:_,onMouseEnter:T,onMouseLeave:E,onFocus:I,onFocusCapture:L,onBlurCapture:b,"aria-live":w,"aria-disabled":A,"aria-label":y,"aria-labelledby":M,"aria-pressed":v,"aria-expanded":O,"aria-describedby":S,"aria-haspopup":C,"aria-current":R,dataPreview:G,dataPart:N}=e,x=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(d);let F;switch(x){case"Enter":F=Ut;break;case"Space":F=Yt;break;case"SpaceOrEnter":F=zt;break;default:F=void 0}return void 0!==i||d?r.createElement("a",a()({},Pt(e),{"data-testid":m,"data-popupid":d,"data-anchor":u,"data-anchor-comp-id":h,"data-preview":G,"data-part":N,href:i||void 0,target:o,role:d?"button":n,rel:c,className:s,onKeyDown:F,"aria-live":w,"aria-disabled":A,"aria-label":y,"aria-labelledby":M,"aria-pressed":v,"aria-expanded":O,"aria-haspopup":C,"aria-describedby":S,"aria-current":R,title:p,onClick:f,onMouseEnter:T,onMouseLeave:E,onDoubleClick:_,onFocus:I,onFocusCapture:L,onBlurCapture:b,ref:t,tabIndex:d?0:g}),l):r.createElement("div",a()({},Pt(e),{"data-testid":m,"data-preview":G,"data-part":N,className:s,tabIndex:g,"aria-label":y,"aria-labelledby":M,"aria-haspopup":C,"aria-disabled":A,"aria-expanded":O,title:p,role:n,onClick:f,onDoubleClick:_,onMouseEnter:T,onMouseLeave:E,ref:t}),l)};var jt=r.forwardRef(Dt);var Vt="elEp3H",Zt="w681pN",Wt="drrrT5",qt="UZDyCY",Jt="wKTolk",Kt="MrRu4F",Xt="vFkhXO",Qt="YcIVz3";const ea=e=>{const{text:t,extraStyle:a,align:i,direction:n,lineHeight:r,letterSpacing:c,size:s,family:l,weight:d,style:u,decoration:h,transform:g}=e;let m;switch(i){case"start":default:m="rtl"===n?"100%":"0";break;case"middle":m="50%";break;case"end":m="rtl"===n?"0":"100%"}return o().createElement(o().Fragment,null,t.split("\n").map(((e,t)=>o().createElement("text",{key:"text-line-"+t,x:m,y:t*r+"em",style:{letterSpacing:""+(null==c?void 0:c.value)+(null==c?void 0:c.type),direction:n,fill:"#000",fontSize:s+"px",fontFamily:l,fontWeight:d,fontStyle:u,textAnchor:i,textDecoration:h,textTransform:g,...a}},e.trim()))))},ta=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M7.5,5 C8.32842712,5 9,5.67157288 9,6.5 L9,11.5 C9,12.3284271 8.32842712,13 7.5,13 C6.67157288,13 6,12.3284271 6,11.5 L6,6.5 C6,5.67157288 6.67157288,5 7.5,5 Z M11.5,5 C12.3284271,5 13,5.67157288 13,6.5 L13,11.5 C13,12.3284271 12.3284271,13 11.5,13 C10.6715729,13 10,12.3284271 10,11.5 L10,6.5 C10,5.67157288 10.6715729,5 11.5,5 Z M7.5,6 C7.22385763,6 7,6.22385763 7,6.5 L7,11.5 C7,11.7761424 7.22385763,12 7.5,12 C7.77614237,12 8,11.7761424 8,11.5 L8,6.5 C8,6.22385763 7.77614237,6 7.5,6 Z M11.5,6 C11.2238576,6 11,6.22385763 11,6.5 L11,11.5 C11,11.7761424 11.2238576,12 11.5,12 C11.7761424,12 12,11.7761424 12,11.5 L12,6.5 C12,6.22385763 11.7761424,6 11.5,6 Z"}));ta.displayName="PauseSmall";var aa=ta;const ia=({size:e,...t})=>r.createElement("svg",{viewBox:"0 0 18 18",fill:"currentColor",width:e||"18",height:e||"18",...t},r.createElement("path",{d:"M6.87468837,5.45041947 L12.7318793,8.46657119 C13.20163,8.68731241 13.20163,9.26940918 12.7318793,9.53342881 L6.87468837,12.5495805 C6.58008377,12.7012867 6.00070071,12.5801226 6,12.0161517 L6,5.98384828 C6,5.65247743 6.35266876,5.20682168 6.87468837,5.45041947 Z M7,11.3602529 L11.5834735,9 L7,6.63974714 L7,11.3602529 Z"}));ia.displayName="PlaySmall";var na=ia;const ra=e=>{let{isPlaying:t,onToggle:a,translations:i,className:n}=e;return o().createElement("button",{className:n,onKeyDown:e=>{"Space"===e.code&&(a(),e.preventDefault())},"aria-label":i.ariaLabel,"aria-pressed":t},t?o().createElement(aa,null):o().createElement(na,null))},oa="heading-tag";function ca(e){var t;const a=e.querySelector(".preview-svg"),i=e.querySelector(".display-svg"),n=i.querySelectorAll("text"),r=parseFloat((null==a?void 0:a.dataset.fontsize)||"0"),{width:o=0,height:c=0}=null!=(t=null==a||null==a.getBBox?void 0:a.getBBox())?t:{};if(!o||!c)return{fontSize:0,viewBox:"0 0 0 0"};const s=e.offsetWidth,l="0 0 "+s+" "+s/(o/c),d=s/o*r;return i.setAttributeNS(null,"viewBox",l),n.forEach((e=>{e.style.fontSize=d+"px"})),e.dataset.maskState="true",{fontSize:d,viewBox:l}}const sa=e=>{const{id:t,fillLayers:i,isDecorative:n,headingLevel:r,size:c,lastViewBox:l,lastProps:d,videoRef:u,tagName:h}=e,g="clip-"+t,m="text-group-"+t,p=()=>o().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:l,className:s("display-svg",Zt,Wt)},o().createElement("defs",null,o().createElement("clipPath",{id:g,"aria-hidden":"true"},o().createElement(ea,d))),o().createElement("g",{className:"text-group",id:m},o().createElement(ea,a()({},d,{extraStyle:{overflow:"visible"}})))),f=()=>o().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",className:s("preview-svg",Zt,qt),"data-fontsize":c,"aria-hidden":"true"},o().createElement(ea,e)),_=h||(r?"h"+r:"p");return o().createElement(o().Fragment,null,!n&&o().createElement(_,{className:Xt,"data-testId":oa},d.text),o().createElement("div",{className:Vt,"aria-hidden":!0},o().createElement(f,null),o().createElement(p,null),o().createElement("div",{className:s(Jt,"fill-layers-wrapper"),style:{clipPath:"url(#"+g+")"}},o().createElement(xt,a()({},i,{videoRef:u})))))};const la={TextMask:{component:e=>{const{id:t,link:i,className:n,viewBox:c,onClick:l,onMouseEnter:d,onMouseLeave:u,translations:h}=e,g=o().useRef(null),m=o().useRef(null),[p,f]=(0,r.useState)("string"==typeof c?c:"0 0 0 0"),[_,T]=(0,r.useState)({...e}),[E,I]=(0,r.useState)(!1);(0,r.useEffect)((()=>{const e=m.current;if(!e)return;const t=new AbortController,{signal:a}=t;return e.addEventListener("play",(()=>I(!0)),{signal:a}),e.addEventListener("pause",(()=>I(!1)),{signal:a}),()=>t.abort()}),[m]);const L=()=>{m.current&&(m.current.paused?m.current.play():m.current.pause())};o().useEffect((()=>{const t=g.current;if(t){var a;const i=()=>{const a=ca(t);f(a.viewBox),T({...e,size:a.fontSize})};null==(a=document.fonts)||a.addEventListener("loadingdone",i,{once:!0})}}),[e]),o().useEffect((()=>{const e=Ft((e=>{for(const t of e)ca(t)}),60,{leading:!0,trailing:!0}),t="undefined"!=typeof ResizeObserver&&new ResizeObserver((t=>e(t.map((e=>e.target))))),a=g.current;return a&&t&&t.observe(a),()=>{a&&t&&t.unobserve(a)}}),[]),o().useEffect((()=>{const t=null==g?void 0:g.current;if(t){const a=ca(t);f(a.viewBox),T({...e,size:a.fontSize})}}),[e]);const b={...e,lastViewBox:p,lastProps:_,videoRef:m};return o().createElement("div",a()({id:t},Pt(e),((e={})=>{const t=e.tabIndex??e.tabindex??void 0;return void 0!==t?{tabIndex:Number(t)}:{}})(e.a11y),{className:s(Qt,n),ref:g,onClick:e=>{null==l||l(e),L()},onMouseEnter:d,onMouseLeave:u}),i&&Object.keys(i).length?o().createElement(jt,i,o().createElement(sa,b)):o().createElement(sa,b),m.current&&o().createElement(ra,{isPlaying:E,onToggle:L,translations:h,className:s(Kt,Xt)}))}}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[TextMask].3350c100.bundle.min.js.map