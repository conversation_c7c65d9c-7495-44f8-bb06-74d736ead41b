
<!-- saved from url=(0090)https://www-14kexchange-com.filesusr.com/html/263ca3_7b2a88b3f1bf1d53f3bb772f8af33e16.html -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<script type="text/javascript" src="./bullionvaultchart.js.download"></script>
		<script type="text/javascript">
			var options = {
				bullion: 'gold',
				currency: 'USD',
				timeframe: '1y',
				chartType: 'line',
				miniChartModeAxis: 'oz',
				referrerID: 'MYUSERNAME',
				containerDefinedSize: true,
				miniChartMode: true,
				displayLatestPriceLine: true,
				switchBullion: true,
				switchCurrency: true,
				switchTimeframe: true,
				switchChartType: false,
				exportButton: true
			};
			var chartBV = new BullionVaultChart(options, 'embed');
		</script><link rel="stylesheet" type="text/css" href="./bvchartstyle.css">
	<link rel="stylesheet" href="chrome-extension://ihcjicgdanjaechkgeegckofjjedodee/app/content-style.css"></head>
	<body>
	<div id="embed" style="height: 210px; width: 400px; min-height: 350px;"><div id="jschart_wrapper" class="bvchart" style="min-width: 200px;"><div id="jschart_title" class="bvchart__title"><b>Gold 1 year</b> GMT-4</div><div id="jschart_container" class="bvchart__container" data-highcharts-chart="0" style="overflow: hidden; height: 335px; width: 400px;"><div id="highcharts-e3zi8zm-0" style="position: relative; overflow: hidden; width: 396px; height: 335px; text-align: left; line-height: normal; z-index: 0; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); user-select: none; touch-action: manipulation; outline: none; font-size: 14px;" dir="ltr" class="highcharts-container "><svg version="1.1" class="highcharts-root" style="font-family: Helvetica, Arial, sans-serif; font-size: 14px;" xmlns="http://www.w3.org/2000/svg" width="396" height="335" viewBox="0 0 396 335" role="img" aria-label=""><desc>Created with Highcharts 11.4.1</desc><defs><filter id="highcharts-drop-shadow-0"><fedropshadow dx="1" dy="1" flood-color="#000000" flood-opacity="0.75" stdDeviation="2.5"></fedropshadow></filter><clippath id="highcharts-e3zi8zm-181-"><rect x="0" y="0" width="352" height="305" fill="none"></rect></clippath></defs><rect fill="#ffffff" class="highcharts-background" filter="none" x="0" y="0" width="396" height="335" rx="0" ry="0"></rect><rect fill="none" class="highcharts-plot-background" x="0" y="14" width="352" height="305" filter="none"></rect><image preserveAspectRatio="none" x="53" y="148" width="250" height="38" href="https://www.bullionvault.com/chart/bullionvault2.png"></image><g class="highcharts-grid highcharts-xaxis-grid" data-z-index="1"><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 45.5 14 L 45.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 105.5 14 L 105.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 165.5 14 L 165.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 223.5 14 L 223.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 283.5 14 L 283.5 319" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 343.5 14 L 343.5 319" opacity="1"></path></g><g class="highcharts-grid highcharts-yaxis-grid" data-z-index="1"></g><g class="highcharts-grid highcharts-yaxis-grid" data-z-index="1"><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 0 296.5 L 352 296.5" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 0 192.5 L 352 192.5" opacity="1"></path><path fill="none" stroke="#e9e9e9" stroke-width="1" stroke-dasharray="none" data-z-index="1" class="highcharts-grid-line" d="M 0 89.5 L 352 89.5" opacity="1"></path></g><rect fill="none" class="highcharts-plot-border" data-z-index="1" stroke="#cccccc" stroke-width="0" x="0" y="14" width="352" height="305"></rect><g class="highcharts-axis highcharts-xaxis" data-z-index="2"><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 46 319 L 46 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 106 319 L 106 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 166 319 L 166 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 224 319 L 224 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 284 319 L 284 324" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 344 319 L 344 324" opacity="1"></path><path fill="none" class="highcharts-axis-line" stroke="#8c8c8c" stroke-width="1" data-z-index="7" d="M 0 319.5 L 352 319.5"></path></g><g class="highcharts-axis highcharts-yaxis" data-z-index="2"><text x="0" data-z-index="7" text-anchor="end" transform="translate(0,0)" class="highcharts-axis-title" style="color: rgb(0, 0, 0); font-size: 0.8em; font-weight: bold; text-anchor: start; fill: rgb(0, 0, 0);" visibility="hidden">USD/kg <a href="https://www.bullionvault.com/price-alerts.do" target="_self" style="text-decoration: underline; font-weight: normal;"><tspan>Set<tspan dy="14" x="0">​</tspan>price alert</tspan></a></text><path fill="none" class="highcharts-axis-line" stroke="#8c8c8c" stroke-width="1" data-z-index="7" d="M -0.5 14 L -0.5 319" visibility="hidden"></path></g><g class="highcharts-axis highcharts-yaxis" data-z-index="2"><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 353 296 L 358 296" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 353 192 L 358 192" opacity="1"></path><path fill="none" class="highcharts-tick" stroke="#000000" stroke-width="2" d="M 353 89 L 358 89" opacity="1"></path><text x="388" data-z-index="7" text-anchor="end" transform="translate(0,0)" class="highcharts-axis-title" style="color: rgb(0, 0, 0); font-size: 11px; font-weight: bold; fill: rgb(0, 0, 0);" y="9">USD/oz</text><path fill="none" class="highcharts-axis-line" stroke="#8c8c8c" stroke-width="1" data-z-index="7" d="M 353.5 14 L 353.5 319"></path></g><g class="highcharts-series-group" data-z-index="3" filter="none"><g class="highcharts-series highcharts-series-0 highcharts-area-series" data-z-index="0.1" opacity="1" transform="translate(0,14) scale(1 1)" clip-path="url(#highcharts-e3zi8zm-181-)"><path fill="rgba(253,240,209,0.7)" d="M 0 265.905486756712 L 1.9664804469274 281.486546522356 L 3.9329608938547 278.79215263274 L 5.8994413407821 279.554811160848 L 7.8659217877095 291.136363636364 L 9.8324022346369 285.174675856031 L 11.798882681564 286.659077236643 L 13.765363128492 269.919250099166 L 15.731843575419 270.464362879632 L 17.698324022346 271.199717443115 L 19.664804469274 285.902298323337 L 21.631284916201 275.377304573316 L 23.597765363128 273.679867834498 L 25.564245810056 262.181113222209 L 27.530726256983 268.958537149197 L 29.497206703911 253.763276765709 L 31.463687150838 254.281246666887 L 33.430167597765 252.481520739064 L 35.396648044693 259.089241263504 L 37.36312849162 252.693186871904 L 39.329608938547 251.82179574130998 L 41.296089385475 253.773666769946 L 43.262569832402 255.09110319874 L 45.22905027933 255.081357535851 L 47.195530726257 257.60097383480297 L 49.162011173184 251.36544098154098 L 51.128491620112 256.54377076796 L 53.094972067039 254.151734055925 L 55.061452513966 252.759231860081 L 57.027932960894 235.548713367841 L 58.994413407821 235.40309222317802 L 60.960893854749 236.702326008889 L 62.927374301676 233.271208330425 L 64.893854748603 224.244227671994 L 66.860335195531 223.175426460886 L 68.826815642458 215.248900282288 L 70.793296089385 214.708378433927 L 72.759776536313 213.497097242227 L 74.72625698324 214.347788907014 L 76.692737430168 215.545458387736 L 78.659217877095 216.17272469008 L 80.625698324022 218.82829701347498 L 82.59217877095 227.836511230143 L 84.558659217877 215.250188964984 L 86.525139664804 217.467448086355 L 88.491620111732 214.316055095622 L 90.458100558659 205.71627275096 L 92.424581005587 198.57705115702998 L 94.391061452514 198.51938260638002 L 96.357541899441 198.56472812875 L 98.324022346369 191.79833854735 L 100.2905027933 194.74716672678 L 102.25698324022 184.6901258808 L 104.22346368715 191.96345101779002 L 106.18994413408 194.58334293904 L 108.15642458101 194.74330067869 L 110.12290502793 213.422031475177 L 112.08938547486 208.037754085382 L 114.05586592179 207.83712229812699 L 116.02234636872 230.25963741222301 L 117.98882681564 238.55255218986701 L 119.95530726257 239.449153175704 L 121.9217877095 226.734043183599 L 123.88826815642 216.492559626728 L 125.85474860335 199.87241889466 L 127.82122905028 198.96671658728002 L 129.78770949721 221.625543890776 L 131.75418994413 219.93076506002 L 133.72067039106 216.931436627431 L 135.68715083799 219.045762218448 L 137.65363128492 216.920482824514 L 139.62011173184 221.29217778582 L 141.58659217877 219.614151830113 L 143.5530726257 205.133627086969 L 145.51955307263 209.099467541661 L 147.48603351955 217.503047945836 L 149.45251396648 216.575357489955 L 151.41899441341 233.09192035032498 L 153.38547486034 223.939293129021 L 155.35195530726 223.72440528944 L 157.31843575419 225.686585779645 L 159.28491620112 221.469532741875 L 161.25139664804 224.310272660171 L 163.21787709497 228.24115759673 L 165.1843575419 223.859716972535 L 167.15083798883 219.55205343269301 L 169.11731843575 219.34683071333302 L 171.08379888268 217.10041514596202 L 173.05027932961 211.839206953647 L 175.01675977654 206.659507941864 L 176.98324022346 212.36676143237798 L 178.94972067039 205.404250453158 L 180.91620111732 203.38705932035998 L 182.88268156425 205.023605801787 L 184.84916201117 192.59071732014002 L 186.8156424581 189.90929080015002 L 188.78212290503 185.75900763457 L 190.74860335196 193.29699598029 L 192.71508379888 188.48441045157 L 194.68156424581 178.67664916461 L 196.64804469274 179.85684088627 L 198.61452513966 167.46148528814 L 200.58100558659 163.16614477658 L 202.54748603352 162.42748796369 L 204.51396648045 147.67706473837 L 206.48044692737 150.73784722704 L 208.4469273743 156.7344905255 L 210.41340782123 157.63753492482 L 212.37988826816 143.48135550776 L 214.34636871508 141.5269876564 L 216.31284916201 142.86898959909 L 218.27932960894 139.33364970758 L 220.24581005587 147.28474140007 L 222.21229050279 163.2937243635 L 224.17877094972 159.76353920277 L 226.14525139665 148.47588692464 L 228.11173184358 149.61331048932 L 230.0782122905 149.94804581964 L 232.04469273743 156.27805522306 L 234.01117318436 142.41972259415 L 235.97765363128 130.2988564103 L 237.94413407821 128.97473494002 L 239.91061452514 118.11258957961999 L 241.87709497207 114.39667302531001 L 243.84357541899 120.28587240399 L 245.81005586592 123.83482400648 L 247.77653631285 120.82381688696 L 249.74301675978 104.39730072998 L 251.7094972067 103.32004253868001 L 253.67597765363 96.56621761356001 L 255.64245810056 97.1259891597 L 257.60893854749 116.46766666067 L 259.57541899441 131.26029424429 L 261.54189944134 103.9422346529 L 263.50837988827 64.88991368652 L 265.4748603352 68.39408356531999 L 267.44134078212 61.25486197139 L 269.40782122905 41.62443900380998 L 271.37430167598 41.62443900380998 L 273.34078212291 13.863636363640012 L 275.30726256983 44.487811411960024 L 277.27374301676 43.729018931940004 L 279.24022346369 47.98875958401999 L 281.20670391061 44.31102025458 L 283.17318435754 64.84706498687001 L 285.13966480447 63.97833176434 L 287.1061452514 39.819638887869985 L 289.07262569832 29.045285036150005 L 291.03910614525 42.25234964750001 L 293.00558659218 52.01089936400001 L 294.97206703911 60.56598052762999 L 296.93854748603 65.06597995986999 L 298.90502793296 73.93179473862 L 300.87150837989 68.79091729306 L 302.83798882682 43.50051938138 L 304.80446927374 33.69791282520998 L 306.77094972067 37.63266380985999 L 308.7374301676 46.42147979751002 L 310.70391061453 44.73443306293001 L 312.67039106145 51.32475637096999 L 314.63687150838 26.093235149969985 L 316.60335195531 28.48333883795999 L 318.56983240223 46.04792290096998 L 320.53631284916 44.03274533488002 L 322.50279329609 41.52021679076 L 324.46927374302 23.950477996969994 L 326.43575418994 14.408668601430008 L 328.40223463687 22.142939430299975 L 330.3687150838 29.30745142213999 L 332.33519553073 30.959059382579994 L 334.30167597765 29.81495077641 L 336.26815642458 42.46087461627002 L 338.23463687151 44.076721631889995 L 340.20111731844 55.398040745450004 L 342.16759776536 45.48211065469002 L 344.13407821229 35.95600707952002 L 346.10055865922 39.06728982133001 L 348.06703910615 41.27415893847001 L 350.03351955307 48.132447704640015 L 352 43.440998349350025 L 352 43.440998349350025 L 352 305 L 352 305 L 350.03351955307 305 L 348.06703910615 305 L 346.10055865922 305 L 344.13407821229 305 L 342.16759776536 305 L 340.20111731844 305 L 338.23463687151 305 L 336.26815642458 305 L 334.30167597765 305 L 332.33519553073 305 L 330.3687150838 305 L 328.40223463687 305 L 326.43575418994 305 L 324.46927374302 305 L 322.50279329609 305 L 320.53631284916 305 L 318.56983240223 305 L 316.60335195531 305 L 314.63687150838 305 L 312.67039106145 305 L 310.70391061453 305 L 308.7374301676 305 L 306.77094972067 305 L 304.80446927374 305 L 302.83798882682 305 L 300.87150837989 305 L 298.90502793296 305 L 296.93854748603 305 L 294.97206703911 305 L 293.00558659218 305 L 291.03910614525 305 L 289.07262569832 305 L 287.1061452514 305 L 285.13966480447 305 L 283.17318435754 305 L 281.20670391061 305 L 279.24022346369 305 L 277.27374301676 305 L 275.30726256983 305 L 273.34078212291 305 L 271.37430167598 305 L 269.40782122905 305 L 267.44134078212 305 L 265.4748603352 305 L 263.50837988827 305 L 261.54189944134 305 L 259.57541899441 305 L 257.60893854749 305 L 255.64245810056 305 L 253.67597765363 305 L 251.7094972067 305 L 249.74301675978 305 L 247.77653631285 305 L 245.81005586592 305 L 243.84357541899 305 L 241.87709497207 305 L 239.91061452514 305 L 237.94413407821 305 L 235.97765363128 305 L 234.01117318436 305 L 232.04469273743 305 L 230.0782122905 305 L 228.11173184358 305 L 226.14525139665 305 L 224.17877094972 305 L 222.21229050279 305 L 220.24581005587 305 L 218.27932960894 305 L 216.31284916201 305 L 214.34636871508 305 L 212.37988826816 305 L 210.41340782123 305 L 208.4469273743 305 L 206.48044692737 305 L 204.51396648045 305 L 202.54748603352 305 L 200.58100558659 305 L 198.61452513966 305 L 196.64804469274 305 L 194.68156424581 305 L 192.71508379888 305 L 190.74860335196 305 L 188.78212290503 305 L 186.8156424581 305 L 184.84916201117 305 L 182.88268156425 305 L 180.91620111732 305 L 178.94972067039 305 L 176.98324022346 305 L 175.01675977654 305 L 173.05027932961 305 L 171.08379888268 305 L 169.11731843575 305 L 167.15083798883 305 L 165.1843575419 305 L 163.21787709497 305 L 161.25139664804 305 L 159.28491620112 305 L 157.31843575419 305 L 155.35195530726 305 L 153.38547486034 305 L 151.41899441341 305 L 149.45251396648 305 L 147.48603351955 305 L 145.51955307263 305 L 143.5530726257 305 L 141.58659217877 305 L 139.62011173184 305 L 137.65363128492 305 L 135.68715083799 305 L 133.72067039106 305 L 131.75418994413 305 L 129.78770949721 305 L 127.82122905028 305 L 125.85474860335 305 L 123.88826815642 305 L 121.9217877095 305 L 119.95530726257 305 L 117.98882681564 305 L 116.02234636872 305 L 114.05586592179 305 L 112.08938547486 305 L 110.12290502793 305 L 108.15642458101 305 L 106.18994413408 305 L 104.22346368715 305 L 102.25698324022 305 L 100.2905027933 305 L 98.324022346369 305 L 96.357541899441 305 L 94.391061452514 305 L 92.424581005587 305 L 90.458100558659 305 L 88.491620111732 305 L 86.525139664804 305 L 84.558659217877 305 L 82.59217877095 305 L 80.625698324022 305 L 78.659217877095 305 L 76.692737430168 305 L 74.72625698324 305 L 72.759776536313 305 L 70.793296089385 305 L 68.826815642458 305 L 66.860335195531 305 L 64.893854748603 305 L 62.927374301676 305 L 60.960893854749 305 L 58.994413407821 305 L 57.027932960894 305 L 55.061452513966 305 L 53.094972067039 305 L 51.128491620112 305 L 49.162011173184 305 L 47.195530726257 305 L 45.22905027933 305 L 43.262569832402 305 L 41.296089385475 305 L 39.329608938547 305 L 37.36312849162 305 L 35.396648044693 305 L 33.430167597765 305 L 31.463687150838 305 L 29.497206703911 305 L 27.530726256983 305 L 25.564245810056 305 L 23.597765363128 305 L 21.631284916201 305 L 19.664804469274 305 L 17.698324022346 305 L 15.731843575419 305 L 13.765363128492 305 L 11.798882681564 305 L 9.8324022346369 305 L 7.8659217877095 305 L 5.8994413407821 305 L 3.9329608938547 305 L 1.9664804469274 305 L 0 305 Z" class="highcharts-area" data-z-index="0" fill-opacity="1" style="pointer-events: none;"></path><path fill="none" d="M 0 265.905486756712 L 1.9664804469274 281.486546522356 L 3.9329608938547 278.79215263274 L 5.8994413407821 279.554811160848 L 7.8659217877095 291.136363636364 L 9.8324022346369 285.174675856031 L 11.798882681564 286.659077236643 L 13.765363128492 269.919250099166 L 15.731843575419 270.464362879632 L 17.698324022346 271.199717443115 L 19.664804469274 285.902298323337 L 21.631284916201 275.377304573316 L 23.597765363128 273.679867834498 L 25.564245810056 262.181113222209 L 27.530726256983 268.958537149197 L 29.497206703911 253.763276765709 L 31.463687150838 254.281246666887 L 33.430167597765 252.481520739064 L 35.396648044693 259.089241263504 L 37.36312849162 252.693186871904 L 39.329608938547 251.82179574130998 L 41.296089385475 253.773666769946 L 43.262569832402 255.09110319874 L 45.22905027933 255.081357535851 L 47.195530726257 257.60097383480297 L 49.162011173184 251.36544098154098 L 51.128491620112 256.54377076796 L 53.094972067039 254.151734055925 L 55.061452513966 252.759231860081 L 57.027932960894 235.548713367841 L 58.994413407821 235.40309222317802 L 60.960893854749 236.702326008889 L 62.927374301676 233.271208330425 L 64.893854748603 224.244227671994 L 66.860335195531 223.175426460886 L 68.826815642458 215.248900282288 L 70.793296089385 214.708378433927 L 72.759776536313 213.497097242227 L 74.72625698324 214.347788907014 L 76.692737430168 215.545458387736 L 78.659217877095 216.17272469008 L 80.625698324022 218.82829701347498 L 82.59217877095 227.836511230143 L 84.558659217877 215.250188964984 L 86.525139664804 217.467448086355 L 88.491620111732 214.316055095622 L 90.458100558659 205.71627275096 L 92.424581005587 198.57705115702998 L 94.391061452514 198.51938260638002 L 96.357541899441 198.56472812875 L 98.324022346369 191.79833854735 L 100.2905027933 194.74716672678 L 102.25698324022 184.6901258808 L 104.22346368715 191.96345101779002 L 106.18994413408 194.58334293904 L 108.15642458101 194.74330067869 L 110.12290502793 213.422031475177 L 112.08938547486 208.037754085382 L 114.05586592179 207.83712229812699 L 116.02234636872 230.25963741222301 L 117.98882681564 238.55255218986701 L 119.95530726257 239.449153175704 L 121.9217877095 226.734043183599 L 123.88826815642 216.492559626728 L 125.85474860335 199.87241889466 L 127.82122905028 198.96671658728002 L 129.78770949721 221.625543890776 L 131.75418994413 219.93076506002 L 133.72067039106 216.931436627431 L 135.68715083799 219.045762218448 L 137.65363128492 216.920482824514 L 139.62011173184 221.29217778582 L 141.58659217877 219.614151830113 L 143.5530726257 205.133627086969 L 145.51955307263 209.099467541661 L 147.48603351955 217.503047945836 L 149.45251396648 216.575357489955 L 151.41899441341 233.09192035032498 L 153.38547486034 223.939293129021 L 155.35195530726 223.72440528944 L 157.31843575419 225.686585779645 L 159.28491620112 221.469532741875 L 161.25139664804 224.310272660171 L 163.21787709497 228.24115759673 L 165.1843575419 223.859716972535 L 167.15083798883 219.55205343269301 L 169.11731843575 219.34683071333302 L 171.08379888268 217.10041514596202 L 173.05027932961 211.839206953647 L 175.01675977654 206.659507941864 L 176.98324022346 212.36676143237798 L 178.94972067039 205.404250453158 L 180.91620111732 203.38705932035998 L 182.88268156425 205.023605801787 L 184.84916201117 192.59071732014002 L 186.8156424581 189.90929080015002 L 188.78212290503 185.75900763457 L 190.74860335196 193.29699598029 L 192.71508379888 188.48441045157 L 194.68156424581 178.67664916461 L 196.64804469274 179.85684088627 L 198.61452513966 167.46148528814 L 200.58100558659 163.16614477658 L 202.54748603352 162.42748796369 L 204.51396648045 147.67706473837 L 206.48044692737 150.73784722704 L 208.4469273743 156.7344905255 L 210.41340782123 157.63753492482 L 212.37988826816 143.48135550776 L 214.34636871508 141.5269876564 L 216.31284916201 142.86898959909 L 218.27932960894 139.33364970758 L 220.24581005587 147.28474140007 L 222.21229050279 163.2937243635 L 224.17877094972 159.76353920277 L 226.14525139665 148.47588692464 L 228.11173184358 149.61331048932 L 230.0782122905 149.94804581964 L 232.04469273743 156.27805522306 L 234.01117318436 142.41972259415 L 235.97765363128 130.2988564103 L 237.94413407821 128.97473494002 L 239.91061452514 118.11258957961999 L 241.87709497207 114.39667302531001 L 243.84357541899 120.28587240399 L 245.81005586592 123.83482400648 L 247.77653631285 120.82381688696 L 249.74301675978 104.39730072998 L 251.7094972067 103.32004253868001 L 253.67597765363 96.56621761356001 L 255.64245810056 97.1259891597 L 257.60893854749 116.46766666067 L 259.57541899441 131.26029424429 L 261.54189944134 103.9422346529 L 263.50837988827 64.88991368652 L 265.4748603352 68.39408356531999 L 267.44134078212 61.25486197139 L 269.40782122905 41.62443900380998 L 271.37430167598 41.62443900380998 L 273.34078212291 13.863636363640012 L 275.30726256983 44.487811411960024 L 277.27374301676 43.729018931940004 L 279.24022346369 47.98875958401999 L 281.20670391061 44.31102025458 L 283.17318435754 64.84706498687001 L 285.13966480447 63.97833176434 L 287.1061452514 39.819638887869985 L 289.07262569832 29.045285036150005 L 291.03910614525 42.25234964750001 L 293.00558659218 52.01089936400001 L 294.97206703911 60.56598052762999 L 296.93854748603 65.06597995986999 L 298.90502793296 73.93179473862 L 300.87150837989 68.79091729306 L 302.83798882682 43.50051938138 L 304.80446927374 33.69791282520998 L 306.77094972067 37.63266380985999 L 308.7374301676 46.42147979751002 L 310.70391061453 44.73443306293001 L 312.67039106145 51.32475637096999 L 314.63687150838 26.093235149969985 L 316.60335195531 28.48333883795999 L 318.56983240223 46.04792290096998 L 320.53631284916 44.03274533488002 L 322.50279329609 41.52021679076 L 324.46927374302 23.950477996969994 L 326.43575418994 14.408668601430008 L 328.40223463687 22.142939430299975 L 330.3687150838 29.30745142213999 L 332.33519553073 30.959059382579994 L 334.30167597765 29.81495077641 L 336.26815642458 42.46087461627002 L 338.23463687151 44.076721631889995 L 340.20111731844 55.398040745450004 L 342.16759776536 45.48211065469002 L 344.13407821229 35.95600707952002 L 346.10055865922 39.06728982133001 L 348.06703910615 41.27415893847001 L 350.03351955307 48.132447704640015 L 352 43.440998349350025 L 352 43.440998349350025" class="highcharts-graph" data-z-index="1" stroke="#c7aa50" stroke-width="1" stroke-linejoin="round" stroke-linecap="round" filter="none"></path></g><g class="highcharts-markers highcharts-series-0 highcharts-area-series" data-z-index="0.1" opacity="1" transform="translate(0,14) scale(1 1)" clip-path="none"></g></g><text x="198" text-anchor="middle" class="highcharts-title" data-z-index="4" style="font-size: 1em; color: rgb(51, 51, 51); font-weight: bold; fill: rgb(51, 51, 51);" y="21"></text><text x="198" text-anchor="middle" class="highcharts-subtitle" data-z-index="4" style="color: rgb(102, 102, 102); font-size: 0.8em; fill: rgb(102, 102, 102);" y="23"></text><text x="10" text-anchor="start" class="highcharts-caption" data-z-index="4" style="color: rgb(102, 102, 102); font-size: 0.8em; fill: rgb(102, 102, 102);" y="331"></text><g class="highcharts-plot-lines-5" data-z-index="5"><path fill="none" class="highcharts-plot-line " stroke="#0967e2" stroke-width="1" d="M 0 57.5 L 352 57.5"></path></g><text x="342" text-anchor="end" class="highcharts-plot-line-label " data-z-index="5" transform="translate(0,0)" y="54" style="font-size: 0.8em; font-weight: bold; color: rgb(9, 103, 226); fill: rgb(9, 103, 226);">3,320.22</text><g class="highcharts-axis-labels highcharts-xaxis-labels" data-z-index="7"><text x="48.96888873772167" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 48.96888873772167 333)" y="333" opacity="1"><title>September'24</title>September'…</text><text x="108.94654236901067" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 108.94654236901067 333)" y="333" opacity="1">November'24</text><text x="168.96516434293068" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 168.96516434293068 333)" y="333" opacity="1">January'25</text><text x="226.9763375272907" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 226.9763375272907 333)" y="333" opacity="1">March'25</text><text x="286.9130098320107" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 286.9130098320107 333)" y="333" opacity="1">May'25</text><text x="346.89067644722064" style="color: rgb(0, 0, 0); cursor: default; font-size: 11px; fill: rgb(0, 0, 0);" text-anchor="end" transform="translate(0,0) rotate(-45 346.89067644722064 333)" y="333" opacity="1">July'25</text></g><g class="highcharts-axis-labels highcharts-yaxis-labels" data-z-index="7"></g><g class="highcharts-axis-labels highcharts-yaxis-labels" data-z-index="7"><text x="360" style="color: rgb(0, 0, 0); cursor: default; font-size: 0.8em; fill: rgb(0, 0, 0);" text-anchor="start" transform="translate(0,0)" y="299" opacity="1">2,400</text><text x="360" style="color: rgb(0, 0, 0); cursor: default; font-size: 0.8em; fill: rgb(0, 0, 0);" text-anchor="start" transform="translate(0,0)" y="195" opacity="1">2,800</text><text x="360" style="color: rgb(0, 0, 0); cursor: default; font-size: 0.8em; fill: rgb(0, 0, 0);" text-anchor="start" transform="translate(0,0)" y="92" opacity="1">3,200</text></g></svg></div></div><a id="jschart_timestamp" class="bvchart__timestamp" href="https://www.bullionvaultaffiliate.com/MYUSERNAME/en/help/custom_gold_price_charts.html" target="_blank" style="font-size: 9px; padding: 2px; right: 0px; display: block;">Embed</a></div></div>
	
	</body></html>