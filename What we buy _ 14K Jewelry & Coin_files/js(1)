
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__cid"}],
  "tags":[{"function":"__ccd_ads_first","priority":100,"vtp_instanceDestinationId":["macro",1],"tag_id":2},{"function":"__rep","once_per_event":true,"vtp_containerId":["macro",1],"tag_id":1},{"function":"__ccd_ads_last","vtp_instanceDestinationId":["macro",1],"tag_id":3}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"}],
  "rules":[[["if",0],["add",1,0,2]]]
},
"runtime":[ [50,"__ccd_ads_first",[46,"a"],[50,"e",[46,"f"],[2,[15,"c"],"B",[7,[15,"f"]]],[2,[15,"d"],"A",[7,[15,"f"]]]],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_webPrivacyTasks"]],[52,"d",[15,"__module_taskConversionAutoDataAnalysis"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"f"],["e",[15,"f"]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ads_last",[46,"a"],[52,"b",["require","internal.registerCcdCallback"]],[52,"c",[15,"__module_metadataSchema"]],[52,"d",[15,"__module_gtagSchema"]],[52,"e",[15,"__module_adwordsHitType"]],["b",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"f"],[52,"g",[2,[15,"f"],"getMetadata",[7,[17,[15,"c"],"K"]]]],[22,[1,[20,[15,"g"],[17,[15,"e"],"B"]],[28,[2,[15,"f"],"getHitData",[7,[17,[15,"d"],"DG"]]]]],[46,[53,[2,[15,"f"],"abort",[7]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__cid",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"containerId"]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",34],[52,"f",40],[52,"g",42],[52,"h",43],[52,"i",44],[52,"j",45],[52,"k",46],[52,"l",47],[52,"m",56],[52,"n",113],[52,"o",129],[52,"p",142],[52,"q",156],[52,"r",168],[52,"s",174],[52,"t",178],[52,"u",188],[52,"v",212],[36,[8,"DT",[15,"r"],"X",[15,"b"],"Z",[15,"c"],"AA",[15,"d"],"AB",[15,"e"],"AH",[15,"f"],"AJ",[15,"g"],"AK",[15,"h"],"AL",[15,"i"],"AM",[15,"j"],"AN",[15,"k"],"AO",[15,"l"],"EE",[15,"u"],"AT",[15,"m"],"DX",[15,"s"],"EA",[15,"t"],"CB",[15,"n"],"CO",[15,"o"],"DB",[15,"p"],"EZ",[15,"v"],"DL",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_adwordsHitType",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","conversion"],[52,"c","ga_conversion"],[52,"d","page_view"],[52,"e","remarketing"],[52,"f","user_data_lead"],[52,"g","user_data_web"],[36,[8,"B",[15,"b"],"D",[15,"c"],"F",[15,"d"],"G",[15,"e"],"H",[15,"f"],"I",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_direct_google_requests"],[52,"v","allow_google_signals"],[52,"w","auid"],[52,"x","discount"],[52,"y","aw_feed_country"],[52,"z","aw_feed_language"],[52,"aA","items"],[52,"aB","aw_merchant_id"],[52,"aC","aw_basket_type"],[52,"aD","client_id"],[52,"aE","conversion_id"],[52,"aF","conversion_linker"],[52,"aG","conversion_api"],[52,"aH","cookie_deprecation"],[52,"aI","cookie_expires"],[52,"aJ","cookie_update"],[52,"aK","country"],[52,"aL","currency"],[52,"aM","customer_buyer_stage"],[52,"aN","customer_lifetime_value"],[52,"aO","customer_loyalty"],[52,"aP","customer_ltv_bucket"],[52,"aQ","debug_mode"],[52,"aR","shipping"],[52,"aS","engagement_time_msec"],[52,"aT","estimated_delivery_date"],[52,"aU","event_developer_id_string"],[52,"aV","event"],[52,"aW","event_timeout"],[52,"aX","first_party_collection"],[52,"aY","gdpr_applies"],[52,"aZ","google_analysis_params"],[52,"bA","_google_ng"],[52,"bB","gpp_sid"],[52,"bC","gpp_string"],[52,"bD","gsa_experiment_id"],[52,"bE","gtag_event_feature_usage"],[52,"bF","iframe_state"],[52,"bG","ignore_referrer"],[52,"bH","is_passthrough"],[52,"bI","_lps"],[52,"bJ","language"],[52,"bK","merchant_feed_label"],[52,"bL","merchant_feed_language"],[52,"bM","merchant_id"],[52,"bN","new_customer"],[52,"bO","page_hostname"],[52,"bP","page_path"],[52,"bQ","page_referrer"],[52,"bR","page_title"],[52,"bS","_platinum_request_status"],[52,"bT","restricted_data_processing"],[52,"bU","screen_resolution"],[52,"bV","send_page_view"],[52,"bW","server_container_url"],[52,"bX","session_duration"],[52,"bY","session_engaged_time"],[52,"bZ","session_id"],[52,"cA","_shared_user_id"],[52,"cB","topmost_url"],[52,"cC","transaction_id"],[52,"cD","transport_url"],[52,"cE","update"],[52,"cF","_user_agent_architecture"],[52,"cG","_user_agent_bitness"],[52,"cH","_user_agent_full_version_list"],[52,"cI","_user_agent_mobile"],[52,"cJ","_user_agent_model"],[52,"cK","_user_agent_platform"],[52,"cL","_user_agent_platform_version"],[52,"cM","_user_agent_wow64"],[52,"cN","user_data_auto_latency"],[52,"cO","user_data_auto_meta"],[52,"cP","user_data_auto_multi"],[52,"cQ","user_data_auto_selectors"],[52,"cR","user_data_auto_status"],[52,"cS","user_data_mode"],[52,"cT","user_id"],[52,"cU","user_properties"],[52,"cV","us_privacy_string"],[52,"cW","value"],[52,"cX","_fpm_parameters"],[52,"cY","_host_name"],[52,"cZ","_in_page_command"],[52,"dA","non_personalized_ads"],[52,"dB","conversion_label"],[52,"dC","page_location"],[52,"dD","global_developer_id_string"],[52,"dE","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"W",[15,"s"],"X",[15,"t"],"Y",[15,"u"],"Z",[15,"v"],"AA",[15,"w"],"AB",[15,"x"],"AC",[15,"y"],"AD",[15,"z"],"AE",[15,"aA"],"AF",[15,"aB"],"AG",[15,"aC"],"AH",[15,"aD"],"AI",[15,"aE"],"DG",[15,"dB"],"AJ",[15,"aF"],"AK",[15,"aG"],"AL",[15,"aH"],"AM",[15,"aI"],"AN",[15,"aJ"],"AO",[15,"aK"],"AP",[15,"aL"],"AQ",[15,"aM"],"AR",[15,"aN"],"AS",[15,"aO"],"AT",[15,"aP"],"AU",[15,"aQ"],"AV",[15,"aR"],"AW",[15,"aS"],"AX",[15,"aT"],"AY",[15,"aU"],"AZ",[15,"aV"],"BA",[15,"aW"],"BB",[15,"aX"],"BC",[15,"aY"],"DI",[15,"dD"],"BD",[15,"aZ"],"BE",[15,"bA"],"BF",[15,"bB"],"BG",[15,"bC"],"BH",[15,"bD"],"BI",[15,"bE"],"BJ",[15,"bF"],"BK",[15,"bG"],"BL",[15,"bH"],"BM",[15,"bI"],"BN",[15,"bJ"],"BO",[15,"bK"],"BP",[15,"bL"],"BQ",[15,"bM"],"BR",[15,"bN"],"BS",[15,"bO"],"DH",[15,"dC"],"BT",[15,"bP"],"BU",[15,"bQ"],"BV",[15,"bR"],"BW",[15,"bS"],"BX",[15,"bT"],"BY",[15,"bU"],"CA",[15,"bV"],"CB",[15,"bW"],"CC",[15,"bX"],"CD",[15,"bY"],"CE",[15,"bZ"],"CF",[15,"cA"],"DJ",[15,"dE"],"CG",[15,"cB"],"CH",[15,"cC"],"CI",[15,"cD"],"CJ",[15,"cE"],"CK",[15,"cF"],"CL",[15,"cG"],"CM",[15,"cH"],"CN",[15,"cI"],"CO",[15,"cJ"],"CP",[15,"cK"],"CQ",[15,"cL"],"CR",[15,"cM"],"CS",[15,"cN"],"CT",[15,"cO"],"CU",[15,"cP"],"CV",[15,"cQ"],"CW",[15,"cR"],"CX",[15,"cS"],"CY",[15,"cT"],"CZ",[15,"cU"],"DA",[15,"cV"],"DB",[15,"cW"],"DC",[15,"cX"],"DD",[15,"cY"],"DE",[15,"cZ"],"DF",[15,"dA"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_webPrivacyTasks",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"d",[46,"f"],[52,"g",["b"]],[65,"h",[7,[17,[15,"c"],"DA"],[17,[15,"c"],"BC"],[17,[15,"c"],"DJ"]],[46,[53,[2,[15,"f"],"setHitData",[7,[15,"h"],[16,[15,"g"],[15,"h"]]]]]]]],[50,"e",[46,"f"],[52,"g",["b"]],[22,[16,[15,"g"],[17,[15,"c"],"BG"]],[46,[53,[2,[15,"f"],"setHitData",[7,[17,[15,"c"],"BG"],[16,[15,"g"],[17,[15,"c"],"BG"]]]]]]],[22,[16,[15,"g"],[17,[15,"c"],"BF"]],[46,[53,[2,[15,"f"],"setHitData",[7,[17,[15,"c"],"BF"],[16,[15,"g"],[17,[15,"c"],"BF"]]]]]]]],[52,"b",["require","internal.getPrivacyStrings"]],[52,"c",[15,"__module_gtagSchema"]],[36,[8,"B",[15,"e"],"A",[15,"d"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_taskConversionAutoDataAnalysis",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"p",[46,"q"],[22,[28,[1,["e",[17,[15,"f"],"EE"]],[20,[2,[15,"q"],"getMetadata",[7,[17,[15,"j"],"K"]]],[17,[15,"b"],"B"]]]],[46,[53,[36]]]],[52,"r",[7]],[65,"s",[15,"o"],[46,[53,[52,"t",["c",[17,[15,"s"],"modelKey"]]],[52,"u",["g",[15,"t"]]],[22,[28,[30,[20,[15,"u"],"string"],[20,[15,"u"],"number"]]],[46,[6]]],[22,[28,["k",[17,[15,"s"],"regexp"],[2,["i",[15,"t"]],"replace",[7,["d","\\s","g"],""]]]],[46,[53,[6]]]],[2,[15,"r"],"push",[7,[17,[15,"s"],"googleAnalysisKey"]]]]]],[22,[28,[17,[15,"r"],"length"]],[46,[36]]],[2,[15,"q"],"mergeHitDataForKey",[7,[17,[15,"h"],"BD"],[8,"cad",[2,[15,"r"],"join",[7,"."]]]]]],[52,"b",[15,"__module_adwordsHitType"]],[52,"c",["require","copyFromDataLayer"]],[52,"d",["require","internal.createRegex"]],[52,"e",["require","internal.isFeatureEnabled"]],[52,"f",[15,"__module_featureFlags"]],[52,"g",["require","getType"]],[52,"h",[15,"__module_gtagSchema"]],[52,"i",["require","makeString"]],[52,"j",[15,"__module_metadataSchema"]],[52,"k",["require","internal.testRegex"]],[52,"l",["d","^(?:[1-9][\\d.,]*)|(?:0?[.,]\\d+)$"]],[52,"m",["d","^[A-Za-z]{3}$"]],[52,"n",["d","^.+$"]],[52,"o",[7,[8,"modelKey","ecommerce.value","googleAnalysisKey",1,"regexp",[15,"l"]],[8,"modelKey","ecommerce.currency","googleAnalysisKey",31,"regexp",[15,"m"]],[8,"modelKey","ecommerce.currencyCode","googleAnalysisKey",33,"regexp",[15,"m"]],[8,"modelKey","ecommerce.purchase.value","googleAnalysisKey",2,"regexp",[15,"l"]],[8,"modelKey","ecommerce.purchase.currency","googleAnalysisKey",39,"regexp",[15,"m"]],[8,"modelKey","ecommerce.purchase.transaction_id","googleAnalysisKey",68,"regexp",[15,"n"]],[8,"modelKey","ecommerce.purchase.actionField.revenue","googleAnalysisKey",3,"regexp",[15,"l"]],[8,"modelKey","ecommerce.purchase.actionField.currency","googleAnalysisKey",41,"regexp",[15,"m"]],[8,"modelKey","ecommerce.purchase.actionField.id","googleAnalysisKey",62,"regexp",[15,"n"]],[8,"modelKey","ecommerce.cart.currencyCode","googleAnalysisKey",45,"regexp",[15,"m"]],[8,"modelKey","ecommerce.transaction_id","googleAnalysisKey",61,"regexp",[15,"n"]],[8,"modelKey","common_model.order.id","googleAnalysisKey",69,"regexp",[15,"n"]],[8,"modelKey","common_model.order.total_amounts.revenue","googleAnalysisKey",10,"regexp",[15,"l"]],[8,"modelKey","common.currency","googleAnalysisKey",42,"regexp",[15,"m"]],[8,"modelKey","orderConversions.currency","googleAnalysisKey",43,"regexp",[15,"m"]],[8,"modelKey","eventModel.value","googleAnalysisKey",4,"regexp",[15,"l"]],[8,"modelKey","eventModel.currency","googleAnalysisKey",34,"regexp",[15,"m"]],[8,"modelKey","eventModel.transaction_id","googleAnalysisKey",64,"regexp",[15,"n"]],[8,"modelKey","context.localization.currency_code","googleAnalysisKey",35,"regexp",[15,"m"]],[8,"modelKey","leadsHookData.googleConversion.value","googleAnalysisKey",15,"regexp",[15,"l"]],[8,"modelKey","leadsHookData.googleConversion.currency","googleAnalysisKey",44,"regexp",[15,"m"]],[8,"modelKey","orderData.attributes.order_number","googleAnalysisKey",74,"regexp",[15,"n"]],[8,"modelKey","order.id","googleAnalysisKey",75,"regexp",[15,"n"]],[8,"modelKey","transaction.id","googleAnalysisKey",76,"regexp",[15,"n"]],[8,"modelKey","transactionTotal","googleAnalysisKey",5,"regexp",[15,"l"]],[8,"modelKey","value","googleAnalysisKey",6,"regexp",[15,"l"]],[8,"modelKey","totalValue","googleAnalysisKey",7,"regexp",[15,"l"]],[8,"modelKey","ecomm_totalvalue","googleAnalysisKey",8,"regexp",[15,"l"]],[8,"modelKey","price","googleAnalysisKey",9,"regexp",[15,"l"]],[8,"modelKey","conversionValue","googleAnalysisKey",11,"regexp",[15,"l"]],[8,"modelKey","ihAmount","googleAnalysisKey",12,"regexp",[15,"l"]],[8,"modelKey","wp_conversion_value","googleAnalysisKey",13,"regexp",[15,"l"]],[8,"modelKey","revenue","googleAnalysisKey",14,"regexp",[15,"l"]],[8,"modelKey","currency","googleAnalysisKey",32,"regexp",[15,"m"]],[8,"modelKey","transactionCurrency","googleAnalysisKey",36,"regexp",[15,"m"]],[8,"modelKey","currencyCode","googleAnalysisKey",37,"regexp",[15,"m"]],[8,"modelKey","ihCurrency","googleAnalysisKey",38,"regexp",[15,"m"]],[8,"modelKey","CurrCode","googleAnalysisKey",40,"regexp",[15,"m"]],[8,"modelKey","transactionId","googleAnalysisKey",63,"regexp",[15,"n"]],[8,"modelKey","transaction_id","googleAnalysisKey",65,"regexp",[15,"n"]],[8,"modelKey","order_id","googleAnalysisKey",66,"regexp",[15,"n"]],[8,"modelKey","orderId","googleAnalysisKey",67,"regexp",[15,"n"]],[8,"modelKey","ihConfirmID","googleAnalysisKey",70,"regexp",[15,"n"]],[8,"modelKey","wp_order_id","googleAnalysisKey",71,"regexp",[15,"n"]],[8,"modelKey","orderID","googleAnalysisKey",72,"regexp",[15,"n"]],[8,"modelKey","id","googleAnalysisKey",73,"regexp",[15,"n"]]]],[36,[8,"A",[15,"p"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__ccd_ads_first":{"2":true,"5":true}
,
"__ccd_ads_last":{"2":true,"5":true}
,
"__cid":{"2":true,"3":true,"5":true}
,
"__e":{"2":true,"5":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__ccd_ads_first":{"read_data_layer":{"allowedKeys":"any"}}
,
"__ccd_ads_last":{}
,
"__cid":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}


}



,"security_groups":{
"google":[
"__ccd_ads_first"
,
"__ccd_ads_last"
,
"__cid"
,
"__e"

]


}



};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ea=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ea(this),ia=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&ba(c,g,{configurable:!0,writable:!0,value:m})}};
ia("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ja=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},la;
if(typeof Object.setPrototypeOf=="function")la=Object.setPrototypeOf;else{var ma;a:{var oa={a:!0},pa={};try{pa.__proto__=oa;ma=pa.a;break a}catch(a){}ma=!1}la=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=la,sa=function(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Dq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ua=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},va=function(a){return a instanceof Array?a:ua(l(a))},xa=function(a){return wa(a,a)},wa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},ya=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ia("Object.assign",function(a){return a||ya});
var za=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Aa=this||self,Ba=function(a,b){function c(){}c.prototype=b.prototype;a.Dq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Br=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ca=function(a,b){this.type=a;this.data=b};var Da=function(){this.map=new Map;this.C=new Set};k=Da.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.C.has(a)||this.map.set(String(a),b)};k.ol=function(a,b){this.set(a,b);this.C.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.C.has(a)||this.map.delete(String(a))};var Ea=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Da.prototype.wa=function(){return Ea(this,1)};Da.prototype.Zb=function(){return Ea(this,2)};Da.prototype.Ib=function(){return Ea(this,3)};var Fa=function(){this.map={};this.C={}};k=Fa.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};k.ol=function(a,b){this.set(a,b);this.C["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ga=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Fa.prototype.wa=function(){return Ga(this,1)};Fa.prototype.Zb=function(){return Ga(this,2)};Fa.prototype.Ib=function(){return Ga(this,3)};var Ha=function(){};Ha.prototype.reset=function(){};var Ia=[],Ja={};function Ka(a){return Ia[a]===void 0?!1:Ia[a]};var La=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.tb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=Ka(15)?new Da:new Fa};k=La.prototype;k.add=function(a,b){this.tb||this.values.set(a,b)};k.oh=function(a,b){this.tb||this.values.ol(a,b)};k.set=function(a,b){this.tb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};
k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new La(this.P,this);this.C&&a.Nb(this.C);a.Vc(this.H);a.Ld(this.N);return a};k.Cd=function(){return this.P};k.Nb=function(a){this.C=a};k.Yl=function(){return this.C};k.Vc=function(a){this.H=a};k.aj=function(){return this.H};k.Ua=function(){this.tb=!0};k.Ld=function(a){this.N=a};k.sb=function(){return this.N};var Ma=function(a,b){this.ba=a;this.parent=b;this.P=this.H=void 0;this.tb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};Ma.prototype.add=function(a,b){Na(this,a,b,!1)};Ma.prototype.oh=function(a,b){Na(this,a,b,!0)};var Na=function(a,b,c,d){a.tb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=Ma.prototype;k.set=function(a,b){this.tb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.rb=function(){var a=new Ma(this.ba,this);this.H&&a.Nb(this.H);a.Vc(this.N);a.Ld(this.P);return a};k.Cd=function(){return this.ba};k.Nb=function(a){this.H=a};k.Yl=function(){return this.H};k.Vc=function(a){this.N=a};k.aj=function(){return this.N};k.Ua=function(){this.tb=!0};k.Ld=function(a){this.P=a};k.sb=function(){return this.P};var Oa=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.km=a;this.Ql=c===void 0?!1:c;this.debugInfo=[];this.C=b};sa(Oa,Error);var Pa=function(a){return a instanceof Oa?a:new Oa(a,void 0,!0)};var Qa=new Map;function Ra(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Sa(a,e.value),c instanceof Ca);e=d.next());return c}function Sa(a,b){try{var c=l(b),d=c.next().value,e=ua(c),f,g=String(d);f=Ka(17)?Qa.has(g)?Qa.get(g):a.get(g):a.get(g);if(!f||typeof f.invoke!=="function")throw Pa(Error("Attempting to execute non-function "+b[0]+"."));return Ka(18)?f.apply(a,e):f.invoke.apply(f,[a].concat(va(e)))}catch(m){var h=a.Yl();h&&h(m,b.context?{id:b[0],line:b.context.line}:null);throw m;}};var Ta=function(){this.H=new Ha;this.C=Ka(16)?new Ma(this.H):new La(this.H)};k=Ta.prototype;k.Cd=function(){return this.H};k.Nb=function(a){this.C.Nb(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Bj([a].concat(va(za.apply(1,arguments))))};k.Bj=function(){for(var a,b=l(za.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Sa(this.C,c.value);return a};
k.fo=function(a){var b=za.apply(1,arguments),c=this.C.rb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Sa(c,f.value);return d};k.Ua=function(){this.C.Ua()};var Ua=function(){this.Ca=!1;this.aa=new Fa};k=Ua.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};function Wa(){for(var a=Xa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Za(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Xa,$a;function ab(a){Xa=Xa||Za();$a=$a||Wa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Xa[m],Xa[n],Xa[p],Xa[q])}return b.join("")}
function bb(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=$a[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Xa=Xa||Za();$a=$a||Wa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var cb={};function db(a,b){cb[a]=cb[a]||[];cb[a][b]=!0}function eb(){cb.GTAG_EVENT_FEATURE_CHANNEL=fb}function gb(a){var b=cb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return ab(c.join("")).replace(/\.+$/,"")}function hb(){for(var a=[],b=cb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function ib(){}function jb(a){return typeof a==="function"}function kb(a){return typeof a==="string"}function lb(a){return typeof a==="number"&&!isNaN(a)}function mb(a){return Array.isArray(a)?a:[a]}function nb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function ob(a,b){if(!lb(a)||!lb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function pb(a,b){for(var c=new qb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function sb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function tb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function ub(a){return Math.round(Number(a))||0}function vb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function wb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function xb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function yb(){return new Date(Date.now())}function zb(){return yb().getTime()}var qb=function(){this.prefix="gtm.";this.values={}};qb.prototype.set=function(a,b){this.values[this.prefix+a]=b};qb.prototype.get=function(a){return this.values[this.prefix+a]};qb.prototype.contains=function(a){return this.get(a)!==void 0};
function Ab(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Bb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Cb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Db(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Eb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Fb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Gb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Hb=/^\w{1,9}$/;function Jb(a,b){a=a||{};b=b||",";var c=[];sb(a,function(d,e){Hb.test(d)&&e&&c.push(d)});return c.join(b)}function Kb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Lb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Mb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Nb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Ob(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,va(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Pb=globalThis.trustedTypes,Qb;function Rb(){var a=null;if(!Pb)return a;try{var b=function(c){return c};a=Pb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Sb(){Qb===void 0&&(Qb=Rb());return Qb};var Tb=function(a){this.C=a};Tb.prototype.toString=function(){return this.C+""};function Ub(a){var b=a,c=Sb(),d=c?c.createScriptURL(b):b;return new Tb(d)}function Vb(a){if(a instanceof Tb)return a.C;throw Error("");};var Wb=xa([""]),Xb=wa(["\x00"],["\\0"]),Yb=wa(["\n"],["\\n"]),Zb=wa(["\x00"],["\\u0000"]);function $b(a){return a.toString().indexOf("`")===-1}$b(function(a){return a(Wb)})||$b(function(a){return a(Xb)})||$b(function(a){return a(Yb)})||$b(function(a){return a(Zb)});var bc=function(a){this.C=a};bc.prototype.toString=function(){return this.C};var cc=function(a){this.Tp=a};function dc(a){return new cc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var ec=[dc("data"),dc("http"),dc("https"),dc("mailto"),dc("ftp"),new cc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function hc(a){var b;b=b===void 0?ec:b;if(a instanceof bc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof cc&&d.Tp(a))return new bc(a)}}var ic=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function jc(a){var b;if(a instanceof bc)if(a instanceof bc)b=a.C;else throw Error("");else b=ic.test(a)?a:void 0;return b};function kc(a,b){var c=jc(b);c!==void 0&&(a.action=c)};function lc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var mc=function(a){this.C=a};mc.prototype.toString=function(){return this.C+""};var oc=function(){this.C=nc[0].toLowerCase()};oc.prototype.toString=function(){return this.C};function pc(a,b){var c=[new oc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof oc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var qc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function rc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,sc=window.history,z=document,tc=navigator;function uc(){var a;try{a=tc.serviceWorker}catch(b){return}return a}var vc=z.currentScript,wc=vc&&vc.src;function xc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function yc(a){return(tc.userAgent||"").indexOf(a)!==-1}function zc(){return yc("Firefox")||yc("FxiOS")}function Ac(){return(yc("GSA")||yc("GoogleApp"))&&(yc("iPhone")||yc("iPad"))}function Bc(){return yc("Edg/")||yc("EdgA/")||yc("EdgiOS/")}
var Cc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Dc={onload:1,src:1,width:1,height:1,style:1};function Ec(a,b,c){b&&sb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Fc(a,b,c,d,e){var f=z.createElement("script");Ec(f,d,Cc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Ub(rc(a));f.src=Vb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Gc(){if(wc){var a=wc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Hc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Ec(g,c,Dc);d&&sb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Ic(a,b,c,d){return Jc(a,b,c,d)}function Kc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Lc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function B(a){x.setTimeout(a,0)}function Mc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Nc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Oc(a){var b=z.createElement("div"),c=b,d,e=rc("A<div>"+a+"</div>"),f=Sb(),g=f?f.createHTML(e):e;d=new mc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof mc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Pc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Qc(a,b,c){var d;try{d=tc.sendBeacon&&tc.sendBeacon(a)}catch(e){db("TAGGING",15)}d?b==null||b():Jc(a,b,c)}function Rc(a,b){try{return tc.sendBeacon(a,b)}catch(c){db("TAGGING",15)}return!1}var Tc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Uc(a,b,c,d,e){if(Vc()){var f=Object.assign({},Tc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Dh)return e==null||e(),!1;if(b){var h=
Rc(a,b);h?d==null||d():e==null||e();return h}Wc(a,d,e);return!0}function Vc(){return typeof x.fetch==="function"}function Xc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Yc(){var a=x.performance;if(a&&jb(a.now))return a.now()}
function Zc(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function $c(){return x.performance||void 0}function ad(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Jc=function(a,b,c,d){var e=new Image(1,1);Ec(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Wc=Qc;function bd(a,b){return this.evaluate(a)&&this.evaluate(b)}function cd(a,b){return this.evaluate(a)===this.evaluate(b)}function dd(a,b){return this.evaluate(a)||this.evaluate(b)}function ed(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function fd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function gd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ua&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var hd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,id=function(a){if(a==null)return String(a);var b=hd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},jd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},kd=function(a){if(!a||id(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!jd(a,"constructor")&&!jd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
jd(a,b)},ld=function(a,b){var c=b||(id(a)=="array"?[]:{}),d;for(d in a)if(jd(a,d)){var e=a[d];id(e)=="array"?(id(c[d])!="array"&&(c[d]=[]),c[d]=ld(e,c[d])):kd(e)?(kd(c[d])||(c[d]={}),c[d]=ld(e,c[d])):c[d]=e}return c};function md(a){if(a==void 0||Array.isArray(a)||kd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function nd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var od=function(a){a=a===void 0?[]:a;this.aa=new Fa;this.values=[];this.Ca=!1;for(var b in a)a.hasOwnProperty(b)&&(nd(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=od.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof od?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ca)if(a==="length"){if(!nd(b))throw Pa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else nd(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():nd(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.wa=function(){for(var a=this.aa.wa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Zb=function(){for(var a=this.aa.Zb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Ib=function(){for(var a=this.aa.Ib(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){nd(a)?delete this.values[Number(a)]:this.Ca||this.aa.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,va(za.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=za.apply(2,arguments);return b===void 0&&c.length===0?new od(this.values.splice(a)):new od(this.values.splice.apply(this.values,[a,b||0].concat(va(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,va(za.apply(0,arguments)))};k.has=function(a){return nd(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Ua=function(){this.Ca=!0;Object.freeze(this.values)};k.tb=function(){return this.Ca};
function pd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var qd=function(a,b){this.functionName=a;this.zd=b;this.aa=new Fa;this.Ca=!1};k=qd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new od(this.wa())};k.invoke=function(a){return this.zd.call.apply(this.zd,[new rd(this,a)].concat(va(za.apply(1,arguments))))};k.apply=function(a,b){return this.zd.apply(new rd(this,a),b)};k.Lb=function(a){var b=za.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(va(b)))}catch(c){}};
k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};k.Ua=function(){this.Ca=!0};k.tb=function(){return this.Ca};var sd=function(a,b){qd.call(this,a,b)};sa(sd,qd);var td=function(a,b){qd.call(this,a,b)};sa(td,qd);var rd=function(a,b){this.zd=a;this.K=b};
rd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Sa(b,a):a};rd.prototype.getName=function(){return this.zd.getName()};rd.prototype.Cd=function(){return this.K.Cd()};var ud=function(){this.map=new Map};ud.prototype.set=function(a,b){this.map.set(a,b)};ud.prototype.get=function(a){return this.map.get(a)};var vd=function(){this.keys=[];this.values=[]};vd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};vd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function wd(){try{return Map?new ud:new vd}catch(a){return new vd}};var xd=function(a){if(a instanceof xd)return a;if(md(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};xd.prototype.getValue=function(){return this.value};xd.prototype.toString=function(){return String(this.value)};var zd=function(a){this.promise=a;this.Ca=!1;this.aa=new Fa;this.aa.set("then",yd(this));this.aa.set("catch",yd(this,!0));this.aa.set("finally",yd(this,!1,!0))};k=zd.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ca||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ca||this.aa.remove(a)};k.wa=function(){return this.aa.wa()};k.Zb=function(){return this.aa.Zb()};k.Ib=function(){return this.aa.Ib()};
var yd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new sd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof sd||(d=void 0);e instanceof sd||(e=void 0);var f=this.K.rb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new xd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new zd(h)})};zd.prototype.Ua=function(){this.Ca=!0};zd.prototype.tb=function(){return this.Ca};function Ad(a,b,c){var d=wd(),e=function(g,h){for(var m=g.wa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof od){var m=[];d.set(g,m);for(var n=g.wa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof zd)return g.promise.then(function(u){return Ad(u,b,1)},function(u){return Promise.reject(Ad(u,b,1))});if(g instanceof Ua){var q={};d.set(g,q);e(g,q);return q}if(g instanceof sd){var r=function(){for(var u=
za.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Bd(u[w],b,c);var y=new La(b?b.Cd():new Ha);b&&y.Ld(b.sb());return f(g.invoke.apply(g,[y].concat(va(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof xd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Bd(a,b,c){var d=wd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||tb(g)){var m=new od;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(kd(g)){var p=new Ua;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new sd("",function(){for(var u=za.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Ad(this.evaluate(u[w]),b,c);return f(this.K.aj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new xd(g)};return f(a)};var Cd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof od)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new od(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new od(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new od(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
va(za.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Pa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Pa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Pa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Pa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=pd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new od(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=pd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(va(za.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,va(za.apply(1,arguments)))}};var Dd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Ed=new Ca("break"),Fd=new Ca("continue");function Gd(a,b){return this.evaluate(a)+this.evaluate(b)}function Hd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Id(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof od))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Pa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Ad(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Pa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Dd.hasOwnProperty(e)){var m=2;m=1;var n=Ad(f,void 0,m);return Bd(d[e].apply(d,n),this.K)}throw Pa(Error("TypeError: "+e+" is not a function"));}if(d instanceof od){if(d.has(e)){var p=d.get(String(e));if(p instanceof sd){var q=pd(f);return p.invoke.apply(p,[this.K].concat(va(q)))}throw Pa(Error("TypeError: "+e+" is not a function"));}if(Cd.supportedMethods.indexOf(e)>=
0){var r=pd(f);return Cd[e].call.apply(Cd[e],[d,this.K].concat(va(r)))}}if(d instanceof sd||d instanceof Ua||d instanceof zd){if(d.has(e)){var t=d.get(e);if(t instanceof sd){var u=pd(f);return t.invoke.apply(t,[this.K].concat(va(u)))}throw Pa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof sd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof xd&&e==="toString")return d.toString();throw Pa(Error("TypeError: Object has no '"+
e+"' property."));}function Jd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Kd(){var a=za.apply(0,arguments),b=this.K.rb(),c=Ra(b,a);if(c instanceof Ca)return c}function Ld(){return Ed}function Md(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ca)return d}}
function Nd(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.oh(c,d)}}}function Od(){return Fd}function Pd(a,b){return new Ca(a,this.evaluate(b))}function Qd(a,b){for(var c=za.apply(2,arguments),d=new od,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(va(c));this.K.add(a,this.evaluate(g))}function Rd(a,b){return this.evaluate(a)/this.evaluate(b)}
function Sd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof xd,f=d instanceof xd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Td(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Ud(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ra(f,d);if(g instanceof Ca){if(g.type==="break")break;if(g.type==="return")return g}}}
function Vd(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(f){return f},c);if(b instanceof Ua||b instanceof zd||b instanceof od||b instanceof sd){var d=b.wa(),e=d.length;return Ud(a,function(){return e},function(f){return d[f]},c)}}function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){g.set(d,h);return g},e,f)}
function Xd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.oh(d,h);return m},e,f)}function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Vd(function(h){var m=g.rb();m.add(d,h);return m},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){g.set(d,h);return g},e,f)}
function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.oh(d,h);return m},e,f)}function be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return $d(function(h){var m=g.rb();m.add(d,h);return m},e,f)}
function $d(a,b,c){if(typeof b==="string")return Ud(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof od)return Ud(a,function(){return b.length()},function(d){return b.get(d)},c);throw Pa(Error("The value is not iterable."));}
function ce(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof od))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.rb();for(e(g,m);Sa(m,b);){var n=Ra(m,h);if(n instanceof Ca){if(n.type==="break")break;if(n.type==="return")return n}var p=g.rb();e(m,p);Sa(p,c);m=p}}
function de(a,b){var c=za.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof od))throw Error("Error: non-List value given for Fn argument names.");return new sd(a,function(){return function(){var f=za.apply(0,arguments),g=d.rb();g.sb()===void 0&&g.Ld(this.K.sb());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new od(h));var r=Ra(g,c);if(r instanceof Ca)return r.type===
"return"?r.data:r}}())}function ee(a){var b=this.evaluate(a),c=this.K;if(fe&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ge(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Pa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ua||d instanceof zd||d instanceof od||d instanceof sd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:nd(e)&&(c=d[e]);else if(d instanceof xd)return;return c}function he(a,b){return this.evaluate(a)>this.evaluate(b)}function ie(a,b){return this.evaluate(a)>=this.evaluate(b)}
function je(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof xd&&(c=c.getValue());d instanceof xd&&(d=d.getValue());return c===d}function ke(a,b){return!je.call(this,a,b)}function le(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ra(this.K,d);if(e instanceof Ca)return e}var fe=!1;
function me(a,b){return this.evaluate(a)<this.evaluate(b)}function ne(a,b){return this.evaluate(a)<=this.evaluate(b)}function oe(){for(var a=new od,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function pe(){for(var a=new Ua,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function qe(a,b){return this.evaluate(a)%this.evaluate(b)}
function re(a,b){return this.evaluate(a)*this.evaluate(b)}function se(a){return-this.evaluate(a)}function te(a){return!this.evaluate(a)}function ue(a,b){return!Sd.call(this,a,b)}function ve(){return null}function we(a,b){return this.evaluate(a)||this.evaluate(b)}function xe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function ye(a){return this.evaluate(a)}function ze(){return za.apply(0,arguments)}function Ae(a){return new Ca("return",this.evaluate(a))}
function Be(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Pa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof sd||d instanceof od||d instanceof Ua)&&d.set(String(e),f);return f}function Ce(a,b){return this.evaluate(a)-this.evaluate(b)}
function De(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ca){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ca&&(g.type==="return"||g.type==="continue")))return g}
function Ee(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Fe(a){var b=this.evaluate(a);return b instanceof sd?"function":typeof b}function Ge(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function He(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ra(this.K,e);if(f instanceof Ca){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ra(this.K,e);if(g instanceof Ca){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ie(a){return~Number(this.evaluate(a))}function Je(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Ke(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Le(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ne(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Pe(){}
function Qe(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ca)return d}catch(h){if(!(h instanceof Oa&&h.Ql))throw h;var e=this.K.rb();a!==""&&(h instanceof Oa&&(h=h.km),e.add(a,new xd(h)));var f=this.evaluate(c),g=Ra(e,f);if(g instanceof Ca)return g}}function Re(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Oa&&f.Ql))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ca)return e;if(c)throw c;if(d instanceof Ca)return d};var Te=function(){this.C=new Ta;Se(this)};Te.prototype.execute=function(a){return this.C.Bj(a)};var Se=function(a){var b=function(c,d){var e=new td(String(c),d);e.Ua();var f=String(c);a.C.C.set(f,e);Qa.set(f,e)};b("map",pe);b("and",bd);b("contains",ed);b("equals",cd);b("or",dd);b("startsWith",fd);b("variable",gd)};Te.prototype.Nb=function(a){this.C.Nb(a)};var Ve=function(){this.H=!1;this.C=new Ta;Ue(this);this.H=!0};Ve.prototype.execute=function(a){return We(this.C.Bj(a))};var Xe=function(a,b,c){return We(a.C.fo(b,c))};Ve.prototype.Ua=function(){this.C.Ua()};
var Ue=function(a){var b=function(c,d){var e=String(c),f=new td(e,d);f.Ua();a.C.C.set(e,f);Qa.set(e,f)};b(0,Gd);b(1,Hd);b(2,Id);b(3,Jd);b(56,Me);b(57,Je);b(58,Ie);b(59,Oe);b(60,Ke);b(61,Le);b(62,Ne);b(53,Kd);b(4,Ld);b(5,Md);b(68,Qe);b(52,Nd);b(6,Od);b(49,Pd);b(7,oe);b(8,pe);b(9,Md);b(50,Qd);b(10,Rd);b(12,Sd);b(13,Td);b(67,Re);b(51,de);b(47,Wd);b(54,Xd);b(55,Yd);b(63,ce);b(64,Zd);b(65,ae);b(66,be);b(15,ee);b(16,ge);b(17,ge);b(18,he);b(19,ie);b(20,je);b(21,ke);b(22,le);b(23,me);b(24,ne);b(25,qe);b(26,
re);b(27,se);b(28,te);b(29,ue);b(45,ve);b(30,we);b(32,xe);b(33,xe);b(34,ye);b(35,ye);b(46,ze);b(36,Ae);b(43,Be);b(37,Ce);b(38,De);b(39,Ee);b(40,Fe);b(44,Pe);b(41,Ge);b(42,He)};Ve.prototype.Cd=function(){return this.C.Cd()};Ve.prototype.Nb=function(a){this.C.Nb(a)};Ve.prototype.Vc=function(a){this.C.Vc(a)};
function We(a){if(a instanceof Ca||a instanceof sd||a instanceof od||a instanceof Ua||a instanceof zd||a instanceof xd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Ye=function(a){this.message=a};function Ze(a){a.Ir=!0;return a};var $e=Ze(function(a){return typeof a==="string"});function af(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Ye("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function bf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var cf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function df(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+af(e)+c}a<<=2;d||(a|=32);return c=""+af(a|b)+c}
function ef(a,b){var c;var d=a.Uc,e=a.Sc;d===void 0?c="":(e||(e=0),c=""+df(1,1)+af(d<<2|e));var f=a.Pl,g=a.Mo,h="4"+c+(f?""+df(2,1)+af(f):"")+(g?""+df(12,1)+af(g):""),m,n=a.Cj;m=n&&cf.test(n)?""+df(3,2)+n:"";var p,q=a.yj;p=q?""+df(4,1)+af(q):"";var r;var t=a.ctid;if(t&&b){var u=df(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+af(1+y.length)+(a.bm||0)+y}}else r="";var A=a.Cq,C=a.ve,E=a.Ma,G=a.Mr,I=h+m+p+r+(A?""+df(6,1)+af(A):"")+(C?""+df(7,3)+af(C.length)+
C:"")+(E?""+df(8,3)+af(E.length)+E:"")+(G?""+df(9,3)+af(G.length)+G:""),M;var T=a.Rl;T=T===void 0?{}:T;for(var ca=[],P=l(Object.keys(T)),ha=P.next();!ha.done;ha=P.next()){var da=ha.value;ca[Number(da)]=T[da]}if(ca.length){var ka=df(10,3),X;if(ca.length===0)X=af(0);else{for(var W=[],ta=0,ra=!1,na=0;na<ca.length;na++){ra=!0;var Va=na%6;ca[na]&&(ta|=1<<Va);Va===5&&(W.push(af(ta)),ta=0,ra=!1)}ra&&W.push(af(ta));X=W.join("")}var Ya=X;M=""+ka+af(Ya.length)+Ya}else M="";var rb=a.lm,ac=a.sq;return I+M+(rb?
""+df(11,3)+af(rb.length)+rb:"")+(ac?""+df(13,3)+af(ac.length)+ac:"")};var ff=function(){function a(b){return{toString:function(){return b}}}return{Om:a("consent"),Rj:a("convert_case_to"),Sj:a("convert_false_to"),Tj:a("convert_null_to"),Uj:a("convert_true_to"),Vj:a("convert_undefined_to"),Oq:a("debug_mode_metadata"),Ra:a("function"),zi:a("instance_name"),jo:a("live_only"),ko:a("malware_disabled"),METADATA:a("metadata"),no:a("original_activity_id"),jr:a("original_vendor_template_id"),ir:a("once_on_load"),mo:a("once_per_event"),ql:a("once_per_load"),lr:a("priority_override"),
qr:a("respected_consent_types"),Al:a("setup_tags"),mh:a("tag_id"),Il:a("teardown_tags")}}();var Cf;var Df=[],Ef=[],Ff=[],Gf=[],Hf=[],Jf,Kf,Lf;function Mf(a){Lf=Lf||a}
function Nf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Df.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Gf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Ff.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Of(p[r])}Ef.push(p)}}
function Of(a){}var Pf,Qf=[],Rf=[];function Sf(a,b){var c={};c[ff.Ra]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Tf(a,b,c){try{return Kf(Uf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Uf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Vf(a[e],b,c));return d},Vf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Vf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Df[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[ff.zi]);try{var m=Uf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Wf(m,{event:b,index:f,type:2,
name:h});Pf&&(d=Pf.Po(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Vf(a[n],b,c)]=Vf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Vf(a[q],b,c);Lf&&(p=p||Lf.Qp(r));d.push(r)}return Lf&&p?Lf.Uo(d):d.join("");case "escape":d=Vf(a[1],b,c);if(Lf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Lf.Rp(a))return Lf.hq(d);d=String(d);for(var t=2;t<a.length;t++)nf[a[t]]&&(d=nf[a[t]](d));return d;
case "tag":var u=a[1];if(!Gf[u])throw Error("Unable to resolve tag reference "+u+".");return{Vl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[ff.Ra]=a[1];var w=Tf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Wf=function(a,b){var c=a[ff.Ra],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Jf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Qf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Eb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Df[q];break;case 1:r=Gf[q];break;default:n="";break a}var t=r&&r[ff.zi];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Rf.indexOf(c)===-1){Rf.push(c);
var y=zb();u=e(g);var A=zb()-y,C=zb();v=Cf(c,h,b);w=A-(zb()-C)}else if(e&&(u=e(g)),!e||f)v=Cf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),md(u)?(Array.isArray(u)?Array.isArray(v):kd(u)?kd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Xf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};sa(Xf,Error);Xf.prototype.getMessage=function(){return this.message};function Yf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Yf(a[c],b[c])}};function Zf(){return function(a,b){var c;var d=$f;a instanceof Oa?(a.C=d,c=a):c=new Oa(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function $f(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)lb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function ag(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=bg(a),f=0;f<Ef.length;f++){var g=Ef[f],h=cg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Gf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function cg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function bg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Tf(Ff[c],a));return b[c]}};function dg(a,b){b[ff.Rj]&&typeof a==="string"&&(a=b[ff.Rj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(ff.Tj)&&a===null&&(a=b[ff.Tj]);b.hasOwnProperty(ff.Vj)&&a===void 0&&(a=b[ff.Vj]);b.hasOwnProperty(ff.Uj)&&a===!0&&(a=b[ff.Uj]);b.hasOwnProperty(ff.Sj)&&a===!1&&(a=b[ff.Sj]);return a};var eg=function(){this.C={}},gg=function(a,b){var c=fg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,va(za.apply(0,arguments)))})};function hg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Xf(c,d,g);}}
function ig(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(va(za.apply(1,arguments))));hg(e,b,d,g);hg(f,b,d,g)}}}};var mg=function(){var a=data.permissions||{},b=jg.ctid,c=this;this.H={};this.C=new eg;var d={},e={},f=ig(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(va(za.apply(1,arguments)))):{}});sb(a,function(g,h){function m(p){var q=za.apply(1,arguments);if(!n[p])throw kg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(va(q)))}var n={};sb(h,function(p,q){var r=lg(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Nl&&!e[p]&&(e[p]=r.Nl)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw kg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(va(t.slice(1))))}})},ng=function(a){return fg.H[a]||function(){}};
function lg(a,b){var c=Sf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=kg;try{return Wf(c)}catch(d){return{assert:function(e){throw new Xf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Xf(a,{},"Permission "+a+" is unknown.");}}}}function kg(a,b,c){return new Xf(a,b,c)};var og=!1;var pg={};pg.Hm=vb('');pg.fp=vb('');function ug(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var vg=[];function wg(a){switch(a){case 1:return 0;case 38:return 12;case 53:return 1;case 54:return 2;case 52:return 6;case 215:return 18;case 211:return 17;case 75:return 3;case 103:return 13;case 197:return 14;case 203:return 15;case 114:return 11;case 116:return 4;case 209:return 16;case 135:return 8;case 136:return 5}}function xg(a,b){vg[a]=b;var c=wg(a);c!==void 0&&(Ia[c]=b)}function D(a){xg(a,!0)}
D(39);D(34);D(35);D(36);
D(56);D(145);D(153);D(144);D(120);
D(5);D(111);D(139);D(87);
D(92);D(159);D(132);
D(20);D(72);D(113);
D(154);D(116);xg(23,!1),D(24);Ja[1]=ug('1',6E4);Ja[3]=ug('10',1);
Ja[2]=ug('',50);D(29);yg(26,25);D(37);
D(9);D(91);
D(123);D(158);D(71);
D(136);D(127);
D(27);D(69);
D(135);D(95);D(38);
D(103);D(112);D(63);D(152);
D(101);
D(122);D(121);
D(108);D(134);
D(31);D(22);

D(19);

D(90);
D(59);D(208);
D(175);D(185);D(186);

D(192);D(198);
D(200);D(207);function F(a){return!!vg[a]}function yg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?D(b):D(a)};var Ag={},Bg=(Ag.uaa=!0,Ag.uab=!0,Ag.uafvl=!0,Ag.uamb=!0,Ag.uam=!0,Ag.uap=!0,Ag.uapv=!0,Ag.uaw=!0,Ag);
var Jg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Hg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Ig.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Eb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Ig=/^[a-z$_][\w-$]*$/i,Hg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Kg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Lg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Mg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Ng=new qb;function Og(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Ng.get(e);f||(f=new RegExp(b,d),Ng.set(e,f));return f.test(a)}catch(g){return!1}}function Pg(a,b){return String(a).indexOf(String(b))>=0}
function Qg(a,b){return String(a)===String(b)}function Rg(a,b){return Number(a)>=Number(b)}function Sg(a,b){return Number(a)<=Number(b)}function Tg(a,b){return Number(a)>Number(b)}function Ug(a,b){return Number(a)<Number(b)}function Vg(a,b){return Eb(String(a),String(b))};var bh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,ch={Fn:"function",PixieMap:"Object",List:"Array"};
function dh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=bh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof sd?n="Fn":m instanceof od?n="List":m instanceof Ua?n="PixieMap":m instanceof zd?n="PixiePromise":m instanceof xd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((ch[n]||n)+", which does not match required type ")+
((ch[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof sd?d.push("function"):g instanceof od?d.push("Array"):g instanceof Ua?d.push("Object"):g instanceof zd?d.push("Promise"):g instanceof xd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function eh(a){return a instanceof Ua}function fh(a){return eh(a)||a===null||gh(a)}
function hh(a){return a instanceof sd}function ih(a){return hh(a)||a===null||gh(a)}function jh(a){return a instanceof od}function kh(a){return a instanceof xd}function lh(a){return typeof a==="string"}function mh(a){return lh(a)||a===null||gh(a)}function nh(a){return typeof a==="boolean"}function oh(a){return nh(a)||gh(a)}function ph(a){return nh(a)||a===null||gh(a)}function qh(a){return typeof a==="number"}function gh(a){return a===void 0};function rh(a){return""+a}
function sh(a,b){var c=[];return c};function th(a,b){var c=new sd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Pa(g);}});c.Ua();return c}
function uh(a,b){var c=new Ua,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];jb(e)?c.set(d,th(a+"_"+d,e)):kd(e)?c.set(d,uh(a+"_"+d,e)):(lb(e)||kb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ua();return c};function vh(a,b){if(!lh(a))throw H(this.getName(),["string"],arguments);if(!mh(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new Ua;return d=uh("AssertApiSubject",
c)};function wh(a,b){if(!mh(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof zd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ua;return d=uh("AssertThatSubject",c)};function xh(a){return function(){for(var b=za.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Ad(b[e],d));return Bd(a.apply(null,c))}}function yh(){for(var a=Math,b=zh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=xh(a[e].bind(a)))}return c};function Ah(a){return a!=null&&Eb(a,"__cvt_")};function Bh(a){var b;return b};function Ch(a){var b;return b};function Dh(a){try{return encodeURI(a)}catch(b){}};function Eh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Jh(a){if(!mh(a))throw H(this.getName(),["string|undefined"],arguments);};function Kh(a,b){if(!qh(a)||!qh(b))throw H(this.getName(),["number","number"],arguments);return ob(a,b)};function Lh(){return(new Date).getTime()};function Mh(a){if(a===null)return"null";if(a instanceof od)return"array";if(a instanceof sd)return"function";if(a instanceof xd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Nh(a){function b(c){return function(d){try{return c(d)}catch(e){(og||pg.Hm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Bd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Ad(c))}),publicName:"JSON"}};function Oh(a){return ub(Ad(a,this.K))};function Ph(a){return Number(Ad(a,this.K))};function Qh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Rh(a,b,c){var d=null,e=!1;return e?d:null};var zh="floor ceil round max min abs pow sqrt".split(" ");function Sh(){var a={};return{tp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Cm:function(b,c){a[b]=c},reset:function(){a={}}}}function Th(a,b){return function(){return sd.prototype.invoke.apply(a,[b].concat(va(za.apply(0,arguments))))}}
function Uh(a,b){if(!lh(a))throw H(this.getName(),["string","any"],arguments);}
function Vh(a,b){if(!lh(a)||!eh(b))throw H(this.getName(),["string","PixieMap"],arguments);};var Wh={};
Wh.keys=function(a){return new od};
Wh.values=function(a){return new od};
Wh.entries=function(a){return new od};
Wh.freeze=function(a){return a};Wh.delete=function(a,b){return!1};function J(a,b){var c=za.apply(2,arguments),d=a.K.sb();if(!d)throw Error("Missing program state.");if(d.oq){try{d.Ol.apply(null,[b].concat(va(c)))}catch(e){throw db("TAGGING",21),e;}return}d.Ol.apply(null,[b].concat(va(c)))};var Yh=function(){this.H={};this.C={};this.N=!0;};Yh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Yh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Yh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:jb(b)?th(a,b):uh(a,b)};function Zh(a,b){var c=void 0;return c};function $h(){var a={};return a};var K={m:{La:"ad_personalization",U:"ad_storage",V:"ad_user_data",ja:"analytics_storage",fc:"region",fa:"consent_updated",rg:"wait_for_update",bn:"app_remove",dn:"app_store_refund",fn:"app_store_subscription_cancel",gn:"app_store_subscription_convert",hn:"app_store_subscription_renew",jn:"consent_update",Yj:"add_payment_info",Zj:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",bk:"view_cart",Xc:"begin_checkout",Rd:"select_item",jc:"view_item_list",Gc:"select_promotion",kc:"view_promotion",
kb:"purchase",Sd:"refund",xb:"view_item",dk:"add_to_wishlist",kn:"exception",ln:"first_open",mn:"first_visit",qa:"gtag.config",Cb:"gtag.get",nn:"in_app_purchase",Yc:"page_view",on:"screen_view",pn:"session_start",qn:"source_update",rn:"timing_complete",sn:"track_social",Td:"user_engagement",tn:"user_id_update",Le:"gclid_link_decoration_source",Me:"gclid_storage_source",mc:"gclgb",lb:"gclid",ek:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",za:"ads_data_redaction",Ne:"gad_source",Oe:"gad_source_src",
Zc:"gclid_url",fk:"gclsrc",Pe:"gbraid",Xd:"wbraid",Ea:"allow_ad_personalization_signals",yg:"allow_custom_scripts",Qe:"allow_direct_google_requests",zg:"allow_display_features",Ag:"allow_enhanced_conversions",Ob:"allow_google_signals",mb:"allow_interest_groups",un:"app_id",vn:"app_installer_id",wn:"app_name",xn:"app_version",Pb:"auid",yn:"auto_detection_enabled",bd:"aw_remarketing",Oh:"aw_remarketing_only",Bg:"discount",Cg:"aw_feed_country",Dg:"aw_feed_language",sa:"items",Eg:"aw_merchant_id",gk:"aw_basket_type",
Re:"campaign_content",Se:"campaign_id",Te:"campaign_medium",Ue:"campaign_name",Ve:"campaign",We:"campaign_source",Xe:"campaign_term",Qb:"client_id",hk:"rnd",Ph:"consent_update_type",zn:"content_group",An:"content_type",Rb:"conversion_cookie_prefix",Ye:"conversion_id",Oa:"conversion_linker",Qh:"conversion_linker_disabled",dd:"conversion_api",Fg:"cookie_deprecation",nb:"cookie_domain",ob:"cookie_expires",yb:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",cb:"cookie_prefix",Hc:"cookie_update",Yd:"country",
Va:"currency",Rh:"customer_buyer_stage",Ze:"customer_lifetime_value",Sh:"customer_loyalty",Th:"customer_ltv_bucket",af:"custom_map",Uh:"gcldc",fd:"dclid",ik:"debug_mode",oa:"developer_id",Bn:"disable_merchant_reported_purchases",gd:"dc_custom_params",Cn:"dc_natural_search",jk:"dynamic_event_settings",kk:"affiliation",Gg:"checkout_option",Vh:"checkout_step",lk:"coupon",bf:"item_list_name",Wh:"list_name",Dn:"promotions",cf:"shipping",Xh:"tax",Hg:"engagement_time_msec",Ig:"enhanced_client_id",Yh:"enhanced_conversions",
mk:"enhanced_conversions_automatic_settings",Jg:"estimated_delivery_date",Zh:"euid_logged_in_state",df:"event_callback",En:"event_category",Tb:"event_developer_id_string",Gn:"event_label",hd:"event",Kg:"event_settings",Lg:"event_timeout",Hn:"description",In:"fatal",Jn:"experiments",ai:"firebase_id",Zd:"first_party_collection",Mg:"_x_20",oc:"_x_19",nk:"fledge_drop_reason",pk:"fledge",qk:"flight_error_code",rk:"flight_error_message",sk:"fl_activity_category",tk:"fl_activity_group",bi:"fl_advertiser_id",
uk:"fl_ar_dedupe",ef:"match_id",vk:"fl_random_number",wk:"tran",xk:"u",Ng:"gac_gclid",ae:"gac_wbraid",yk:"gac_wbraid_multiple_conversions",zk:"ga_restrict_domain",di:"ga_temp_client_id",Kn:"ga_temp_ecid",jd:"gdpr_applies",Ak:"geo_granularity",Ic:"value_callback",qc:"value_key",rc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Bk:"google_tld",ff:"gpp_sid",hf:"gpp_string",Og:"groups",Ck:"gsa_experiment_id",jf:"gtag_event_feature_usage",Dk:"gtm_up",Jc:"iframe_state",kf:"ignore_referrer",
ei:"internal_traffic_results",Ek:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Pg:"is_passthrough",kd:"_lps",zb:"language",Qg:"legacy_developer_id_string",Pa:"linker",de:"accept_incoming",sc:"decorate_forms",ma:"domains",Mc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Fk:"method",Ln:"name",Gk:"navigation_type",lf:"new_customer",Rg:"non_interaction",Mn:"optimize_id",Hk:"page_hostname",nf:"page_path",Wa:"page_referrer",Db:"page_title",Ik:"passengers",
Jk:"phone_conversion_callback",Nn:"phone_conversion_country_code",Kk:"phone_conversion_css_class",On:"phone_conversion_ids",Lk:"phone_conversion_number",Mk:"phone_conversion_options",Pn:"_platinum_request_status",Qn:"_protected_audience_enabled",pf:"quantity",Sg:"redact_device_info",fi:"referral_exclusion_definition",Rq:"_request_start_time",Vb:"restricted_data_processing",Rn:"retoken",Sn:"sample_rate",gi:"screen_name",Nc:"screen_resolution",Nk:"_script_source",Tn:"search_term",pb:"send_page_view",
ld:"send_to",md:"server_container_url",qf:"session_duration",Tg:"session_engaged",hi:"session_engaged_time",uc:"session_id",Ug:"session_number",rf:"_shared_user_id",tf:"delivery_postal_code",Sq:"_tag_firing_delay",Tq:"_tag_firing_time",Uq:"temporary_client_id",ii:"_timezone",ji:"topmost_url",Un:"tracking_id",ki:"traffic_type",Xa:"transaction_id",vc:"transport_url",Ok:"trip_type",od:"update",Eb:"url_passthrough",Pk:"uptgs",uf:"_user_agent_architecture",vf:"_user_agent_bitness",wf:"_user_agent_full_version_list",
xf:"_user_agent_mobile",yf:"_user_agent_model",zf:"_user_agent_platform",Af:"_user_agent_platform_version",Bf:"_user_agent_wow64",eb:"user_data",li:"user_data_auto_latency",mi:"user_data_auto_meta",ni:"user_data_auto_multi",oi:"user_data_auto_selectors",ri:"user_data_auto_status",wc:"user_data_mode",Vg:"user_data_settings",Qa:"user_id",Wb:"user_properties",Qk:"_user_region",Cf:"us_privacy_string",Fa:"value",Rk:"wbraid_multiple_conversions",rd:"_fpm_parameters",xi:"_host_name",al:"_in_page_command",
bl:"_ip_override",kl:"_is_passthrough_cid",xc:"non_personalized_ads",Ii:"_sst_parameters",nc:"conversion_label",Aa:"page_location",Ub:"global_developer_id_string",nd:"tc_privacy_string"}};var ai={},bi=(ai[K.m.fa]="gcu",ai[K.m.mc]="gclgb",ai[K.m.lb]="gclaw",ai[K.m.ek]="gclid_len",ai[K.m.Ud]="gclgs",ai[K.m.Vd]="gcllp",ai[K.m.Wd]="gclst",ai[K.m.Pb]="auid",ai[K.m.Bg]="dscnt",ai[K.m.Cg]="fcntr",ai[K.m.Dg]="flng",ai[K.m.Eg]="mid",ai[K.m.gk]="bttype",ai[K.m.Qb]="gacid",ai[K.m.nc]="label",ai[K.m.dd]="capi",ai[K.m.Fg]="pscdl",ai[K.m.Va]="currency_code",ai[K.m.Rh]="clobs",ai[K.m.Ze]="vdltv",ai[K.m.Sh]="clolo",ai[K.m.Th]="clolb",ai[K.m.ik]="_dbg",ai[K.m.Jg]="oedeld",ai[K.m.Tb]="edid",ai[K.m.nk]=
"fdr",ai[K.m.pk]="fledge",ai[K.m.Ng]="gac",ai[K.m.ae]="gacgb",ai[K.m.yk]="gacmcov",ai[K.m.jd]="gdpr",ai[K.m.Ub]="gdid",ai[K.m.be]="_ng",ai[K.m.ff]="gpp_sid",ai[K.m.hf]="gpp",ai[K.m.Ck]="gsaexp",ai[K.m.jf]="_tu",ai[K.m.Jc]="frm",ai[K.m.Pg]="gtm_up",ai[K.m.kd]="lps",ai[K.m.Qg]="did",ai[K.m.ee]="fcntr",ai[K.m.fe]="flng",ai[K.m.he]="mid",ai[K.m.lf]=void 0,ai[K.m.Db]="tiba",ai[K.m.Vb]="rdp",ai[K.m.uc]="ecsid",ai[K.m.rf]="ga_uid",ai[K.m.tf]="delopc",ai[K.m.nd]="gdpr_consent",ai[K.m.Xa]="oid",ai[K.m.Pk]=
"uptgs",ai[K.m.uf]="uaa",ai[K.m.vf]="uab",ai[K.m.wf]="uafvl",ai[K.m.xf]="uamb",ai[K.m.yf]="uam",ai[K.m.zf]="uap",ai[K.m.Af]="uapv",ai[K.m.Bf]="uaw",ai[K.m.li]="ec_lat",ai[K.m.mi]="ec_meta",ai[K.m.ni]="ec_m",ai[K.m.oi]="ec_sel",ai[K.m.ri]="ec_s",ai[K.m.wc]="ec_mode",ai[K.m.Qa]="userId",ai[K.m.Cf]="us_privacy",ai[K.m.Fa]="value",ai[K.m.Rk]="mcov",ai[K.m.xi]="hn",ai[K.m.al]="gtm_ee",ai[K.m.xc]="npa",ai[K.m.Ye]=null,ai[K.m.Nc]=null,ai[K.m.zb]=null,ai[K.m.sa]=null,ai[K.m.Aa]=null,ai[K.m.Wa]=null,ai[K.m.ji]=
null,ai[K.m.rd]=null,ai[K.m.Le]=null,ai[K.m.Me]=null,ai[K.m.rc]=null,ai);function ci(a,b){if(a){var c=a.split("x");c.length===2&&(di(b,"u_w",c[0]),di(b,"u_h",c[1]))}}
function ei(a){var b=fi;b=b===void 0?gi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(hi(q.value)),r.push(hi(q.quantity)),r.push(hi(q.item_id)),r.push(hi(q.start_date)),r.push(hi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function gi(a){return ii(a.item_id,a.id,a.item_name)}function ii(){for(var a=l(za.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ji(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function di(a,b,c){c===void 0||c===null||c===""&&!Bg[b]||(a[b]=c)}function hi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ki={},li=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=ob(0,1)===0,b=ob(0,1)===0,c++,c>30)return;return a},ni={uq:mi};function oi(a,b){var c=ki[b],d=c.Em,e=c.experimentId,f=c.Ce;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;ki[b].active||ki[b].Ce>.5?pi(a,e):f<=0||f>1||ni.uq(a,b)}}
function mi(a,b){var c=ki[b],d=c.controlId2;if(!(ob(0,9999)<c.Ce*(c.controlId2&&c.Ce<=.25?4:2)*1E4))return a;qi(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.Ce<=.25?d:void 0,experimentCallback:function(){}});return a}function pi(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function qi(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=li()?0:1;e&&(g|=(li()?0:1)<<1);g===0?(pi(a,c),f()):g===1?pi(a,d):g===2&&pi(a,e)}};var L={J:{Lj:"call_conversion",W:"conversion",Vn:"floodlight",Ef:"ga_conversion",Ei:"landing_page",Ga:"page_view",na:"remarketing",Ta:"user_data_lead",Ja:"user_data_web"}};
var ri={},si=Object.freeze((ri[K.m.Le]=1,ri[K.m.Me]=1,ri[K.m.Ea]=1,ri[K.m.Qe]=1,ri[K.m.Ag]=1,ri[K.m.mb]=1,ri[K.m.bd]=1,ri[K.m.Oh]=1,ri[K.m.Bg]=1,ri[K.m.Cg]=1,ri[K.m.Dg]=1,ri[K.m.sa]=1,ri[K.m.Eg]=1,ri[K.m.Rb]=1,ri[K.m.Oa]=1,ri[K.m.nb]=1,ri[K.m.ob]=1,ri[K.m.yb]=1,ri[K.m.cb]=1,ri[K.m.Va]=1,ri[K.m.Rh]=1,ri[K.m.Ze]=1,ri[K.m.Sh]=1,ri[K.m.Th]=1,ri[K.m.oa]=1,ri[K.m.Bn]=1,ri[K.m.Yh]=1,ri[K.m.Jg]=1,ri[K.m.ai]=1,ri[K.m.Zd]=1,ri[K.m.rc]=1,ri[K.m.Kc]=1,ri[K.m.Lc]=1,ri[K.m.zb]=1,ri[K.m.ee]=1,ri[K.m.fe]=1,ri[K.m.he]=
1,ri[K.m.lf]=1,ri[K.m.Aa]=1,ri[K.m.Wa]=1,ri[K.m.Jk]=1,ri[K.m.Kk]=1,ri[K.m.Lk]=1,ri[K.m.Mk]=1,ri[K.m.Vb]=1,ri[K.m.pb]=1,ri[K.m.ld]=1,ri[K.m.md]=1,ri[K.m.tf]=1,ri[K.m.Xa]=1,ri[K.m.vc]=1,ri[K.m.od]=1,ri[K.m.Eb]=1,ri[K.m.eb]=1,ri[K.m.Qa]=1,ri[K.m.Fa]=1,ri));function ti(a){return ui?z.querySelectorAll(a):null}
function vi(a,b){if(!ui)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var wi=!1;
if(z.querySelectorAll)try{var xi=z.querySelectorAll(":root");xi&&xi.length==1&&xi[0]==z.documentElement&&(wi=!0)}catch(a){}var ui=wi;function yi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function zi(){this.blockSize=-1};function Ai(a,b){this.blockSize=-1;this.blockSize=64;this.N=Aa.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.ba=a;this.R=b;this.ka=Aa.Int32Array?new Int32Array(64):Array(64);Bi===void 0&&(Aa.Int32Array?Bi=new Int32Array(Ci):Bi=Ci);this.reset()}Ba(Ai,zi);for(var Di=[],Ei=0;Ei<63;Ei++)Di[Ei]=0;var Fi=[].concat(128,Di);
Ai.prototype.reset=function(){this.P=this.H=0;var a;if(Aa.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Gi=function(a){for(var b=a.N,c=a.ka,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Bi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Ai.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Gi(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Gi(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Ai.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Fi,56-this.H):this.update(Fi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Gi(this);for(var d=0,e=0;e<this.ba;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Ci=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Bi;function Hi(){Ai.call(this,8,Ii)}Ba(Hi,Ai);var Ii=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ji=/^[0-9A-Fa-f]{64}$/;function Ki(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Li(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Ji.test(a))return Promise.resolve(a);try{var d=Ki(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Mi(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Mi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ni=[],Oi;function Pi(a){Oi?Oi(a):Ni.push(a)}function Qi(a,b){if(!F(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Pi(a),b):c}function Ri(a,b){if(!F(190))return b;var c=Si(a,"");return c!==b?(Pi(a),b):c}function Si(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Ti(a,b){if(!F(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Pi(a),b)}function Ui(){Oi=Vi;for(var a=l(Ni),b=a.next();!b.done;b=a.next())Oi(b.value);Ni.length=0};var Wi={Xm:'512',Ym:'0',Zm:'1000',Zn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',ao:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',xo:Ri(44,'101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104908321~104908323~104909302~104909304~104935091~104935093')},Xi={ap:Number(Wi.Xm)||0,bp:Number(Wi.Ym)||0,ep:Number(Wi.Zm)||0,xp:Wi.Zn.split("~"),yp:Wi.ao.split("~"),Lq:Wi.xo};Object.assign({},Xi);function N(a){db("GTM",a)};
var bj=function(a,b){var c=["tv.1"],d=Yi(a);if(d)return c.push(d),{Za:!1,Dj:c.join("~"),ng:{}};var e={},f=0;var g=Zi(a,function(p,q,r){var t=p.value,u;if(r){var v=q+"__"+f++;u="${userData."+v+"|sha256}";e[v]=t}else u=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).Za;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{Za:g,Dj:h,ng:m,cp:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?$i():aj()}:{Za:g,Dj:h,ng:m}},dj=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=cj(a);return Zi(b,function(){}).Za},Zi=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=ej[g.name];if(h){var m=fj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{Za:d,ej:c}},fj=function(a){var b=gj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(hj.test(e)||
Ji.test(e))}return d},gj=function(a){return ij.indexOf(a)!==-1},aj=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BD6dP0RV5GhwQa1pObjmBnl4cmUONRwMVPviFiMRmYLCETOfGaVUgPIounrK8INuZgpnuLWau3tmsAgDckuYkMM\x3d\x22,\x22version\x22:0},\x22id\x22:\x22baa4e8be-b125-4ce4-a0c2-bf556a893ef4\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BL/yzBlWWmd9oa0xNGwna1cAmsrfLyvdtz5fg+5JF+3rrk0qmAHVRj6OCuMll0oTBZ3zVrevL88d/TCvJFLziO4\x3d\x22,\x22version\x22:0},\x22id\x22:\x22a73df72c-528c-4872-adc8-2ad5aed787a1\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BCMPMLMxwSxccypHORFYkNbjBgqjoB9oQVwWvWe7MZW5bSK98s++Tj+MZpF2GPPZh4zlZSyEqSyNx+eGbFzxmRQ\x3d\x22,\x22version\x22:0},\x22id\x22:\x226e566fec-f826-4006-b302-30af1c9c44ae\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BBuAw1Advm2qdY6vnNWIKDoZSpLD+itgKWpXOsh+U5M1GB7GvnYz1zC3ddtoyeriRcCP3mYSVHcQT1JNY8fPlMI\x3d\x22,\x22version\x22:0},\x22id\x22:\x224351ac64-5ea1-4762-adef-4fbe3e802392\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BBWRsTHiHHGeX9wbntoN87J6FFTc31AdEAZhDNR/XwE5slgsqDT040PMZltVl0+DeRQVwiCeRRld/RK/74e+DDk\x3d\x22,\x22version\x22:0},\x22id\x22:\x222e5ed546-6c33-44c8-8b24-c479e770cd53\x22}]}'},lj=function(a){if(x.Promise){var b=void 0;return b}},qj=function(a,b,c,d,e){if(x.Promise)try{var f=cj(a),g=mj(f,e).then(nj);return g}catch(p){}},sj=function(a){try{return nj(rj(cj(a)))}catch(b){}},kj=function(a,b){var c=void 0;return c},nj=function(a){var b=a.Tc,c=a.time,d=["tv.1"],e=Yi(b);if(e)return d.push(e),{Kb:encodeURIComponent(d.join("~")),ej:!1,Za:!1,time:c,dj:!0};var f=b.filter(function(n){return!fj(n)}),g=Zi(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.ej,m=g.Za;return{Kb:encodeURIComponent(d.join("~")),ej:h,Za:m,time:c,dj:!1}},Yi=function(a){if(a.length===1&&a[0].name==="error_code")return ej.error_code+
"."+a[0].value},pj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(ej[d.name]&&d.value)return!0}return!1},cj=function(a){function b(r,t,u,v){var w=tj(r);w!==""&&(Ji.test(w)?h.push({name:t,value:w,index:v}):h.push({name:t,value:u(w),index:v}))}function c(r,t){var u=r;if(kb(u)||Array.isArray(u)){u=mb(r);for(var v=0;v<u.length;++v){var w=tj(u[v]),y=Ji.test(w);t&&!y&&N(89);!t&&y&&N(88)}}}function d(r,t){var u=r[t];c(u,!1);var v=
uj[t];r[v]&&(r[t]&&N(90),u=r[v],c(u,!0));return u}function e(r,t,u){for(var v=mb(d(r,t)),w=0;w<v.length;++w)b(v[w],t,u)}function f(r,t,u,v){var w=d(r,t);b(w,t,u,v)}function g(r){return function(t){N(64);return r(t)}}var h=[];if(x.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",vj);e(a,"phone_number",wj);e(a,"first_name",g(xj));e(a,"last_name",g(xj));var m=a.home_address||{};e(m,"street",g(yj));e(m,"city",g(yj));e(m,"postal_code",g(zj));e(m,"region",
g(yj));e(m,"country",g(zj));for(var n=mb(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",xj,p);f(q,"last_name",xj,p);f(q,"street",yj,p);f(q,"city",yj,p);f(q,"postal_code",zj,p);f(q,"region",yj,p);f(q,"country",zj,p)}return h},Aj=function(a){var b=a?cj(a):[];return nj({Tc:b})},Bj=function(a){return a&&a!=null&&Object.keys(a).length>0&&x.Promise?cj(a).some(function(b){return b.value&&gj(b.name)&&!Ji.test(b.value)}):!1},tj=function(a){return a==null?"":kb(a)?xb(String(a)):"e0"},zj=function(a){return a.replace(Cj,
"")},xj=function(a){return yj(a.replace(/\s/g,""))},yj=function(a){return xb(a.replace(Dj,"").toLowerCase())},wj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Ej.test(a)?a:"e0"},vj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Fj.test(c))return c}return"e0"},rj=function(a){var b=Yc();try{a.forEach(function(e){if(e.value&&gj(e.name)){var f;var g=e.value,h=x;if(g===""||
g==="e0"||Ji.test(g))f=g;else try{var m=new Hi;m.update(Ki(g));f=Mi(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Tc:a};if(b!==void 0){var d=Yc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Tc:[]}}},mj=function(a,b){if(!a.some(function(d){return d.value&&gj(d.name)}))return Promise.resolve({Tc:a});if(!x.Promise)return Promise.resolve({Tc:[]});var c=b?Yc():void 0;return Promise.all(a.map(function(d){return d.value&&gj(d.name)?Li(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Tc:a};if(c!==void 0){var e=Yc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Tc:[]}})},Dj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Fj=/^\S+@\S+\.\S+$/,Ej=/^\+\d{10,15}$/,Cj=/[.~]/g,hj=/^[0-9A-Za-z_-]{43}$/,Gj={},ej=(Gj.email="em",Gj.phone_number="pn",Gj.first_name="fn",Gj.last_name="ln",Gj.street="sa",Gj.city="ct",Gj.region="rg",Gj.country="co",Gj.postal_code="pc",Gj.error_code="ec",Gj),Hj={},uj=(Hj.email="sha256_email_address",Hj.phone_number="sha256_phone_number",
Hj.first_name="sha256_first_name",Hj.last_name="sha256_last_name",Hj.street="sha256_street",Hj);var ij=Object.freeze(["email","phone_number","first_name","last_name","street"]);
var Ij={},Jj=(Ij[K.m.mb]=1,Ij[K.m.md]=2,Ij[K.m.vc]=2,Ij[K.m.za]=3,Ij[K.m.Ze]=4,Ij[K.m.yg]=5,Ij[K.m.Hc]=6,Ij[K.m.cb]=6,Ij[K.m.nb]=6,Ij[K.m.ed]=6,Ij[K.m.Sb]=6,Ij[K.m.yb]=6,Ij[K.m.ob]=7,Ij[K.m.Vb]=9,Ij[K.m.zg]=10,Ij[K.m.Ob]=11,Ij),Kj={},Lj=(Kj.unknown=13,Kj.standard=14,Kj.unique=15,Kj.per_session=16,Kj.transactions=17,Kj.items_sold=18,Kj);var fb=[];function Mj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Jj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Jj[f],h=b;h=h===void 0?!1:h;db("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(fb[g]=!0)}}};var Nj=function(){this.C=new Set;this.H=new Set},Pj=function(a){var b=Oj.R;a=a===void 0?[]:a;var c=[].concat(va(b.C)).concat([].concat(va(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Qj=function(){var a=[].concat(va(Oj.R.C));a.sort(function(b,c){return b-c});return a},Rj=function(){var a=Oj.R,b=Xi.Lq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Sj={},Tj=Ri(14,"5781"),Uj=Ti(15,Number("0")),Vj=Ri(19,"dataLayer");Ri(20,"");Ri(16,"ChAI8Mm9wwYQ/obQruSG6Z55EiYAAjDAIAOE631U/E/+91uJILyhhTA7olb3EogcYaqqjP0oIQuvthoCYCM\x3d");var Wj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Xj={__paused:1,__tg:1},Yj;for(Yj in Wj)Wj.hasOwnProperty(Yj)&&(Xj[Yj]=1);var Zj=Qi(11,vb("")),ak=!1,bk,ck=!1;ck=!0;
bk=ck;var dk,ek=!1;dk=ek;Sj.wg=Ri(3,"www.googletagmanager.com");var fk=""+Sj.wg+(bk?"/gtag/js":"/gtm.js"),gk=null,hk=null,ik={},jk={};Sj.Pm=Qi(2,vb(""));var kk="";Sj.Ji=kk;
var Oj=new function(){this.R=new Nj;this.C=this.N=!1;this.H=0;this.Ba=this.Sa=this.qb=this.P="";this.ba=this.ka=!1};function lk(){var a;a=a===void 0?[]:a;return Pj(a).join("~")}function mk(){var a=Oj.P.length;return Oj.P[a-1]==="/"?Oj.P.substring(0,a-1):Oj.P}function nk(){return Oj.C?F(84)?Oj.H===0:Oj.H!==1:!1}function ok(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var pk=new qb,qk={},rk={},uk={name:Vj,set:function(a,b){ld(Gb(a,b),qk);sk()},get:function(a){return tk(a,2)},reset:function(){pk=new qb;qk={};sk()}};function tk(a,b){return b!=2?pk.get(a):vk(a)}function vk(a,b){var c=a.split(".");b=b||[];for(var d=qk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function wk(a,b){rk.hasOwnProperty(a)||(pk.set(a,b),ld(Gb(a,b),qk),sk())}
function xk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=tk(c,1);if(Array.isArray(d)||kd(d))d=ld(d,null);rk[c]=d}}function sk(a){sb(rk,function(b,c){pk.set(b,c);ld(Gb(b),qk);ld(Gb(b,c),qk);a&&delete rk[b]})}function yk(a,b){var c,d=(b===void 0?2:b)!==1?vk(a):pk.get(a);id(d)==="array"||id(d)==="object"?c=ld(d,null):c=d;return c};
var Ak=function(a){for(var b=[],c=Object.keys(zk),d=0;d<c.length;d++){var e=c[d],f=zk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},Bk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},Ck=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!Eb(w,"#")&&!Eb(w,".")}),h=0;h<g.length;h++){var m=g[h];if(Eb(m,"dataLayer."))f=tk(m.substring(10));
else{var n=m.split(".");f=x[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&ui)try{var q=ti(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Nc(q[r])||xb(q[r].value));f=f.length===1?f[0]:f}}catch(w){N(149)}if(F(60)){for(var t,u=0;u<g.length&&(t=tk(g[u]),t===void 0);u++);var v=f!==void 0;d[b]=Bk(t!==void 0,v);v||(f=t)}return f?(a[b]=f,!0):!1},Dk=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=Ck(c,"email",
a.email,b)||d;d=Ck(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=Ck(g,"first_name",e[f].first_name,b)||d;d=Ck(g,"last_name",e[f].last_name,b)||d;d=Ck(g,"street",e[f].street,b)||d;d=Ck(g,"city",e[f].city,b)||d;d=Ck(g,"region",e[f].region,b)||d;d=Ck(g,"country",e[f].country,b)||d;d=Ck(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},Ek=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&kd(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=x.enhanced_conversion_data;d&&db("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return Dk(a[K.m.mk])}},Fk=function(a){return kd(a)?!!a.enable_code:!1},zk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Gk=function(){return tc.userAgent.toLowerCase().indexOf("firefox")!==-1},Hk=function(a){var b=a&&a[K.m.mk];return b&&!!b[K.m.yn]};var Ik=/:[0-9]+$/,Jk=/^\d+\.fls\.doubleclick\.net$/;function Kk(a,b,c,d){var e=Lk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Lk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=ua(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Mk(a){try{return decodeURIComponent(a)}catch(b){}}function Nk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Ok(a.protocol)||Ok(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Ik,"").toLowerCase());return Pk(a,b,c,d,e)}
function Pk(a,b,c,d,e){var f,g=Ok(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Qk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Ik,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||db("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Kk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Ok(a){return a?a.replace(":","").toLowerCase():""}function Qk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Rk={},Sk=0;
function Tk(a){var b=Rk[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||db("TAGGING",1),d="/"+d);var e=c.hostname.replace(Ik,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Sk<5&&(Rk[a]=b,Sk++)}return b}function Uk(a,b,c){var d=Tk(a);return Mb(b,d,c)}
function Vk(a){var b=Tk(x.location.href),c=Nk(b,"host",!1);if(c&&c.match(Jk)){var d=Nk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Wk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Xk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Yk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Tk(""+c+b).href}}function Zk(a,b){if(nk()||Oj.N)return Yk(a,b)}
function $k(){return!!Sj.Ji&&Sj.Ji.split("@@").join("")!=="SGTM_TOKEN"}function al(a){for(var b=l([K.m.md,K.m.vc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function bl(a,b,c){c=c===void 0?"":c;if(!nk())return a;var d=b?Wk[a]||"":"";d==="/gs"&&(c="");return""+mk()+d+c}function cl(a){if(!nk())return a;for(var b=l(Xk),c=b.next();!c.done;c=b.next())if(Eb(a,""+mk()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function dl(a){var b=String(a[ff.Ra]||"").replace(/_/g,"");return Eb(b,"cvt")?"cvt":b}var el=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var fl={qq:Ti(27,Number("0.005000")),Yo:Ti(42,Number("0.010000"))},gl=Math.random(),hl=el||gl<Number(fl.qq),il=el||gl>=1-Number(fl.Yo);var jl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},kl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var ll,ml;a:{for(var nl=["CLOSURE_FLAGS"],ol=Aa,pl=0;pl<nl.length;pl++)if(ol=ol[nl[pl]],ol==null){ml=null;break a}ml=ol}var ql=ml&&ml[610401301];ll=ql!=null?ql:!1;function rl(){var a=Aa.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var sl,tl=Aa.navigator;sl=tl?tl.userAgentData||null:null;function ul(a){if(!ll||!sl)return!1;for(var b=0;b<sl.brands.length;b++){var c=sl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function vl(a){return rl().indexOf(a)!=-1};function wl(){return ll?!!sl&&sl.brands.length>0:!1}function xl(){return wl()?!1:vl("Opera")}function yl(){return vl("Firefox")||vl("FxiOS")}function zl(){return wl()?ul("Chromium"):(vl("Chrome")||vl("CriOS"))&&!(wl()?0:vl("Edge"))||vl("Silk")};var Al=function(a){Al[" "](a);return a};Al[" "]=function(){};var Bl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function Cl(){return ll?!!sl&&!!sl.platform:!1}function Dl(){return vl("iPhone")&&!vl("iPod")&&!vl("iPad")}function El(){Dl()||vl("iPad")||vl("iPod")};xl();wl()||vl("Trident")||vl("MSIE");vl("Edge");!vl("Gecko")||rl().toLowerCase().indexOf("webkit")!=-1&&!vl("Edge")||vl("Trident")||vl("MSIE")||vl("Edge");rl().toLowerCase().indexOf("webkit")!=-1&&!vl("Edge")&&vl("Mobile");Cl()||vl("Macintosh");Cl()||vl("Windows");(Cl()?sl.platform==="Linux":vl("Linux"))||Cl()||vl("CrOS");Cl()||vl("Android");Dl();vl("iPad");vl("iPod");El();rl().toLowerCase().indexOf("kaios");var Fl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Al(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Gl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Hl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Il=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Fl(b.top)?1:2},Jl=function(a){a=a===void 0?document:a;return a.createElement("img")},Kl=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Fl(a)&&(b=a);return b};function Ll(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Ml(){return Ll("join-ad-interest-group")&&jb(tc.joinAdInterestGroup)}
function Nl(a,b,c){var d=Ja[3]===void 0?1:Ja[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ja[2]===void 0?50:Ja[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&zb()-q<(Ja[1]===void 0?6E4:Ja[1])?(db("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ol(f[0]);else{if(n)return db("TAGGING",10),!1}else f.length>=d?Ol(f[0]):n&&Ol(m[0]);Hc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:zb()});return!0}function Ol(a){try{a.parentNode.removeChild(a)}catch(b){}};function Pl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Ql=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};yl();Dl()||vl("iPod");vl("iPad");!vl("Android")||zl()||yl()||xl()||vl("Silk");zl();!vl("Safari")||zl()||(wl()?0:vl("Coast"))||xl()||(wl()?0:vl("Edge"))||(wl()?ul("Microsoft Edge"):vl("Edg/"))||(wl()?ul("Opera"):vl("OPR"))||yl()||vl("Silk")||vl("Android")||El();var Rl={},Sl=null,Tl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Sl){Sl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Rl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Sl[q]===void 0&&(Sl[q]=p)}}}for(var r=Rl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],C=b[v+2],E=r[y>>2],G=r[(y&3)<<4|A>>4],I=r[(A&15)<<2|C>>6],M=r[C&63];t[w++]=""+E+G+I+M}var T=0,ca=u;switch(b.length-v){case 2:T=b[v+1],ca=r[(T&15)<<2]||u;case 1:var P=b[v];t[w]=""+r[P>>2]+r[(P&3)<<4|T>>4]+ca+u}return t.join("")};var Ul=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Vl=/#|$/,Wl=function(a,b){var c=a.search(Vl),d=Ul(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Bl(a.slice(d,e!==-1?e:0))},Xl=/[?&]($|#)/,Yl=function(a,b,c){for(var d,e=a.search(Vl),f=0,g,h=[];(g=Ul(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Xl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Zl(a,b,c,d,e,f,g){var h=Wl(c,"fmt");if(d){var m=Wl(c,"random"),n=Wl(c,"label")||"";if(!m)return!1;var p=Tl(Bl(n)+":"+Bl(m));if(!Pl(a,p,d))return!1}h&&Number(h)!==4&&(c=Yl(c,"rfmt",h));var q=Yl(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||g.H();Fc(q,function(){g==null||g.C();a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||g.C();e==null||e()},f,r||void 0);return!0};var $l={},am=($l[1]={},$l[2]={},$l[3]={},$l[4]={},$l);function bm(a,b,c){var d=cm(b,c);if(d){var e=am[b][d];e||(e=am[b][d]=[]);e.push(Object.assign({},a))}}function dm(a,b){var c=cm(a,b);if(c){var d=am[a][c];d&&(am[a][c]=d.filter(function(e){return!e.ym}))}}function em(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function cm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function fm(a){var b=za.apply(1,arguments);il&&(bm(a,2,b[0]),bm(a,3,b[0]));Qc.apply(null,va(b))}function gm(a){var b=za.apply(1,arguments);il&&bm(a,2,b[0]);return Rc.apply(null,va(b))}function hm(a){var b=za.apply(1,arguments);il&&bm(a,3,b[0]);Ic.apply(null,va(b))}
function im(a){var b=za.apply(1,arguments),c=b[0];il&&(bm(a,2,c),bm(a,3,c));return Uc.apply(null,va(b))}function jm(a){var b=za.apply(1,arguments);il&&bm(a,1,b[0]);Fc.apply(null,va(b))}function km(a){var b=za.apply(1,arguments);b[0]&&il&&bm(a,4,b[0]);Hc.apply(null,va(b))}function lm(a){var b=za.apply(1,arguments);il&&bm(a,1,b[2]);return Zl.apply(null,va(b))}function mm(a){var b=za.apply(1,arguments);il&&bm(a,4,b[0]);Nl.apply(null,va(b))};var nm=/gtag[.\/]js/,om=/gtm[.\/]js/,pm=!1;function qm(a){if(pm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(nm.test(c))return"3";if(om.test(c))return"2"}return"0"};function rm(a,b,c){var d=sm(),e=tm().container[a];e&&e.state!==3||(tm().container[a]={state:1,context:b,parent:d},um({ctid:a,isDestination:!1},c))}function um(a,b){var c=tm();c.pending||(c.pending=[]);nb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function vm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var wm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=vm()};function tm(){var a=xc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new wm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=vm());return c};var xm={},jg={ctid:Ri(5,"AW-2121225687"),canonicalContainerId:Ri(6,""),qm:Ri(10,"AW-2121225687"),rm:Ri(9,"AW-2121225687")};xm.pe=Qi(7,vb(""));function ym(){return xm.pe&&zm().some(function(a){return a===jg.ctid})}function Am(){return jg.canonicalContainerId||"_"+jg.ctid}function Bm(){return jg.qm?jg.qm.split("|"):[jg.ctid]}
function zm(){return jg.rm?jg.rm.split("|").filter(function(a){return F(108)?a.indexOf("GTM-")!==0:!0}):[]}function Cm(){var a=Dm(sm()),b=a&&a.parent;if(b)return Dm(b)}function Em(){var a=Dm(sm());if(a){for(;a.parent;){var b=Dm(a.parent);if(!b)break;a=b}return a}}function Dm(a){var b=tm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Fm(){var a=tm();if(a.pending){for(var b,c=[],d=!1,e=Bm(),f=zm(),g={},h=0;h<a.pending.length;g={kg:void 0},h++)g.kg=a.pending[h],nb(g.kg.target.isDestination?f:e,function(m){return function(n){return n===m.kg.target.ctid}}(g))?d||(b=g.kg.onLoad,d=!0):c.push(g.kg);a.pending=c;if(b)try{b(Am())}catch(m){}}}
function Gm(){for(var a=jg.ctid,b=Bm(),c=zm(),d=function(n,p){var q={canonicalContainerId:jg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};vc&&(q.scriptElement=vc);wc&&(q.scriptSource=wc);if(Cm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Oj.C,y=Tk(v),A=w?y.pathname:""+y.hostname+y.pathname,C=z.scripts,E="",G=0;G<C.length;++G){var I=C[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(A)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}E=String(G)}}if(E){t=E;break b}}t=void 0}var M=t;if(M){pm=!0;r=M;break a}}var T=[].slice.call(z.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=qm(q)}var ca=p?e.destination:e.container,P=ca[n];P?(p&&P.state===0&&N(93),Object.assign(P,q)):ca[n]=q},e=tm(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Am()]={};Fm()}function Hm(){var a=Am();return!!tm().canonical[a]}function Im(a){return!!tm().container[a]}function Jm(a){var b=tm().destination[a];return!!b&&!!b.state}function sm(){return{ctid:jg.ctid,isDestination:xm.pe}}function Km(){var a=tm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Lm(){var a={};sb(tm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Mm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Nm(){for(var a=tm(),b=l(Bm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Om={Ia:{je:0,oe:1,Fi:2}};Om.Ia[Om.Ia.je]="FULL_TRANSMISSION";Om.Ia[Om.Ia.oe]="LIMITED_TRANSMISSION";Om.Ia[Om.Ia.Fi]="NO_TRANSMISSION";var Pm={X:{Fb:0,Da:1,Fc:2,Oc:3}};Pm.X[Pm.X.Fb]="NO_QUEUE";Pm.X[Pm.X.Da]="ADS";Pm.X[Pm.X.Fc]="ANALYTICS";Pm.X[Pm.X.Oc]="MONITORING";function Qm(){var a=xc("google_tag_data",{});return a.ics=a.ics||new Rm}var Rm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Rm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;db("TAGGING",19);b==null?db("TAGGING",18):Sm(this,a,b==="granted",c,d,e,f,g)};Rm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Sm(this,a[d],void 0,void 0,"","",b,c)};
var Sm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&kb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(db("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Rm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Tm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Tm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&kb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,zd:b})};var Tm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.sm=!0)}};Rm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.sm){d.sm=!1;try{d.zd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Um=!1,Vm=!1,Wm={},Xm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Wm.ad_storage=1,Wm.analytics_storage=1,Wm.ad_user_data=1,Wm.ad_personalization=1,Wm),usedContainerScopedDefaults:!1};function Ym(a){var b=Qm();b.accessedAny=!0;return(kb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Xm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Zm(a){var b=Qm();b.accessedAny=!0;return b.getConsentState(a,Xm)}function $m(a){var b=Qm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function an(){if(!Ka(7))return!1;var a=Qm();a.accessedAny=!0;if(a.active)return!0;if(!Xm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Xm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Xm.containerScopedDefaults[c.value]!==1)return!0;return!1}function bn(a,b){Qm().addListener(a,b)}
function cn(a,b){Qm().notifyListeners(a,b)}function dn(a,b){function c(){for(var e=0;e<b.length;e++)if(!$m(b[e]))return!0;return!1}if(c()){var d=!1;bn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function en(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];Ym(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=kb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),bn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var fn={},gn=(fn[Pm.X.Fb]=Om.Ia.je,fn[Pm.X.Da]=Om.Ia.je,fn[Pm.X.Fc]=Om.Ia.je,fn[Pm.X.Oc]=Om.Ia.je,fn),hn=function(a,b){this.C=a;this.consentTypes=b};hn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Ym(a)});case 1:return this.consentTypes.some(function(a){return Ym(a)});default:lc(this.C,"consentsRequired had an unknown type")}};
var jn={},kn=(jn[Pm.X.Fb]=new hn(0,[]),jn[Pm.X.Da]=new hn(0,["ad_storage"]),jn[Pm.X.Fc]=new hn(0,["analytics_storage"]),jn[Pm.X.Oc]=new hn(1,["ad_storage","analytics_storage"]),jn);var mn=function(a){var b=this;this.type=a;this.C=[];bn(kn[a].consentTypes,function(){ln(b)||b.flush()})};mn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var ln=function(a){return gn[a.type]===Om.Ia.Fi&&!kn[a.type].isConsentGranted()},nn=function(a,b){ln(a)?a.C.push(b):b()},on=new Map;function pn(a){on.has(a)||on.set(a,new mn(a));return on.get(a)};var qn={Z:{Mm:"aw_user_data_cache",Lh:"cookie_deprecation_label",xg:"diagnostics_page_id",Wn:"fl_user_data_cache",Yn:"ga4_user_data_cache",Ff:"ip_geo_data_cache",Ai:"ip_geo_fetch_in_progress",pl:"nb_data",po:"page_experiment_ids",Of:"pt_data",rl:"pt_listener_set",zl:"service_worker_endpoint",Bl:"shared_user_id",Cl:"shared_user_id_requested",kh:"shared_user_id_source"}};var rn=function(a){return Ze(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(qn.Z);
function sn(a,b){b=b===void 0?!1:b;if(rn(a)){var c,d,e=(d=(c=xc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function tn(a,b){var c=sn(a,!0);c&&c.set(b)}function un(a){var b;return(b=sn(a))==null?void 0:b.get()}function vn(a){var b={},c=sn(a);if(!c){c=sn(a,!0);if(!c)return;c.set(b)}return c.get()}function wn(a,b){if(typeof b==="function"){var c;return(c=sn(a,!0))==null?void 0:c.subscribe(b)}}function xn(a,b){var c=sn(a);return c?c.unsubscribe(b):!1};var yn="https://"+Ri(21,"www.googletagmanager.com"),zn="/td?id="+jg.ctid,An={},Bn=(An.tdp=1,An.exp=1,An.pid=1,An.dl=1,An.seq=1,An.t=1,An.v=1,An),Cn=["mcc"],Dn={},En={},Fn=!1,Gn=void 0;function Hn(a,b,c){En[a]=b;(c===void 0||c)&&In(a)}function In(a,b){Dn[a]!==void 0&&(b===void 0||!b)||Eb(jg.ctid,"GTM-")&&a==="mcc"||(Dn[a]=!0)}
function Jn(a){a=a===void 0?!1:a;var b=Object.keys(Dn).filter(function(c){return Dn[c]===!0&&En[c]!==void 0&&(a||!Cn.includes(c))}).map(function(c){var d=En[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+bl(yn)+zn+(""+b+"&z=0")}function Kn(){Object.keys(Dn).forEach(function(a){Bn[a]||(Dn[a]=!1)})}
function Ln(a){a=a===void 0?!1:a;if(Oj.ba&&il&&jg.ctid){var b=pn(Pm.X.Oc);if(ln(b))Fn||(Fn=!0,nn(b,Ln));else{var c=Jn(a),d={destinationId:jg.ctid,endpoint:61};a?im(d,c,void 0,{Dh:!0},void 0,function(){hm(d,c+"&img=1")}):hm(d,c);Kn();Fn=!1}}}var Mn={};
function Nn(a){var b=String(a);Mn.hasOwnProperty(b)||(Mn[b]=!0,Hn("csp",Object.keys(Mn).join("~")),In("csp",!0),Gn===void 0&&F(171)&&(Gn=x.setTimeout(function(){var c=Dn.csp;Dn.csp=!0;Dn.seq=!1;var d=Jn(!1);Dn.csp=c;Dn.seq=!0;Fc(d+"&script=1");Gn=void 0},500)))}function On(){Object.keys(Dn).filter(function(a){return Dn[a]&&!Bn[a]}).length>0&&Ln(!0)}var Pn;
function Qn(){if(un(qn.Z.xg)===void 0){var a=function(){tn(qn.Z.xg,ob());Pn=0};a();x.setInterval(a,864E5)}else wn(qn.Z.xg,function(){Pn=0});Pn=0}function Rn(){Qn();Hn("v","3");Hn("t","t");Hn("pid",function(){return String(un(qn.Z.xg))});Hn("seq",function(){return String(++Pn)});Hn("exp",lk());Kc(x,"pagehide",On)};var Sn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Tn=[K.m.md,K.m.vc,K.m.Zd,K.m.Qb,K.m.uc,K.m.Qa,K.m.Pa,K.m.cb,K.m.nb,K.m.Sb],Un=!1,Vn=!1,Wn={},Xn={};function Yn(){!Vn&&Un&&(Sn.some(function(a){return Xm.containerScopedDefaults[a]!==1})||Zn("mbc"));Vn=!0}function Zn(a){il&&(Hn(a,"1"),Ln())}function $n(a,b){if(!Wn[b]&&(Wn[b]=!0,Xn[b]))for(var c=l(Tn),d=c.next();!d.done;d=c.next())if(O(a,d.value)){Zn("erc");break}};function ao(a){db("HEALTH",a)};var bo={rp:Ri(22,"eyIwIjoiVVMiLCIxIjoiVVMtTUkiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9")},co={},eo=!1;function fo(){function a(){c!==void 0&&xn(qn.Z.Ff,c);try{var e=un(qn.Z.Ff);co=JSON.parse(e)}catch(f){N(123),ao(2),co={}}eo=!0;b()}var b=go,c=void 0,d=un(qn.Z.Ff);d?a(d):(c=wn(qn.Z.Ff,a),ho())}
function ho(){function a(c){tn(qn.Z.Ff,c||"{}");tn(qn.Z.Ai,!1)}if(!un(qn.Z.Ai)){tn(qn.Z.Ai,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function io(){var a=bo.rp;try{return JSON.parse(bb(a))}catch(b){return N(123),ao(2),{}}}function jo(){return co["0"]||""}function ko(){return co["1"]||""}function lo(){var a=!1;return a}function mo(){return co["6"]!==!1}function no(){var a="";return a}
function oo(){var a=!1;a=!!co["5"];return a}function po(){var a="";return a};var qo={},ro=Object.freeze((qo[K.m.Ea]=1,qo[K.m.zg]=1,qo[K.m.Ag]=1,qo[K.m.Ob]=1,qo[K.m.sa]=1,qo[K.m.nb]=1,qo[K.m.ob]=1,qo[K.m.yb]=1,qo[K.m.ed]=1,qo[K.m.Sb]=1,qo[K.m.cb]=1,qo[K.m.Hc]=1,qo[K.m.af]=1,qo[K.m.oa]=1,qo[K.m.jk]=1,qo[K.m.df]=1,qo[K.m.Kg]=1,qo[K.m.Lg]=1,qo[K.m.Zd]=1,qo[K.m.zk]=1,qo[K.m.rc]=1,qo[K.m.ce]=1,qo[K.m.Bk]=1,qo[K.m.Og]=1,qo[K.m.ei]=1,qo[K.m.Kc]=1,qo[K.m.Lc]=1,qo[K.m.Pa]=1,qo[K.m.fi]=1,qo[K.m.Vb]=1,qo[K.m.pb]=1,qo[K.m.ld]=1,qo[K.m.md]=1,qo[K.m.qf]=1,qo[K.m.hi]=1,qo[K.m.tf]=1,qo[K.m.vc]=
1,qo[K.m.od]=1,qo[K.m.Vg]=1,qo[K.m.Wb]=1,qo[K.m.rd]=1,qo[K.m.Ii]=1,qo));Object.freeze([K.m.Aa,K.m.Wa,K.m.Db,K.m.zb,K.m.gi,K.m.Qa,K.m.ai,K.m.zn]);
var so={},to=Object.freeze((so[K.m.bn]=1,so[K.m.dn]=1,so[K.m.fn]=1,so[K.m.gn]=1,so[K.m.hn]=1,so[K.m.ln]=1,so[K.m.mn]=1,so[K.m.nn]=1,so[K.m.pn]=1,so[K.m.Td]=1,so)),uo={},vo=Object.freeze((uo[K.m.Yj]=1,uo[K.m.Zj]=1,uo[K.m.Pd]=1,uo[K.m.Qd]=1,uo[K.m.bk]=1,uo[K.m.Xc]=1,uo[K.m.Rd]=1,uo[K.m.jc]=1,uo[K.m.Gc]=1,uo[K.m.kc]=1,uo[K.m.kb]=1,uo[K.m.Sd]=1,uo[K.m.xb]=1,uo[K.m.dk]=1,uo)),wo=Object.freeze([K.m.Ea,K.m.Qe,K.m.Ob,K.m.Hc,K.m.Zd,K.m.kf,K.m.pb,K.m.od]),xo=Object.freeze([].concat(va(wo))),yo=Object.freeze([K.m.ob,
K.m.Lg,K.m.qf,K.m.hi,K.m.Hg]),zo=Object.freeze([].concat(va(yo))),Ao={},Bo=(Ao[K.m.U]="1",Ao[K.m.ja]="2",Ao[K.m.V]="3",Ao[K.m.La]="4",Ao),Co={},Do=Object.freeze((Co.search="s",Co.youtube="y",Co.playstore="p",Co.shopping="h",Co.ads="a",Co.maps="m",Co));function Eo(a){return typeof a!=="object"||a===null?{}:a}function Fo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Go(a){if(a!==void 0&&a!==null)return Fo(a)}function Ho(a){return typeof a==="number"?a:Go(a)};function Io(a){return a&&a.indexOf("pending:")===0?Jo(a.substr(8)):!1}function Jo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=zb();return b<c+3E5&&b>c-9E5};var Ko=!1,Lo=!1,Mo=!1,No=0,Oo=!1,Po=[];function Qo(a){if(No===0)Oo&&Po&&(Po.length>=100&&Po.shift(),Po.push(a));else if(Ro()){var b=Ri(41,'google.tagmanager.ta.prodqueue'),c=xc(b,[]);c.length>=50&&c.shift();c.push(a)}}function So(){To();Lc(z,"TAProdDebugSignal",So)}function To(){if(!Lo){Lo=!0;Uo();var a=Po;Po=void 0;a==null||a.forEach(function(b){Qo(b)})}}
function Uo(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Jo(a)?No=1:!Io(a)||Ko||Mo?No=2:(Mo=!0,Kc(z,"TAProdDebugSignal",So,!1),x.setTimeout(function(){To();Ko=!0},200))}function Ro(){if(!Oo)return!1;switch(No){case 1:case 0:return!0;case 2:return!1;default:return!1}};var Vo=!1;function Wo(a,b){var c=Bm(),d=zm();if(Ro()){var e=Xo("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Qo(e)}}
function Yo(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ya;e=a.isBatched;var f;if(f=Ro()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=Xo("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Qo(h)}}function Zo(a){Ro()&&Yo(a())}
function Xo(a,b){b=b===void 0?{}:b;b.groupId=$o;var c,d=b,e={publicId:ap};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=Vo?"OGT":"GTM";c.key.targetRef=bp;return c}var ap="",bp={ctid:"",isDestination:!1},$o;
function cp(a){var b=jg.ctid,c=ym();No=0;Oo=!0;Uo();$o=a;ap=b;Vo=bk;bp={ctid:b,isDestination:c}};var dp=[K.m.U,K.m.ja,K.m.V,K.m.La],ep,fp;function gp(a){var b=a[K.m.fc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)sb(a,function(d){return function(e,f){if(e!==K.m.fc){var g=Fo(f),h=b[d.cg],m=jo(),n=ko();Vm=!0;Um&&db("TAGGING",20);Qm().declare(e,g,h,m,n)}}}(c))}
function hp(a){Yn();!fp&&ep&&Zn("crc");fp=!0;var b=a[K.m.rg];b&&N(41);var c=a[K.m.fc];c?N(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)sb(a,function(e){return function(f,g){if(f!==K.m.fc&&f!==K.m.rg){var h=Go(g),m=c[e.dg],n=Number(b),p=jo(),q=ko();n=n===void 0?0:n;Um=!0;Vm&&db("TAGGING",20);Qm().default(f,h,m,p,q,n,Xm)}}}(d))}
function ip(a){Xm.usedContainerScopedDefaults=!0;var b=a[K.m.fc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ko())&&!c.includes(jo()))return}sb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Xm.usedContainerScopedDefaults=!0;Xm.containerScopedDefaults[d]=e==="granted"?3:2})}
function jp(a,b){Yn();ep=!0;sb(a,function(c,d){var e=Fo(d);Um=!0;Vm&&db("TAGGING",20);Qm().update(c,e,Xm)});cn(b.eventId,b.priorityId)}function kp(a){a.hasOwnProperty("all")&&(Xm.selectedAllCorePlatformServices=!0,sb(Do,function(b){Xm.corePlatformServices[b]=a.all==="granted";Xm.usedCorePlatformServices=!0}));sb(a,function(b,c){b!=="all"&&(Xm.corePlatformServices[b]=c==="granted",Xm.usedCorePlatformServices=!0)})}function Q(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Ym(b)})}
function lp(a,b){bn(a,b)}function mp(a,b){en(a,b)}function np(a,b){dn(a,b)}function op(){var a=[K.m.U,K.m.La,K.m.V];Qm().waitForUpdate(a,500,Xm)}function pp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Qm().clearTimeout(d,void 0,Xm)}cn()}function qp(){if(!dk)for(var a=mo()?ok(Oj.Sa):ok(Oj.qb),b=0;b<dp.length;b++){var c=dp[b],d=c,e=a[c]?"granted":"denied";Qm().implicit(d,e)}};var rp=!1,sp=[];function tp(){if(!rp){rp=!0;for(var a=sp.length-1;a>=0;a--)sp[a]();sp=[]}};var up=x.google_tag_manager=x.google_tag_manager||{};function vp(a,b){return up[a]=up[a]||b()}function wp(){var a=jg.ctid,b=xp;up[a]=up[a]||b}function yp(){var a=up.sequence||1;up.sequence=a+1;return a};function zp(){if(up.pscdl!==void 0)un(qn.Z.Lh)===void 0&&tn(qn.Z.Lh,up.pscdl);else{var a=function(c){up.pscdl=c;tn(qn.Z.Lh,c)},b=function(){a("error")};try{tc.cookieDeprecationLabel?(a("pending"),tc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Ap=0;function Bp(a){il&&a===void 0&&Ap===0&&(Hn("mcc","1"),Ap=1)};var Cp={Df:{Qm:"cd",Rm:"ce",Sm:"cf",Tm:"cpf",Um:"cu"}};var Dp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Ep=/\s/;
function Fp(a,b){if(kb(a)){a=xb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Dp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Ep.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Gp(a,b){for(var c={},d=0;d<a.length;++d){var e=Fp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Hp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Ip={},Hp=(Ip[0]=0,Ip[1]=1,Ip[2]=2,Ip[3]=0,Ip[4]=1,Ip[5]=0,Ip[6]=0,Ip[7]=0,Ip);var Jp=Number('')||500,Kp={},Lp={},Mp={initialized:11,complete:12,interactive:13},Np={},Op=Object.freeze((Np[K.m.pb]=!0,Np)),Pp=void 0;function Qp(a,b){if(b.length&&il){var c;(c=Kp)[a]!=null||(c[a]=[]);Lp[a]!=null||(Lp[a]=[]);var d=b.filter(function(e){return!Lp[a].includes(e)});Kp[a].push.apply(Kp[a],va(d));Lp[a].push.apply(Lp[a],va(d));!Pp&&d.length>0&&(In("tdc",!0),Pp=x.setTimeout(function(){Ln();Kp={};Pp=void 0},Jp))}}
function Rp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Sp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;id(t)==="object"?u=t[r]:id(t)==="array"&&(u=t[r]);return u===void 0?Op[r]:u},f=Rp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=id(m)==="object"||id(m)==="array",q=id(n)==="object"||id(n)==="array";if(p&&q)Sp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Tp(){Hn("tdc",function(){Pp&&(x.clearTimeout(Pp),Pp=void 0);var a=[],b;for(b in Kp)Kp.hasOwnProperty(b)&&a.push(b+"*"+Kp[b].join("."));return a.length?a.join("!"):void 0},!1)};var Up=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Vp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},O=function(a,b,c,d){for(var e=l(Vp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},Wp=function(a){for(var b={},c=Vp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Up.prototype.getMergedValues=function(a,b,c){function d(n){kd(n)&&sb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Vp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var Xp=function(a){for(var b=[K.m.Ve,K.m.Re,K.m.Se,K.m.Te,K.m.Ue,K.m.We,K.m.Xe],c=Vp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},Yp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.ba={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},Zp=function(a,
b){a.H=b;return a},$p=function(a,b){a.R=b;return a},aq=function(a,b){a.C=b;return a},bq=function(a,b){a.N=b;return a},cq=function(a,b){a.ba=b;return a},dq=function(a,b){a.P=b;return a},eq=function(a,b){a.eventMetadata=b||{};return a},fq=function(a,b){a.onSuccess=b;return a},gq=function(a,b){a.onFailure=b;return a},hq=function(a,b){a.isGtmEvent=b;return a},iq=function(a){return new Up(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var R={A:{Ij:"accept_by_default",qg:"add_tag_timing",Hh:"allow_ad_personalization",Kj:"batch_on_navigation",Mj:"client_id_source",He:"consent_event_id",Ie:"consent_priority_id",Nq:"consent_state",fa:"consent_updated",Wc:"conversion_linker_enabled",ya:"cookie_options",tg:"create_dc_join",ug:"create_fpm_geo_join",vg:"create_fpm_signals_join",Od:"create_google_join",Ke:"em_event",Qq:"endpoint_for_debug",Xj:"enhanced_client_id_source",Nh:"enhanced_match_result",ie:"euid_mode_enabled",fb:"event_start_timestamp_ms",
Vk:"event_usage",Xg:"extra_tag_experiment_ids",Xq:"add_parameter",ui:"attribution_reporting_experiment",wi:"counting_method",Yg:"send_as_iframe",Yq:"parameter_order",Zg:"parsed_target",Xn:"ga4_collection_subdomain",Yk:"gbraid_cookie_marked",ia:"hit_type",sd:"hit_type_override",co:"is_config_command",Gf:"is_consent_update",Hf:"is_conversion",fl:"is_ecommerce",ud:"is_external_event",Bi:"is_fallback_aw_conversion_ping_allowed",If:"is_first_visit",il:"is_first_visit_conversion",ah:"is_fl_fallback_conversion_flow_allowed",
ke:"is_fpm_encryption",bh:"is_fpm_split",me:"is_gcp_conversion",jl:"is_google_signals_allowed",vd:"is_merchant_center",eh:"is_new_to_site",fh:"is_server_side_destination",ne:"is_session_start",ml:"is_session_start_conversion",er:"is_sgtm_ga_ads_conversion_study_control_group",gr:"is_sgtm_prehit",nl:"is_sgtm_service_worker",Ci:"is_split_conversion",eo:"is_syn",Jf:"join_id",Di:"join_elapsed",Kf:"join_timer_sec",qe:"tunnel_updated",kr:"prehit_for_retry",mr:"promises",nr:"record_aw_latency",yc:"redact_ads_data",
se:"redact_click_ids",qo:"remarketing_only",xl:"send_ccm_parallel_ping",jh:"send_fledge_experiment",rr:"send_ccm_parallel_test_ping",Pf:"send_to_destinations",Hi:"send_to_targets",yl:"send_user_data_hit",hb:"source_canonical_id",Ha:"speculative",Dl:"speculative_in_message",El:"suppress_script_load",Fl:"syn_or_mod",Jl:"transient_ecsid",Qf:"transmission_type",ib:"user_data",wr:"user_data_from_automatic",xr:"user_data_from_automatic_getter",ue:"user_data_from_code",nh:"user_data_from_manual",Ll:"user_data_mode",
Rf:"user_id_updated"}};var jq={Lm:Number("5"),Or:Number("")},kq=[],lq=!1;function mq(a){kq.push(a)}var nq="?id="+jg.ctid,oq=void 0,pq={},qq=void 0,rq=new function(){var a=5;jq.Lm>0&&(a=jq.Lm);this.H=a;this.C=0;this.N=[]},sq=1E3;
function tq(a,b){var c=oq;if(c===void 0)if(b)c=yp();else return"";for(var d=[bl("https://www.googletagmanager.com"),"/a",nq],e=l(kq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function uq(){if(Oj.ba&&(qq&&(x.clearTimeout(qq),qq=void 0),oq!==void 0&&vq)){var a=pn(Pm.X.Oc);if(ln(a))lq||(lq=!0,nn(a,uq));else{var b;if(!(b=pq[oq])){var c=rq;b=c.C<c.H?!1:zb()-c.N[c.C%c.H]<1E3}if(b||sq--<=0)N(1),pq[oq]=!0;else{var d=rq,e=d.C++%d.H;d.N[e]=zb();var f=tq(!0);hm({destinationId:jg.ctid,endpoint:56,eventId:oq},f);lq=vq=!1}}}}function wq(){if(hl&&Oj.ba){var a=tq(!0,!0);hm({destinationId:jg.ctid,endpoint:56,eventId:oq},a)}}var vq=!1;
function xq(a){pq[a]||(a!==oq&&(uq(),oq=a),vq=!0,qq||(qq=x.setTimeout(uq,500)),tq().length>=2022&&uq())}var yq=ob();function zq(){yq=ob()}function Aq(){return[["v","3"],["t","t"],["pid",String(yq)]]};var Bq={};function Cq(a,b,c){hl&&a!==void 0&&(Bq[a]=Bq[a]||[],Bq[a].push(c+b),xq(a))}function Dq(a){var b=a.eventId,c=a.Nd,d=[],e=Bq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Bq[b];return d};function Eq(a,b,c,d){var e=Fp(a,!0);e&&Fq.register(e,b,c,d)}function Gq(a,b,c,d){var e=Fp(c,d.isGtmEvent);e&&(ak&&(d.deferrable=!0),Fq.push("event",[b,a],e,d))}function Hq(a,b,c,d){var e=Fp(c,d.isGtmEvent);e&&Fq.push("get",[a,b],e,d)}function Iq(a){var b=Fp(a,!0),c;b?c=Jq(Fq,b).C:c={};return c}function Kq(a,b){var c=Fp(a,!0);c&&Lq(Fq,c,b)}
var Mq=function(){this.R={};this.C={};this.H={};this.ba=null;this.P={};this.N=!1;this.status=1},Nq=function(a,b,c,d){this.H=zb();this.C=b;this.args=c;this.messageContext=d;this.type=a},Oq=function(){this.destinations={};this.C={};this.commands=[]},Jq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Mq},Pq=function(a,b,c,d){if(d.C){var e=Jq(a,d.C),f=e.ba;if(f){var g=ld(c,null),h=ld(e.R[d.C.id],null),m=ld(e.P,null),n=ld(e.C,null),p=ld(a.C,null),q={};if(hl)try{q=
ld(qk,null)}catch(w){N(72)}var r=d.C.prefix,t=function(w){Cq(d.messageContext.eventId,r,w)},u=iq(hq(gq(fq(eq(cq(bq(dq(aq($p(Zp(new Yp(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Cq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(il&&w==="config"){var A,C=(A=Fp(y))==null?void 0:A.ids;if(!(C&&C.length>1)){var E,G=xc("google_tag_data",{});G.td||(G.td={});E=G.td;var I=ld(u.P);ld(u.C,I);var M=[],T;for(T in E)E.hasOwnProperty(T)&&Sp(E[T],I).length&&M.push(T);M.length&&(Qp(y,M),db("TAGGING",Mp[z.readyState]||14));E[y]=I}}f(d.C.id,b,d.H,u)}catch(ca){Cq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():nn(e.ka,v)}}};
Oq.prototype.register=function(a,b,c,d){var e=Jq(this,a);e.status!==3&&(e.ba=b,e.status=3,e.ka=pn(c),Lq(this,a,d||{}),this.flush())};
Oq.prototype.push=function(a,b,c,d){c!==void 0&&(Jq(this,c).status===1&&(Jq(this,c).status=2,this.push("require",[{}],c,{})),Jq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[R.A.Pf]||(d.eventMetadata[R.A.Pf]=[c.destinationId]),d.eventMetadata[R.A.Hi]||(d.eventMetadata[R.A.Hi]=[c.id]));this.commands.push(new Nq(a,c,b,d));d.deferrable||this.flush()};
Oq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,sh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Jq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Jq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];sb(h,function(t,u){ld(Gb(t,u),b.C)});Mj(h,!0);break;case "config":var m=Jq(this,g);
e.Qc={};sb(f.args[0],function(t){return function(u,v){ld(Gb(u,v),t.Qc)}}(e));var n=!!e.Qc[K.m.od];delete e.Qc[K.m.od];var p=g.destinationId===g.id;Mj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Pq(this,K.m.qa,e.Qc,f);m.N=!0;p?ld(e.Qc,m.P):(ld(e.Qc,m.R[g.id]),N(70));d=!0;break;case "event":e.sh={};sb(f.args[0],function(t){return function(u,v){ld(Gb(u,v),t.sh)}}(e));Mj(e.sh);Pq(this,f.args[1],e.sh,f);break;case "get":var q={},r=(q[K.m.qc]=f.args[0],q[K.m.Ic]=f.args[1],q);Pq(this,K.m.Cb,r,f)}this.commands.shift();
Qq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Qq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Jq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Lq=function(a,b,c){var d=ld(c,null);ld(Jq(a,b).C,d);Jq(a,b).C=d},Fq=new Oq;function Rq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Sq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Tq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Jl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=qc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Sq(e,"load",f);Sq(e,"error",f)};Rq(e,"load",f);Rq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Uq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Gl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Vq(c,b)}
function Vq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Tq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var Wq=function(){this.ba=this.ba;this.P=this.P};Wq.prototype.ba=!1;Wq.prototype.dispose=function(){this.ba||(this.ba=!0,this.N())};Wq.prototype[Symbol.dispose]=function(){this.dispose()};Wq.prototype.addOnDisposeCallback=function(a,b){this.ba?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};Wq.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function Xq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var Yq=function(a,b){b=b===void 0?{}:b;Wq.call(this);this.C=null;this.ka={};this.qb=0;this.R=null;this.H=a;var c;this.Sa=(c=b.timeoutMs)!=null?c:500;var d;this.Ba=(d=b.Dr)!=null?d:!1};sa(Yq,Wq);Yq.prototype.N=function(){this.ka={};this.R&&(Sq(this.H,"message",this.R),delete this.R);delete this.ka;delete this.H;delete this.C;Wq.prototype.N.call(this)};var $q=function(a){return typeof a.H.__tcfapi==="function"||Zq(a)!=null};
Yq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ba},d=kl(function(){return a(c)}),e=0;this.Sa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Sa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=Xq(c),c.internalBlockOnErrors=b.Ba,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{ar(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};Yq.prototype.removeEventListener=function(a){a&&a.listenerId&&ar(this,"removeEventListener",null,a.listenerId)};
var cr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=br(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&br(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?br(a.purpose.legitimateInterests,
b)&&br(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},br=function(a,b){return!(!a||!a[b])},ar=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(Zq(a)){dr(a);var g=++a.qb;a.ka[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},Zq=function(a){if(a.C)return a.C;a.C=Hl(a.H,"__tcfapiLocator");return a.C},dr=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ka[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Rq(a.H,"message",b)}},er=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=Xq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Uq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var fr={1:0,3:0,4:0,7:3,9:3,10:3};function gr(){return vp("tcf",function(){return{}})}var hr=function(){return new Yq(x,{timeoutMs:-1})};
function ir(){var a=gr(),b=hr();$q(b)&&!jr()&&!kr()&&N(124);if(!a.active&&$q(b)){jr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Qm().active=!0,a.tcString="tcunavailable");op();try{b.addEventListener(function(c){if(c.internalErrorState!==0)lr(a),pp([K.m.U,K.m.La,K.m.V]),Qm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,kr()&&(a.active=!0),!mr(c)||jr()||kr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in fr)fr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(mr(c)){var g={},h;for(h in fr)if(fr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={qp:!0};p=p===void 0?{}:p;m=er(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.qp)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?cr(n,"1",0):!0:!1;g["1"]=m}else g[h]=cr(c,h,fr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(pp([K.m.U,K.m.La,K.m.V]),Qm().active=!0):(r[K.m.La]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":pp([K.m.V]),jp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:nr()||""}))}}else pp([K.m.U,K.m.La,K.m.V])})}catch(c){lr(a),pp([K.m.U,K.m.La,K.m.V]),Qm().active=!0}}}
function lr(a){a.type="e";a.tcString="tcunavailable"}function mr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function jr(){return x.gtag_enable_tcf_support===!0}function kr(){return gr().enableAdvertiserConsentMode===!0}function nr(){var a=gr();if(a.active)return a.tcString}function or(){var a=gr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function pr(a){if(!fr.hasOwnProperty(String(a)))return!0;var b=gr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var qr=[K.m.U,K.m.ja,K.m.V,K.m.La],rr={},sr=(rr[K.m.U]=1,rr[K.m.ja]=2,rr);function tr(a){if(a===void 0)return 0;switch(O(a,K.m.Ea)){case void 0:return 1;case !1:return 3;default:return 2}}function ur(){return(F(183)?Xi.xp:Xi.yp).indexOf(ko())!==-1&&tc.globalPrivacyControl===!0}function vr(a){if(ur())return!1;var b=tr(a);if(b===3)return!1;switch(Zm(K.m.La)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function wr(){return an()||!Ym(K.m.U)||!Ym(K.m.ja)}function xr(){var a={},b;for(b in sr)sr.hasOwnProperty(b)&&(a[sr[b]]=Zm(b));return"G1"+bf(a[1]||0)+bf(a[2]||0)}var yr={},zr=(yr[K.m.U]=0,yr[K.m.ja]=1,yr[K.m.V]=2,yr[K.m.La]=3,yr);function Ar(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Br(a){for(var b="1",c=0;c<qr.length;c++){var d=b,e,f=qr[c],g=Xm.delegatedConsentTypes[f];e=g===void 0?0:zr.hasOwnProperty(g)?12|zr[g]:8;var h=Qm();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Ar(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Ar(m.declare)<<4|Ar(m.default)<<2|Ar(m.update)])}var n=b,p=(ur()?1:0)<<3,q=(an()?1:0)<<2,r=tr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Xm.containerScopedDefaults.ad_storage<<4|Xm.containerScopedDefaults.analytics_storage<<2|Xm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Xm.usedContainerScopedDefaults?1:0)<<2|Xm.containerScopedDefaults.ad_personalization]}
function Cr(){if(!Ym(K.m.V))return"-";for(var a=Object.keys(Do),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Xm.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Do[m])}(Xm.usedCorePlatformServices?Xm.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Dr(){return mo()||(jr()||kr())&&or()==="1"?"1":"0"}function Er(){return(mo()?!0:!(!jr()&&!kr())&&or()==="1")||!Ym(K.m.V)}
function Fr(){var a="0",b="0",c;var d=gr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=gr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;mo()&&(h|=1);or()==="1"&&(h|=2);jr()&&(h|=4);var m;var n=gr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Qm().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Gr(){return ko()==="US-CO"};var fg;function Hr(){var a=!1;return a}function Ir(){F(212)&&bk&&gg("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}})};var Jr;function Kr(){if(wc===null)return 0;var a=$c();if(!a)return 0;var b=a.getEntriesByName(wc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Lr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Mr(a){a=a===void 0?{}:a;var b=jg.ctid.split("-")[0].toUpperCase(),c={ctid:jg.ctid,yj:Uj,Cj:Tj,bm:xm.pe?2:1,Cq:a.Bm,ve:jg.canonicalContainerId};if(F(210)){var d;c.sq=(d=Em())==null?void 0:d.canonicalContainerId}if(F(204)){var e;c.Mo=(e=Jr)!=null?e:Jr=Kr()}c.ve!==a.Ma&&(c.Ma=a.Ma);var f=Cm();c.lm=f?f.canonicalContainerId:void 0;bk?(c.Uc=Lr[b],c.Uc||(c.Uc=0)):c.Uc=dk?13:10;Oj.C?(c.Sc=0,c.Pl=2):Oj.N?c.Sc=1:Hr()?c.Sc=2:c.Sc=3;var g={6:!1};Oj.H===2?g[7]=!0:Oj.H===1&&(g[2]=!0);if(wc){var h=Nk(Tk(wc),
"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Rl=g;return ef(c,a.ph)}
function Nr(){if(!F(192))return Mr();if(F(193))return ef({yj:Uj,Cj:Tj});var a=jg.ctid.split("-")[0].toUpperCase(),b={ctid:jg.ctid,yj:Uj,Cj:Tj,bm:xm.pe?2:1,ve:jg.canonicalContainerId},c=Cm();b.lm=c?c.canonicalContainerId:void 0;bk?(b.Uc=Lr[a],b.Uc||(b.Uc=0)):b.Uc=dk?13:10;Oj.C?(b.Sc=0,b.Pl=2):Oj.N?b.Sc=1:Hr()?b.Sc=2:b.Sc=3;var d={6:!1};Oj.H===2?d[7]=!0:Oj.H===1&&(d[2]=!0);if(wc){var e=Nk(Tk(wc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Rl=d;return ef(b)};function Or(a,b,c,d){var e,f=Number(a.Bc!=null?a.Bc:void 0);f!==0&&(e=new Date((b||zb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Dc:d}};var Pr=["ad_storage","ad_user_data"];function Qr(a,b){if(!a)return db("TAGGING",32),10;if(b===null||b===void 0||b==="")return db("TAGGING",33),11;var c=Rr(!1);if(c.error!==0)return db("TAGGING",34),c.error;if(!c.value)return db("TAGGING",35),2;c.value[a]=b;var d=Sr(c);d!==0&&db("TAGGING",36);return d}
function Tr(a){if(!a)return db("TAGGING",27),{error:10};var b=Rr();if(b.error!==0)return db("TAGGING",29),b;if(!b.value)return db("TAGGING",30),{error:2};if(!(a in b.value))return db("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(db("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Rr(a){a=a===void 0?!0:a;if(!Ym(Pr))return db("TAGGING",43),{error:3};try{if(!x.localStorage)return db("TAGGING",44),{error:1}}catch(f){return db("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return db("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return db("TAGGING",47),{error:12}}}catch(f){return db("TAGGING",48),{error:8}}if(b.schema!=="gcl")return db("TAGGING",49),{error:4};
if(b.version!==1)return db("TAGGING",50),{error:5};try{var e=Ur(b);a&&e&&Sr({value:b,error:0})}catch(f){return db("TAGGING",48),{error:8}}return{value:b,error:0}}
function Ur(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,db("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Ur(a[e.value])||c;return c}return!1}
function Sr(a){if(a.error)return a.error;if(!a.value)return db("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return db("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return db("TAGGING",53),7}return 0};var Vr={oj:"value",Gb:"conversionCount"},Wr=[Vr,{am:9,vm:10,oj:"timeouts",Gb:"timeouts"}];function Xr(){var a=Vr;if(!Yr(a))return{};var b=Zr(Wr),c=b[a.Gb];if(c===void 0||c===-1)return b;var d={},e=Object.assign({},b,(d[a.Gb]=c+1,d));return $r(e)?e:b}
function Zr(a){var b;a:{var c=Tr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&Yr(m)){var n=e[m.oj];n===void 0||Number.isNaN(n)?f[m.Gb]=-1:f[m.Gb]=Number(n)}else f[m.Gb]=-1}return f}
function $r(a,b){b=b||{};for(var c=zb(),d=Or(b,c,!0),e={},f=l(Wr),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.Gb];m!==void 0&&m!==-1&&(e[h.oj]=m)}e.creationTimeMs=c;return Qr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function Yr(a){return Ym(["ad_storage","ad_user_data"])?!a.vm||Ka(a.vm):!1}function as(a){return Ym(["ad_storage","ad_user_data"])?!a.am||Ka(a.am):!1};function bs(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};function cs(a){return a.origin!=="null"};function ds(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ka(11)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function es(a,b,c,d){return fs(d)?ds(a,String(b||gs()),c):[]}function hs(a,b,c,d,e){if(fs(e)){var f=is(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=js(f,function(g){return g.Zo},b);if(f.length===1)return f[0];f=js(f,function(g){return g.bq},c);return f[0]}}}function ks(a,b,c,d){var e=gs(),f=window;cs(f)&&(f.document.cookie=a);var g=gs();return e!==g||c!==void 0&&es(b,g,!1,d).indexOf(c)>=0}
function ls(a,b,c,d){function e(w,y,A){if(A==null)return delete h[y],w;h[y]=A;return w+"; "+y+"="+A}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!fs(c.Dc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ms(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Xp);g=e(g,"samesite",c.tq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=ns(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!os(u,c.path)&&ks(v,a,b,c.Dc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return os(n,c.path)?1:ks(g,a,b,c.Dc)?0:1}function ps(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return ls(a,b,c)}
function js(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function is(a,b,c){for(var d=[],e=es(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Qo:e[f],Ro:g.join("."),Zo:Number(n[0])||1,bq:Number(n[1])||1})}}}return d}function ms(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var qs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,rs=/(^|\.)doubleclick\.net$/i;function os(a,b){return a!==void 0&&(rs.test(window.document.location.hostname)||b==="/"&&qs.test(a))}function ss(a){if(!a)return 1;var b=a;Ka(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function ts(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function us(a,b){var c=""+ss(a),d=ts(b);d>1&&(c+="-"+d);return c}
var gs=function(){return cs(window)?window.document.cookie:""},fs=function(a){return a&&Ka(7)?(Array.isArray(a)?a:[a]).every(function(b){return $m(b)&&Ym(b)}):!0},ns=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;rs.test(e)||qs.test(e)||a.push("none");return a};function vs(a){var b=Math.round(Math.random()*2147483647);return a?String(b^bs(a)&2147483647):String(b)}function ws(a){return[vs(a),Math.round(zb()/1E3)].join(".")}function xs(a,b,c,d,e){var f=ss(b),g;return(g=hs(a,f,ts(c),d,e))==null?void 0:g.Ro};var ys;function zs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=As,d=Bs,e=Cs();if(!e.init){Kc(z,"mousedown",a);Kc(z,"keyup",a);Kc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Ds(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Cs().decorators.push(f)}
function Es(a,b,c){for(var d=Cs().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Cb(e,g.callback())}}return e}
function Cs(){var a=xc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Fs=/(.*?)\*(.*?)\*(.*)/,Gs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Hs=/^(?:www\.|m\.|amp\.)+/,Is=/([^?#]+)(\?[^#]*)?(#.*)?/;function Js(a){var b=Is.exec(a);if(b)return{uj:b[1],query:b[2],fragment:b[3]}}function Ks(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Ls(a,b){var c=[tc.userAgent,(new Date).getTimezoneOffset(),tc.userLanguage||tc.language,Math.floor(zb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=ys)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}ys=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^ys[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ms(a){return function(b){var c=Tk(x.location.href),d=c.search.replace("?",""),e=Kk(d,"_gl",!1,!0)||"";b.query=Ns(e)||{};var f=Nk(c,"fragment"),g;var h=-1;if(Eb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Ns(g||"")||{};a&&Os(c,d,f)}}function Ps(a,b){var c=Ks(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Os(a,b,c){function d(g,h){var m=Ps("_gl",g);m.length&&(m=h+m);return m}if(sc&&sc.replaceState){var e=Ks("_gl");if(e.test(b)||e.test(c)){var f=Nk(a,"path");b=d(b,"?");c=d(c,"#");sc.replaceState({},"",""+f+b+c)}}}function Qs(a,b){var c=Ms(!!b),d=Cs();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Cb(e,f.query),a&&Cb(e,f.fragment));return e}
var Ns=function(a){try{var b=Rs(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=bb(d[e+1]);c[f]=g}db("TAGGING",6);return c}}catch(h){db("TAGGING",8)}};function Rs(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Fs.exec(d);if(f){c=f;break a}d=Mk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Ls(h,p)){m=!0;break a}m=!1}if(m)return h;db("TAGGING",7)}}}
function Ss(a,b,c,d,e){function f(p){p=Ps(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Js(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.uj+h+m}
function Ts(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(ab(String(y))))}var A=v.join("*");u=["1",Ls(A),A].join("*");d?(Ka(3)||Ka(1)||!p)&&Us("_gl",u,a,p,q):Vs("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Es(b,1,d),f=Es(b,2,d),g=Es(b,4,d),h=Es(b,3,d);c(e,!1,!1);c(f,!0,!1);Ka(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
Ws(m,h[m],a)}function Ws(a,b,c){c.tagName.toLowerCase()==="a"?Vs(a,b,c):c.tagName.toLowerCase()==="form"&&Us(a,b,c)}function Vs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ka(4)||d)){var h=x.location.href,m=Js(c.href),n=Js(h);g=!(m&&n&&m.uj===n.uj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=Ss(a,b,c.href,d,e);ic.test(p)&&(c.href=p)}}
function Us(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=Ss(a,b,f,d,e);ic.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function As(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||Ts(e,e.hostname)}}catch(g){}}function Bs(a){try{var b=a.getAttribute("action");if(b){var c=Nk(Tk(b),"host");Ts(a,c)}}catch(d){}}function Xs(a,b,c,d){zs();var e=c==="fragment"?2:1;d=!!d;Ds(a,b,e,d,!1);e===2&&db("TAGGING",23);d&&db("TAGGING",24)}
function Ys(a,b){zs();Ds(a,[Pk(x.location,"host",!0)],b,!0,!0)}function Zs(){var a=z.location.hostname,b=Gs.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Mk(f[2])||"":Mk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Hs,""),m=e.replace(Hs,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function $s(a,b){return a===!1?!1:a||b||Zs()};var at=["1"],bt={},ct={};function dt(a,b){b=b===void 0?!0:b;var c=et(a.prefix);if(bt[c])ft(a);else if(gt(c,a.path,a.domain)){var d=ct[et(a.prefix)]||{id:void 0,Bh:void 0};b&&ht(a,d.id,d.Bh);ft(a)}else{var e=Vk("auiddc");if(e)db("TAGGING",17),bt[c]=e;else if(b){var f=et(a.prefix),g=ws();it(f,g,a);gt(c,a.path,a.domain);ft(a,!0)}}}
function ft(a,b){if((b===void 0?0:b)&&Yr(Vr)){var c=Rr(!1);c.error!==0?db("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Sr(c)!==0&&db("TAGGING",41)):db("TAGGING",40):db("TAGGING",39)}if(as(Vr)&&Zr([Vr])[Vr.Gb]===-1){for(var d={},e=(d[Vr.Gb]=0,d),f=l(Wr),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Vr&&as(h)&&(e[h.Gb]=0)}$r(e,a)}}
function ht(a,b,c){var d=et(a.prefix),e=bt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(zb()/1E3)));it(d,h,a,g*1E3)}}}}function it(a,b,c,d){var e;e=["1",us(c.domain,c.path),b].join(".");var f=Or(c,d);f.Dc=jt();ps(a,e,f)}function gt(a,b,c){var d=xs(a,b,c,at,jt());if(!d)return!1;kt(a,d);return!0}
function kt(a,b){var c=b.split(".");c.length===5?(bt[a]=c.slice(0,2).join("."),ct[a]={id:c.slice(2,4).join("."),Bh:Number(c[4])||0}):c.length===3?ct[a]={id:c.slice(0,2).join("."),Bh:Number(c[2])||0}:bt[a]=b}function et(a){return(a||"_gcl")+"_au"}function lt(a){function b(){Ym(c)&&a()}var c=jt();dn(function(){b();Ym(c)||en(b,c)},c)}
function mt(a){var b=Qs(!0),c=et(a.prefix);lt(function(){var d=b[c];if(d){kt(c,d);var e=Number(bt[c].split(".")[1])*1E3;if(e){db("TAGGING",16);var f=Or(a,e);f.Dc=jt();var g=["1",us(a.domain,a.path),d].join(".");ps(c,g,f)}}})}function nt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=xs(a,e.path,e.domain,at,jt());h&&(g[a]=h);return g};lt(function(){Xs(f,b,c,d)})}function jt(){return["ad_storage","ad_user_data"]};function ot(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Fj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function pt(a,b){var c=ot(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Fj]||(d[c[e].Fj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Fj].push(g)}}return d};var qt={},rt=(qt.k={da:/^[\w-]+$/},qt.b={da:/^[\w-]+$/,zj:!0},qt.i={da:/^[1-9]\d*$/},qt.h={da:/^\d+$/},qt.t={da:/^[1-9]\d*$/},qt.d={da:/^[A-Za-z0-9_-]+$/},qt.j={da:/^\d+$/},qt.u={da:/^[1-9]\d*$/},qt.l={da:/^[01]$/},qt.o={da:/^[1-9]\d*$/},qt.g={da:/^[01]$/},qt.s={da:/^.+$/},qt);var st={},wt=(st[5]={Gh:{2:tt},nj:"2",qh:["k","i","b","u"]},st[4]={Gh:{2:tt,GCL:ut},nj:"2",qh:["k","i","b"]},st[2]={Gh:{GS2:tt,GS1:vt},nj:"GS2",qh:"sogtjlhd".split("")},st);function xt(a,b,c){var d=wt[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function tt(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=wt[b];if(f){for(var g=f.qh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=rt[p];r&&(r.zj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function zt(a,b,c){var d=wt[b];if(d)return[d.nj,c||"1",At(a,b)].join(".")}
function At(a,b){var c=wt[b];if(c){for(var d=[],e=l(c.qh),f=e.next();!f.done;f=e.next()){var g=f.value,h=rt[g];if(h){var m=a[g];if(m!==void 0)if(h.zj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function ut(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function vt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Bt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Ct(a,b,c){if(wt[b]){for(var d=[],e=es(a,void 0,void 0,Bt.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=xt(g.value,b,c);h&&d.push(Dt(h))}return d}}function Et(a,b,c,d,e){d=d||{};var f=us(d.domain,d.path),g=zt(b,c,f);if(!g)return 1;var h=Or(d,e,void 0,Bt.get(c));return ps(a,g,h)}function Ft(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Dt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Uf:void 0},c=b.next()){var e=c.value,f=a[e];d.Uf=rt[e];d.Uf?d.Uf.zj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Ft(h,g.Uf)}}(d)):void 0:typeof f==="string"&&Ft(f,d.Uf)||(a[e]=void 0):a[e]=void 0}return a};var Gt=function(){this.value=0};Gt.prototype.set=function(a){return this.value|=1<<a};var Ht=function(a,b){b<=0||(a.value|=1<<b-1)};Gt.prototype.get=function(){return this.value};Gt.prototype.clear=function(a){this.value&=~(1<<a)};Gt.prototype.clearAll=function(){this.value=0};Gt.prototype.equals=function(a){return this.value===a.value};function It(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Jt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Kt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Nb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Nb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(bs((""+b+e).toLowerCase()))};var Lt={},Mt=(Lt.gclid=!0,Lt.dclid=!0,Lt.gbraid=!0,Lt.wbraid=!0,Lt),Nt=/^\w+$/,Ot=/^[\w-]+$/,Pt={},Qt=(Pt.aw="_aw",Pt.dc="_dc",Pt.gf="_gf",Pt.gp="_gp",Pt.gs="_gs",Pt.ha="_ha",Pt.ag="_ag",Pt.gb="_gb",Pt),Rt=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,St=/^www\.googleadservices\.com$/;function Tt(){return["ad_storage","ad_user_data"]}function Ut(a){return!Ka(7)||Ym(a)}function Vt(a,b){function c(){var d=Ut(b);d&&a();return d}dn(function(){c()||en(c,b)},b)}
function Wt(a){return Xt(a).map(function(b){return b.gclid})}function Yt(a){return Zt(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function Zt(a){var b=$t(a.prefix),c=au("gb",b),d=au("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=Xt(c).map(e("gb")),g=bu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function cu(a,b,c,d,e,f){var g=nb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Fd=f),g.labels=du(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Fd:f})}function bu(a){for(var b=Ct(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=eu(f);h&&cu(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function Xt(a){for(var b=[],c=es(a,z.cookie,void 0,Tt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=fu(e.value);if(f!=null){var g=f;cu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return gu(b)}function hu(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function iu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ka&&b.Ka&&h.Ka.equals(b.Ka)&&(e=h)}if(d){var m,n,p=(m=d.Ka)!=null?m:new Gt,q=(n=b.Ka)!=null?n:new Gt;p.value|=q.value;d.Ka=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Fd=b.Fd);d.labels=hu(d.labels||[],b.labels||[]);d.Bb=hu(d.Bb||[],b.Bb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function ju(a){if(!a)return new Gt;var b=new Gt;if(a===1)return Ht(b,2),Ht(b,3),b;Ht(b,a);return b}
function ku(){var a=Tr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Ot))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Gt;typeof e==="number"?g=ju(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ka:g,Bb:[2]}}catch(h){return null}}
function lu(){var a=Tr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Ot))return b;var f=new Gt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ka:f,Bb:[2]});return b},[])}catch(b){return null}}
function mu(a){for(var b=[],c=es(a,z.cookie,void 0,Tt()),d=l(c),e=d.next();!e.done;e=d.next()){var f=fu(e.value);f!=null&&(f.Fd=void 0,f.Ka=new Gt,f.Bb=[1],iu(b,f))}var g=ku();g&&(g.Fd=void 0,g.Bb=g.Bb||[2],iu(b,g));if(Ka(13)){var h=lu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Fd=void 0;p.Bb=p.Bb||[2];iu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return gu(b)}
function du(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function $t(a){return a&&typeof a==="string"&&a.match(Nt)?a:"_gcl"}function nu(a,b){if(a){var c={value:a,Ka:new Gt};Ht(c.Ka,b);return c}}
function ou(a,b,c){var d=Tk(a),e=Nk(d,"query",!1,void 0,"gclsrc"),f=nu(Nk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=nu(Kk(g,"gclid",!1),3));e||(e=Kk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function pu(a,b){var c=Tk(a),d=Nk(c,"query",!1,void 0,"gclid"),e=Nk(c,"query",!1,void 0,"gclsrc"),f=Nk(c,"query",!1,void 0,"wbraid");f=Lb(f);var g=Nk(c,"query",!1,void 0,"gbraid"),h=Nk(c,"query",!1,void 0,"gad_source"),m=Nk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Kk(n,"gclid",!1);e=e||Kk(n,"gclsrc",!1);f=f||Kk(n,"wbraid",!1);g=g||Kk(n,"gbraid",!1);h=h||Kk(n,"gad_source",!1)}return qu(d,e,m,f,g,h)}function ru(){return pu(x.location.href,!0)}
function qu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Ot))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Ot.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Ot.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Ot.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function su(a){for(var b=ru(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=pu(x.document.referrer,!1),b.gad_source=void 0);tu(b,!1,a)}
function uu(a){su(a);var b=ou(x.location.href,!0,!1);b.length||(b=ou(x.document.referrer,!1,!0));a=a||{};vu(a);if(b.length){var c=b[0],d=zb(),e=Or(a,d,!0),f=Tt(),g=function(){Ut(f)&&e.expires!==void 0&&Qr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ka.get()},expires:Number(e.expires)})};dn(function(){g();Ut(f)||en(g,f)},f)}}
function vu(a){var b;if(b=Ka(14)){var c=wu();b=Rt.test(c)||St.test(c)||xu()}if(b){var d;a:{for(var e=Tk(x.location.href),f=Lk(Nk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!Mt[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=It(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Jt(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,A=w.next().value,C=y,E=A,G=C&7;if(C>>3===16382){if(G!==0)break;var I=Jt(t,E);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var T=void 0,ca=t,P=E;switch(G){case 0:M=(T=Jt(ca,P))==null?void 0:T[1];break d;case 1:M=P+8;break d;case 2:var ha=Jt(ca,P);if(ha===void 0)break;var da=l(ha),ka=da.next().value;M=da.next().value+ka;break d;case 5:M=P+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(W){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var X=d;X&&yu(X,7,a)}}
function yu(a,b,c){c=c||{};var d=zb(),e=Or(c,d,!0),f=Tt(),g=function(){if(Ut(f)&&e.expires!==void 0){var h=lu()||[];iu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ka:ju(b)},!0);Qr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ka?m.Ka.get():0},expires:Number(m.expires)}}))}};dn(function(){Ut(f)?g():en(g,f)},f)}
function tu(a,b,c,d,e){c=c||{};e=e||[];var f=$t(c.prefix),g=d||zb(),h=Math.round(g/1E3),m=Tt(),n=!1,p=!1,q=function(){if(Ut(m)){var r=Or(c,g,!0);r.Dc=m;for(var t=function(T,ca){var P=au(T,f);P&&(ps(P,ca,r),T!=="gb"&&(n=!0))},u=function(T){var ca=["GCL",h,T];e.length>0&&ca.push(e.join("."));return ca.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],C=au("gb",f);!b&&Xt(C).some(function(T){return T.gclid===A&&T.labels&&
T.labels.length>0})||t("gb",u(A))}}if(!p&&a.gbraid&&Ut("ad_storage")&&(p=!0,!n)){var E=a.gbraid,G=au("ag",f);if(b||!bu(G).some(function(T){return T.gclid===E&&T.labels&&T.labels.length>0})){var I={},M=(I.k=E,I.i=""+h,I.b=e,I);Et(G,M,5,c,g)}}zu(a,f,g,c)};dn(function(){q();Ut(m)||en(q,m)},m)}
function zu(a,b,c,d){if(a.gad_source!==void 0&&Ut("ad_storage")){var e=Zc();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=au("gs",b);if(g){var h=Math.floor((zb()-(Yc()||0))/1E3),m,n=Kt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Et(g,m,5,d,c)}}}}
function Au(a,b){var c=Qs(!0);Vt(function(){for(var d=$t(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Qt[f]!==void 0){var g=au(f,d),h=c[g];if(h){var m=Math.min(Bu(h),zb()),n;b:{for(var p=m,q=es(g,z.cookie,void 0,Tt()),r=0;r<q.length;++r)if(Bu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Or(b,m,!0);t.Dc=Tt();ps(g,h,t)}}}}tu(qu(c.gclid,c.gclsrc),!1,b)},Tt())}
function Cu(a){var b=["ag"],c=Qs(!0),d=$t(a.prefix);Vt(function(){for(var e=0;e<b.length;++e){var f=au(b[e],d);if(f){var g=c[f];if(g){var h=xt(g,5);if(h){var m=eu(h);m||(m=zb());var n;a:{for(var p=m,q=Ct(f,5),r=0;r<q.length;++r)if(eu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Et(f,h,5,a,m)}}}}},["ad_storage"])}function au(a,b){var c=Qt[a];if(c!==void 0)return b+c}function Bu(a){return Du(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function eu(a){return a?(Number(a.i)||0)*1E3:0}function fu(a){var b=Du(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Du(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Ot.test(a[2])?[]:a}
function Eu(a,b,c,d,e){if(Array.isArray(b)&&cs(x)){var f=$t(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=au(a[m],f);if(n){var p=es(n,z.cookie,void 0,Tt());p.length&&(h[n]=p.sort()[p.length-1])}}return h};Vt(function(){Xs(g,b,c,d)},Tt())}}
function Fu(a,b,c,d){if(Array.isArray(a)&&cs(x)){var e=["ag"],f=$t(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=au(e[m],f);if(!n)return{};var p=Ct(n,5);if(p.length){var q=p.sort(function(r,t){return eu(t)-eu(r)})[0];h[n]=zt(q,5)}}return h};Vt(function(){Xs(g,a,b,c)},["ad_storage"])}}function gu(a){return a.filter(function(b){return Ot.test(b.gclid)})}
function Gu(a,b){if(cs(x)){for(var c=$t(b.prefix),d={},e=0;e<a.length;e++)Qt[a[e]]&&(d[a[e]]=Qt[a[e]]);Vt(function(){sb(d,function(f,g){var h=es(c+g,z.cookie,void 0,Tt());h.sort(function(t,u){return Bu(u)-Bu(t)});if(h.length){var m=h[0],n=Bu(m),p=Du(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Du(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];tu(q,!0,b,n,p)}})},Tt())}}
function Hu(a){var b=["ag"],c=["gbraid"];Vt(function(){for(var d=$t(a.prefix),e=0;e<b.length;++e){var f=au(b[e],d);if(!f)break;var g=Ct(f,5);if(g.length){var h=g.sort(function(q,r){return eu(r)-eu(q)})[0],m=eu(h),n=h.b,p={};p[c[e]]=h.k;tu(p,!0,a,m,n)}}},["ad_storage"])}function Iu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Ju(a){function b(h,m,n){n&&(h[m]=n)}if(an()){var c=ru(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Qs(!1)._gs);if(Iu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);Ys(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);Ys(function(){return g},1)}}}function xu(){var a=Tk(x.location.href);return Nk(a,"query",!1,void 0,"gad_source")}
function Ku(a){if(!Ka(1))return null;var b=Qs(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ka(2)){b=xu();if(b!=null)return b;var c=ru();if(Iu(c,a))return"0"}return null}function Lu(a){var b=Ku(a);b!=null&&Ys(function(){var c={};return c.gad_source=b,c},4)}function Mu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Nu(a,b,c,d){var e=[];c=c||{};if(!Ut(Tt()))return e;var f=Xt(a),g=Mu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Or(c,p,!0);r.Dc=Tt();ps(a,q,r)}return e}
function Ou(a,b){var c=[];b=b||{};var d=Zt(b),e=Mu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=$t(b.prefix),n=au(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Et(n,y,5,b,u)}else if(h.type==="gb"){var A=[q,v,r].concat(t||[],[a]).join("."),C=Or(b,u,!0);C.Dc=Tt();ps(n,A,C)}}return c}
function Pu(a,b){var c=$t(b),d=au(a,c);if(!d)return 0;var e;e=a==="ag"?bu(d):Xt(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Qu(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Ru(a){var b=Math.max(Pu("aw",a),Qu(Ut(Tt())?pt():{})),c=Math.max(Pu("gb",a),Qu(Ut(Tt())?pt("_gac_gb",!0):{}));c=Math.max(c,Pu("ag",a));return c>b}
function wu(){return z.referrer?Nk(Tk(z.referrer),"host"):""};
var Su=function(a,b){b=b===void 0?!1:b;var c=vp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},Tu=function(a){return Uk(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},$u=function(a,b,c,d,e){var f=$t(a.prefix);if(Su(f,!0)){var g=ru(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=Uu(),r=q.Yf,t=q.Xl;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,Bd:p});n&&h.push({gclid:n,Bd:"ds"});h.length===2&&N(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,Bd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",Bd:"aw.ds"});Vu(function(){var u=Q(Wu());if(u){dt(a);var v=[],w=u?bt[et(a.prefix)]:void 0;w&&v.push("auid="+w);if(Q(K.m.V)){e&&v.push("userId="+e);var y=un(qn.Z.Bl);if(y===void 0)tn(qn.Z.Cl,!0);else{var A=un(qn.Z.kh);v.push("ga_uid="+A+"."+y)}}var C=wu(),E=u||!d?h:[];E.length===0&&(Rt.test(C)||St.test(C))&&E.push({gclid:"",Bd:""});if(E.length!==0||r!==void 0){C&&v.push("ref="+encodeURIComponent(C));var G=Xu();v.push("url="+
encodeURIComponent(G));v.push("tft="+zb());var I=Yc();I!==void 0&&v.push("tfd="+Math.round(I));var M=Il(!0);v.push("frm="+M);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var T={};c=iq(Zp(new Yp(0),(T[K.m.Ea]=Fq.C[K.m.Ea],T)))}v.push("gtm="+Mr({Ma:b}));wr()&&v.push("gcs="+xr());v.push("gcd="+Br(c));Er()&&v.push("dma_cps="+Cr());v.push("dma="+Dr());vr(c)?v.push("npa=0"):v.push("npa=1");Gr()&&v.push("_ng=1");$q(hr())&&
v.push("tcfd="+Fr());var ca=or();ca&&v.push("gdpr="+ca);var P=nr();P&&v.push("gdpr_consent="+P);F(23)&&v.push("apve=0");F(123)&&Qs(!1)._up&&v.push("gtm_up=1");lk()&&v.push("tag_exp="+lk());if(E.length>0)for(var ha=0;ha<E.length;ha++){var da=E[ha],ka=da.gclid,X=da.Bd;if(!Yu(a.prefix,X+"."+ka,w!==void 0)){var W=Zu+"?"+v.join("&");ka!==""?W=X==="gb"?W+"&wbraid="+ka:W+"&gclid="+ka+"&gclsrc="+X:X==="aw.ds"&&(W+="&gclsrc=aw.ds");Qc(W)}}else if(r!==void 0&&!Yu(a.prefix,"gad",w!==void 0)){var ta=Zu+"?"+v.join("&");
Qc(ta)}}}})}},Yu=function(a,b,c){var d=vp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},Uu=function(){var a=Tk(x.location.href),b=void 0,c=void 0,d=Nk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(av);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Yf:b,Xl:c}},Xu=function(){var a=Il(!1)===1?x.top.location.href:x.location.href;return a=a.replace(/[\?#].*$/,"")},bv=function(a){var b=[];sb(a,function(c,d){d=gu(d);for(var e=
[],f=0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},dv=function(a,b){return cv("dc",a,b)},ev=function(a,b){return cv("aw",a,b)},cv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Vk("gcl"+a);if(d)return d.split(".")}var e=$t(b);if(e==="_gcl"){var f=!Q(Wu())&&c,g;g=ru()[a]||[];if(g.length>0)return f?["0"]:g}var h=au(a,e);return h?Wt(h):[]},Vu=function(a){var b=Wu();np(function(){a();Q(b)||en(a,b)},b)},Wu=function(){return[K.m.U,K.m.V]},Zu=Ri(36,
'https://adservice.google.com/pagead/regclk'),av=/^gad_source[_=](\d+)$/;function fv(){return vp("dedupe_gclid",function(){return ws()})};var gv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,hv=/^www.googleadservices.com$/;function iv(a){a||(a=jv());return a.Kq?!1:a.Fp||a.Gp||a.Jp||a.Hp||a.Yf||a.pp||a.Ip||a.vp?!0:!1}function jv(){var a={},b=Qs(!0);a.Kq=!!b._up;var c=ru();a.Fp=c.aw!==void 0;a.Gp=c.dc!==void 0;a.Jp=c.wbraid!==void 0;a.Hp=c.gbraid!==void 0;a.Ip=c.gclsrc==="aw.ds";a.Yf=Uu().Yf;var d=z.referrer?Nk(Tk(z.referrer),"host"):"";a.vp=gv.test(d);a.pp=hv.test(d);return a};function kv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function lv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function mv(){return["ad_storage","ad_user_data"]}function nv(a){if(F(38)&&!un(qn.Z.pl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{kv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(tn(qn.Z.pl,function(d){d.gclid&&yu(d.gclid,5,a)}),lv(c)||N(178))})}catch(c){N(177)}};dn(function(){Ut(mv())?b():en(b,mv())},mv())}};var ov=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function pv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?tn(qn.Z.Of,{gadSource:a.data.gadSource}):N(173)}
function qv(a,b){if(F(a)){if(un(qn.Z.Of))return N(176),qn.Z.Of;if(un(qn.Z.rl))return N(170),qn.Z.Of;var c=Kl();if(!c)N(171);else if(c.opener){var d=function(g){if(ov.includes(g.origin)){a===119?pv(g):a===200&&(pv(g),g.data.gclid&&yu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Sq(c,"message",d)}else N(172)};if(Rq(c,"message",d)){tn(qn.Z.rl,!0);for(var e=l(ov),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);N(174);return qn.Z.Of}N(175)}}}
;var rv=function(){this.C=this.gppString=void 0};rv.prototype.reset=function(){this.C=this.gppString=void 0};var sv=new rv;var tv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),uv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,vv=/^\d+\.fls\.doubleclick\.net$/,wv=/;gac=([^;?]+)/,xv=/;gacgb=([^;?]+)/;
function yv(a,b){if(vv.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(tv)?Mk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function zv(a,b,c){for(var d=Ut(Tt())?pt("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Nu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{op:f?e.join(";"):"",np:yv(d,xv)}}function Av(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(uv)?b[1]:void 0}
function Bv(a){var b={},c,d,e;vv.test(z.location.host)&&(c=Av("gclgs"),d=Av("gclst"),e=Av("gcllp"));if(c&&d&&e)b.th=c,b.wh=d,b.uh=e;else{var f=zb(),g=bu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Fd});h.length>0&&m.length>0&&n.length>0&&(b.th=h.join("."),b.wh=m.join("."),b.uh=n.join("."))}return b}
function Cv(a,b,c,d){d=d===void 0?!1:d;if(vv.test(z.location.host)){var e=Av(c);if(e){if(d){var f=new Gt;Ht(f,2);Ht(f,3);return e.split(".").map(function(h){return{gclid:h,Ka:f,Bb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?mu(g):Xt(g)}if(b==="wbraid")return Xt((a||"_gcl")+"_gb");if(b==="braids")return Zt({prefix:a})}return[]}function Dv(a){return vv.test(z.location.host)?!(Av("gclaw")||Av("gac")):Ru(a)}
function Ev(a,b,c){var d;d=c?Ou(a,b):Nu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Fv(){var a=x.__uspapi;if(jb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var Kv=function(a){if(a.eventName===K.m.qa&&S(a,R.A.ia)===L.J.Ga)if(F(24)){U(a,R.A.se,O(a.D,K.m.za)!=null&&O(a.D,K.m.za)!==!1&&!Q([K.m.U,K.m.V]));var b=Gv(a),c=O(a.D,K.m.Oa)!==!1;c||V(a,K.m.Qh,"1");var d=$t(b.prefix),e=S(a,R.A.fh);if(!S(a,R.A.fa)&&!S(a,R.A.Rf)&&!S(a,R.A.qe)){var f=O(a.D,K.m.Eb),g=O(a.D,K.m.Pa)||{};Hv({we:c,Be:g,Ge:f,Rc:b});if(!e&&!Su(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{V(a,K.m.hd,K.m.Yc);if(S(a,R.A.fa))V(a,K.m.hd,K.m.jn),V(a,K.m.fa,"1");else if(S(a,R.A.Rf))V(a,K.m.hd,
K.m.tn);else if(S(a,R.A.qe))V(a,K.m.hd,K.m.qn);else{var h=ru();V(a,K.m.Zc,h.gclid);V(a,K.m.fd,h.dclid);V(a,K.m.fk,h.gclsrc);Iv(a,K.m.Zc)||Iv(a,K.m.fd)||(V(a,K.m.Xd,h.wbraid),V(a,K.m.Pe,h.gbraid));V(a,K.m.Wa,wu());V(a,K.m.Aa,Xu());if(F(27)&&wc){var m=Nk(Tk(wc),"host");m&&V(a,K.m.Nk,m)}if(!S(a,R.A.qe)){var n=Uu(),p=n.Xl;V(a,K.m.Ne,n.Yf);V(a,K.m.Oe,p)}V(a,K.m.Jc,Il(!0));var q=jv();iv(q)&&V(a,K.m.kd,"1");V(a,K.m.hk,fv());Qs(!1)._up==="1"&&V(a,K.m.Dk,"1")}Un=!0;V(a,K.m.Db);V(a,K.m.Pb);var r=Q([K.m.U,K.m.V]);
r&&(V(a,K.m.Db,Jv()),c&&(dt(b),V(a,K.m.Pb,bt[et(b.prefix)])));V(a,K.m.mc);V(a,K.m.lb);if(!Iv(a,K.m.Zc)&&!Iv(a,K.m.fd)&&Dv(d)){var t=Yt(b);t.length>0&&V(a,K.m.mc,t.join("."))}else if(!Iv(a,K.m.Xd)&&r){var u=Wt(d+"_aw");u.length>0&&V(a,K.m.lb,u.join("."))}F(31)&&V(a,K.m.Gk,Zc());a.D.isGtmEvent&&(a.D.C[K.m.Ea]=Fq.C[K.m.Ea]);vr(a.D)?V(a,K.m.xc,!1):V(a,K.m.xc,!0);U(a,R.A.qg,!0);var v=Fv();v!==void 0&&V(a,K.m.Cf,v||"error");var w=or();w&&V(a,K.m.jd,w);if(F(137))try{var y=Intl.DateTimeFormat().resolvedOptions().timeZone;
V(a,K.m.ii,y||"-")}catch(G){V(a,K.m.ii,"e")}var A=nr();A&&V(a,K.m.nd,A);var C=sv.gppString;C&&V(a,K.m.hf,C);var E=sv.C;E&&V(a,K.m.ff,E);U(a,R.A.Ha,!1)}}else a.isAborted=!0},Gv=function(a){var b={prefix:O(a.D,K.m.Rb)||O(a.D,K.m.cb),domain:O(a.D,K.m.nb),Bc:O(a.D,K.m.ob),flags:O(a.D,K.m.yb)};a.D.isGtmEvent&&(b.path=O(a.D,K.m.Sb));return b},Lv=function(a,b){var c,d,e,f,g,h,m,n;c=a.we;d=a.Be;e=a.Ge;f=a.Ma;g=a.D;h=a.De;m=a.Fr;n=a.Jm;Hv({we:c,Be:d,Ge:e,Rc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,$u(b,
f,g,h,n))},Mv=function(a,b){if(!S(a,R.A.qe)){var c=qv(119);if(c){var d=un(c),e=function(g){U(a,R.A.qe,!0);var h=Iv(a,K.m.Ne),m=Iv(a,K.m.Oe);V(a,K.m.Ne,String(g.gadSource));V(a,K.m.Oe,6);U(a,R.A.fa);U(a,R.A.Rf);V(a,K.m.fa);b();V(a,K.m.Ne,h);V(a,K.m.Oe,m);U(a,R.A.qe,!1)};if(d)e(d);else{var f=void 0;f=wn(c,function(g,h){e(h);xn(c,f)})}}}},Hv=function(a){var b,c,d,e;b=a.we;c=a.Be;d=a.Ge;e=a.Rc;b&&($s(c[K.m.de],!!c[K.m.ma])&&(Au(Nv,e),Cu(e),mt(e)),Il()!==2?(uu(e),nv(e),qv(200,e)):su(e),Gu(Nv,e),Hu(e));
c[K.m.ma]&&(Eu(Nv,c[K.m.ma],c[K.m.Mc],!!c[K.m.sc],e.prefix),Fu(c[K.m.ma],c[K.m.Mc],!!c[K.m.sc],e.prefix),nt(et(e.prefix),c[K.m.ma],c[K.m.Mc],!!c[K.m.sc],e),nt("FPAU",c[K.m.ma],c[K.m.Mc],!!c[K.m.sc],e));d&&(F(101)?Ju(Ov):Ju(Pv));Lu(Pv)},Qv=function(a,b,c,d){var e,f,g;e=a.Km;f=a.callback;g=a.fm;if(typeof f==="function")if(e===K.m.lb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===K.m.Pb?(N(65),dt(b,!1),f(bt[et(b.prefix)])):f(g)},Rv=function(a,b){Array.isArray(b)||
(b=[b]);var c=S(a,R.A.ia);return b.indexOf(c)>=0},Nv=["aw","dc","gb"],Pv=["aw","dc","gb","ag"],Ov=["aw","dc","gb","ag","gad_source"];function Sv(a){var b=O(a.D,K.m.Lc),c=O(a.D,K.m.Kc);b&&!c?(a.eventName!==K.m.qa&&a.eventName!==K.m.Td&&N(131),a.isAborted=!0):!b&&c&&(N(132),a.isAborted=!0)}function Tv(a){var b=Q(K.m.U)?up.pscdl:"denied";b!=null&&V(a,K.m.Fg,b)}function Uv(a){var b=Il(!0);V(a,K.m.Jc,b)}function Vv(a){Gr()&&V(a,K.m.be,1)}
function Jv(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Mk(a.substring(0,b))===void 0;)b--;return Mk(a.substring(0,b))||""}function Wv(a){Xv(a,Cp.Df.Rm,O(a.D,K.m.ob))}function Xv(a,b,c){Iv(a,K.m.rd)||V(a,K.m.rd,{});Iv(a,K.m.rd)[b]=c}function Yv(a){U(a,R.A.Qf,Pm.X.Da)}function Zv(a){var b=gb("GTAG_EVENT_FEATURE_CHANNEL");b&&(V(a,K.m.jf,b),eb())}function $v(a){var b=a.D.getMergedValues(K.m.rc);b&&a.mergeHitDataForKey(K.m.rc,b)}
function aw(a,b){b=b===void 0?!1:b;if(F(108)){var c=S(a,R.A.Pf);if(c)if(c.indexOf(a.target.destinationId)<0){if(U(a,R.A.Ij,!1),b||!bw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else U(a,R.A.Ij,!0)}}function cw(a){il&&(Un=!0,a.eventName===K.m.qa?$n(a.D,a.target.id):(S(a,R.A.Ke)||(Xn[a.target.id]=!0),Bp(S(a,R.A.hb))))};
var dw=function(a){if(Iv(a,K.m.mc)||Iv(a,K.m.ae)){var b=Iv(a,K.m.nc),c=ld(S(a,R.A.ya),null),d=$t(c.prefix);c.prefix=d==="_gcl"?"":d;if(Iv(a,K.m.mc)){var e=Ev(b,c,!S(a,R.A.Yk));U(a,R.A.Yk,!0);e&&V(a,K.m.Rk,e)}if(Iv(a,K.m.ae)){var f=zv(b,c).op;f&&V(a,K.m.yk,f)}}},hw=function(a){var b=new ew;F(101)&&Rv(a,[L.J.W])&&V(a,K.m.Pk,Qs(!1)._gs);if(F(16)){var c=O(a.D,K.m.Aa);c||(c=Il(!1)===1?x.top.location.href:x.location.href);var d,e=Tk(c),f=Nk(e,"query",!1,void 0,"gclid");if(!f){var g=e.hash.replace("#","");
f=f||Kk(g,"gclid",!1)}(d=f?f.length:void 0)&&V(a,K.m.ek,d)}if(Q(K.m.U)&&S(a,R.A.Wc)){var h=S(a,R.A.ya),m=$t(h.prefix);m==="_gcl"&&(m="");var n=Bv(m);V(a,K.m.Ud,n.th);V(a,K.m.Wd,n.wh);V(a,K.m.Vd,n.uh);Dv(m)?fw(a,b,h,m):gw(a,b,m)}if(F(21)){var p=Q(K.m.U)&&Q(K.m.V),q;var r;b:{var t,u=x,v=[];try{u.navigation&&u.navigation.entries&&(v=u.navigation.entries())}catch(ha){}t=v;var w={};try{for(var y=t.length-1;y>=0;y--){var A=t[y]&&t[y].url;if(A){var C=(new URL(A)).searchParams,E=C.get("gclid")||void 0,G=
C.get("gclsrc")||void 0;if(E){w.gclid=E;G&&(w.Bd=G);r=w;break b}}}}catch(ha){}r=w}var I=r,M=I.gclid,T=I.Bd,ca;if(!M||T!==void 0&&T!=="aw"&&T!=="aw.ds")ca=void 0;else if(M!==void 0){var P=new Gt;Ht(P,2);Ht(P,3);ca={version:"GCL",timestamp:0,gclid:M,Ka:P,Bb:[3]}}else ca=void 0;q=ca;q&&(p||(q.gclid="0"),b.N(q),b.R(!1))}b.ka(a)},gw=function(a,b,c){var d=S(a,R.A.ia)===L.J.W&&Il()!==2;Cv(c,"gclid","gclaw",d).forEach(function(f){b.N(f)});b.R(!d);if(!c){var e=yv(Ut(Tt())?pt():{},wv);e&&V(a,K.m.Ng,e)}},fw=
function(a,b,c,d){Cv(d,"braids","gclgb").forEach(function(g){b.ba(g)});if(!d){var e=Iv(a,K.m.nc);c=ld(c,null);c.prefix=d;var f=zv(e,c,!0).np;f&&V(a,K.m.ae,f)}},ew=function(){this.C=[];this.P=[];this.H=void 0};ew.prototype.N=function(a){iu(this.C,a)};ew.prototype.ba=function(a){iu(this.P,a)};ew.prototype.R=function(a){this.H!==!1&&(this.H=a)};ew.prototype.ka=function(a){if(this.C.length>0){var b=[],c=[],d=[];this.C.forEach(function(f){b.push(f.gclid);var g,h;c.push((h=(g=f.Ka)==null?void 0:g.get())!=
null?h:0);for(var m=d.push,n=0,p=l(f.Bb||[0]),q=p.next();!q.done;q=p.next()){var r=q.value;r>0&&(n|=1<<r-1)}m.call(d,n.toString())});b.length>0&&V(a,K.m.lb,b.join("."));this.H||(c.length>0&&V(a,K.m.Le,c.join(".")),d.length>0&&V(a,K.m.Me,d.join(".")))}else{var e=this.P.map(function(f){return f.gclid}).join(".");e&&V(a,K.m.mc,e)}};
var iw=function(a,b){var c=a&&!Q([K.m.U,K.m.V]);return b&&c?"0":b},lw=function(a){var b=a.Rc===void 0?{}:a.Rc,c=$t(b.prefix);Su(c)&&np(function(){function d(y,A,C){var E=Q([K.m.U,K.m.V]),G=m&&E,I=b.prefix||"_gcl",M=jw(),T=(G?I:"")+"."+(Q(K.m.U)?1:0)+"."+(Q(K.m.V)?1:0);if(!M[T]){M[T]=!0;var ca={},P=function(ta,ra){if(ra||typeof ra==="number")ca[ta]=ra.toString()},ha="https://www.google.com";wr()&&(P("gcs",xr()),y&&P("gcu",1));P("gcd",Br(h));lk()&&P("tag_exp",lk());if(an()){P("rnd",fv());if((!p||q&&
q!=="aw.ds")&&E){var da=Wt(I+"_aw");P("gclaw",da.join("."))}P("url",String(x.location).split(/[?#]/)[0]);P("dclid",iw(f,r));E||(ha="https://pagead2.googlesyndication.com")}Er()&&P("dma_cps",Cr());P("dma",Dr());P("npa",vr(h)?0:1);Gr()&&P("_ng",1);$q(hr())&&P("tcfd",Fr());P("gdpr_consent",nr()||"");P("gdpr",or()||"");Qs(!1)._up==="1"&&P("gtm_up",1);P("gclid",iw(f,p));P("gclsrc",q);if(!(ca.hasOwnProperty("gclid")||ca.hasOwnProperty("dclid")||ca.hasOwnProperty("gclaw"))&&(P("gbraid",iw(f,t)),!ca.hasOwnProperty("gbraid")&&
an()&&E)){var ka=Wt(I+"_gb");ka.length>0&&P("gclgb",ka.join("."))}P("gtm",Mr({Ma:h.eventMetadata[R.A.hb],ph:!g}));m&&Q(K.m.U)&&(dt(b||{}),G&&P("auid",bt[et(b.prefix)]||""));kw||a.Sl&&P("did",a.Sl);a.bj&&P("gdid",a.bj);a.Xi&&P("edid",a.Xi);a.fj!==void 0&&P("frm",a.fj);F(23)&&P("apve","0");var X=Object.keys(ca).map(function(ta){return ta+"="+encodeURIComponent(ca[ta])}),W=ha+"/pagead/landing?"+X.join("&");Qc(W);v&&g!==void 0&&Yo({targetId:g,request:{url:W,parameterEncoding:3,endpoint:E?12:13},Ya:{eventId:h.eventId,
priorityId:h.priorityId},rh:A===void 0?void 0:{eventId:A,priorityId:C}})}}var e=!!a.Si,f=!!a.De,g=a.targetId,h=a.D,m=a.yh===void 0?!0:a.yh,n=ru(),p=n.gclid||"",q=n.gclsrc,r=n.dclid||"",t=n.wbraid||"",u=!e&&((!p||q&&q!=="aw.ds"?!1:!0)||t),v=an();if(u||v)if(v){var w=[K.m.U,K.m.V,K.m.La];d();(function(){Q(w)||mp(function(y){d(!0,y.consentEventId,y.consentPriorityId)},w)})()}else d()},[K.m.U,K.m.V,K.m.La])},jw=function(){return vp("reported_gclid",function(){return{}})},kw=!1;function mw(a,b,c,d){var e=Gc(),f;if(e===1)a:{var g=fk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};
var rw=function(a,b){if(a)if(Hr()){}else if(kb(a)&&(a=Fp(a)),a){var c=void 0,d=!1,e=O(b,K.m.On);if(e&&Array.isArray(e)){c=[];for(var f=0;f<e.length;f++){var g=Fp(e[f]);g&&(c.push(g),(a.id===g.id||a.id===a.destinationId&&a.destinationId===g.destinationId)&&(d=!0))}}if(!c||d){var h=O(b,K.m.Lk),m;if(h){m=Array.isArray(h)?h:[h];var n=O(b,K.m.Jk),p=O(b,K.m.Kk),q=O(b,K.m.Mk),r=Go(O(b,K.m.Nn)),t=n||p,u=1;a.prefix!=="UA"||c||
(u=5);for(var v=0;v<m.length;v++)if(v<u)if(c)nw(c,m[v],r,b,{Cc:t,options:q});else if(a.prefix==="AW"&&a.ids[Hp[1]])F(155)?nw([a],m[v],r||"US",b,{Cc:t,options:q}):ow(a.ids[Hp[0]],a.ids[Hp[1]],m[v],b,{Cc:t,options:q});else if(a.prefix==="UA")if(F(155))nw([a],m[v],r||"US",b,{Cc:t});else{var w=a.destinationId,y=m[v],A={Cc:t};N(23);if(y){A=A||{};var C=pw(qw,A,w),E={};A.Cc!==void 0?E.receiver=A.Cc:E.replace=y;E.ga_wpid=w;E.destination=y;C(2,yb(),E)}}}}}},nw=function(a,b,c,d,e){N(21);if(b&&c){e=e||{};for(var f=
{countryNameCode:c,destinationNumber:b,retrievalTime:yb()},g=0;g<a.length;g++){var h=a[g];sw[h.id]||(h&&h.prefix==="AW"&&!f.adData&&h.ids.length>=2?(f.adData={ak:h.ids[Hp[0]],cl:h.ids[Hp[1]]},tw(f.adData,d),sw[h.id]=!0):h&&h.prefix==="UA"&&!f.gaData&&(f.gaData={gaWpid:h.destinationId},sw[h.id]=!0))}(f.gaData||f.adData)&&pw(uw,e,void 0,d)(e.Cc,f,e.options)}},ow=function(a,b,c,d,e){N(22);if(c){e=e||{};var f=pw(vw,e,a,d),g={ak:a,cl:b};e.Cc===void 0&&(g.autoreplace=c);tw(g,d);f(2,e.Cc,g,c,0,yb(),e.options)}},
tw=function(a,b){a.dma=Dr();Er()&&(a.dmaCps=Cr());vr(b)?a.npa="0":a.npa="1"},pw=function(a,b,c,d){var e=x;if(e[a.functionName])return b.tj&&B(b.tj),e[a.functionName];var f=ww();e[a.functionName]=f;if(a.additionalQueues)for(var g=0;g<a.additionalQueues.length;g++)e[a.additionalQueues[g]]=e[a.additionalQueues[g]]||ww();a.idKey&&e[a.idKey]===void 0&&(e[a.idKey]=c);jm({destinationId:jg.ctid,endpoint:0,eventId:d==null?void 0:d.eventId,priorityId:d==null?void 0:d.priorityId},mw("https://","http://",a.scriptUrl),
b.tj,b.Zp);return f},ww=function(){function a(){a.q=a.q||[];a.q.push(arguments)}return a},vw={functionName:"_googWcmImpl",idKey:"_googWcmAk",scriptUrl:"www.gstatic.com/wcm/loader.js"},qw={functionName:"_gaPhoneImpl",idKey:"ga_wpid",scriptUrl:"www.gstatic.com/gaphone/loader.js"},xw={Nm:"9",uo:"5"},uw={functionName:"_googCallTrackingImpl",additionalQueues:[qw.functionName,vw.functionName],scriptUrl:"www.gstatic.com/call-tracking/call-tracking_"+
(xw.Nm||xw.uo)+".js"},sw={};function yw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Iv(a,b)},setHitData:function(b,c){V(a,b,c)},setHitDataIfNotDefined:function(b,c){Iv(a,b)===void 0&&V(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return S(a,b)},setMetadata:function(b,c){U(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.D,b)},Ab:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return kd(c)?a.mergeHitDataForKey(b,c):!1}}};var Aw=function(a){var b=zw[a.target.destinationId];if(!a.isAborted&&b)for(var c=yw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Bw=function(a,b){var c=zw[a];c||(c=zw[a]=[]);c.push(b)},zw={};var Cw=function(a){if(Q(K.m.U)){a=a||{};dt(a,!1);var b,c=$t(a.prefix);if((b=ct[et(c)])&&!(zb()-b.Bh*1E3>18E5)){var d=b.id,e=d.split(".");if(e.length===2&&!(zb()-(Number(e[1])||0)*1E3>864E5))return d}}};function Dw(a,b){return arguments.length===1?Ew("set",a):Ew("set",a,b)}function Fw(a,b){return arguments.length===1?Ew("config",a):Ew("config",a,b)}function Gw(a,b,c){c=c||{};c[K.m.ld]=a;return Ew("event",b,c)}function Ew(){return arguments};var Hw=function(){var a=tc&&tc.userAgent||"";if(a.indexOf("Safari")<0||/Chrome|Coast|Opera|Edg|Silk|Android/.test(a))return!1;var b=(/Version\/([\d\.]+)/.exec(a)||[])[1]||"";if(b==="")return!1;for(var c=["14","1","1"],d=b.split("."),e=0;e<d.length;e++){if(c[e]===void 0)return!0;if(d[e]!==c[e])return Number(d[e])>Number(c[e])}return d.length>=c.length};var Iw=function(){this.messages=[];this.C=[]};Iw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Iw.prototype.listen=function(a){this.C.push(a)};
Iw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Iw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Jw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[R.A.hb]=jg.canonicalContainerId;Kw().enqueue(a,b,c)}
function Lw(){var a=Mw;Kw().listen(a)}function Kw(){return vp("mb",function(){return new Iw})};var Nw,Ow=!1;function Pw(){Ow=!0;Nw=Nw||{}}function Qw(a){Ow||Pw();return Nw[a]};function Rw(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Sw(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}
var bx=function(a){return a.tagName+":"+a.isVisible+":"+a.la.length+":"+ax.test(a.la)},px=function(a){a=a||{ze:!0,Ae:!0,Fh:void 0};a.Yb=a.Yb||{email:!0,phone:!1,address:!1};var b=cx(a),c=dx[b];if(c&&zb()-c.timestamp<200)return c.result;var d=ex(),e=d.status,f=[],g,h,m=[];if(!F(33)){if(a.Yb&&a.Yb.email){var n=fx(d.elements);f=gx(n,a&&a.Vf);g=hx(f);n.length>10&&(e="3")}!a.Fh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(ix(f[p],!!a.ze,!!a.Ae));m=m.slice(0,10)}else if(a.Yb){}g&&(h=ix(g,!!a.ze,!!a.Ae));var G={elements:m,
xj:h,status:e};dx[b]={timestamp:zb(),result:G};return G},qx=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},sx=function(a){var b=rx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},rx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},ox=function(a,b,c){var d=a.element,e={la:a.la,type:a.xa,tagName:d.tagName};b&&(e.querySelector=tx(d));c&&(e.isVisible=!Sw(d));return e},ix=function(a,b,c){return ox({element:a.element,la:a.la,xa:nx.hc},b,c)},cx=function(a){var b=!(a==null||!a.ze)+"."+!(a==null||!a.Ae);a&&a.Vf&&a.Vf.length&&(b+="."+a.Vf.join("."));a&&a.Yb&&(b+="."+a.Yb.email+"."+a.Yb.phone+"."+a.Yb.address);return b},hx=function(a){if(a.length!==0){var b;b=ux(a,function(c){return!vx.test(c.la)});b=ux(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=ux(b,function(c){return!Sw(c.element)});return b[0]}},gx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&vi(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},ux=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},tx=function(a){var b;if(a===z.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=tx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},fx=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(wx);if(f){var g=f[0],h;if(x.location){var m=Pk(x.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,la:g})}}}return b},ex=function(){var a=[],b=z.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(xx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(yx.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||F(33)&&zx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},wx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,ax=/@(gmail|googlemail)\./i,vx=/support|noreply/i,xx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),yx=
["BR"],Ax=ug('',2),nx={hc:"1",xd:"2",pd:"3",wd:"4",Je:"5",Nf:"6",gh:"7",Li:"8",Ih:"9",Gi:"10"},dx={},zx=["INPUT","SELECT"],Bx=rx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var ey=Number('')||5,fy=Number('')||50,gy=ob();
var iy=function(a,b){a&&(hy("sid",a.targetId,b),hy("cc",a.clientCount,b),hy("tl",a.totalLifeMs,b),hy("hc",a.heartbeatCount,b),hy("cl",a.clientLifeMs,b))},hy=function(a,b,c){b!=null&&c.push(a+"="+b)},jy=function(){var a=z.referrer;if(a){var b;return Nk(Tk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},ky="https://"+Ri(21,"www.googletagmanager.com")+"/a?",my=function(){this.R=ly;this.N=0};my.prototype.H=function(a,b,c,d){var e=jy(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&hy("si",a.gg,g);hy("m",0,g);hy("iss",f,g);hy("if",c,g);iy(b,g);d&&hy("fm",encodeURIComponent(d.substring(0,fy)),g);this.P(g);};my.prototype.C=function(a,b,c,d,e){var f=[];hy("m",1,f);hy("s",a,f);hy("po",jy(),f);b&&(hy("st",b.state,f),hy("si",b.gg,f),hy("sm",b.mg,f));iy(c,f);hy("c",d,f);e&&hy("fm",encodeURIComponent(e.substring(0,
fy)),f);this.P(f);};my.prototype.P=function(a){a=a===void 0?[]:a;!hl||this.N>=ey||(hy("pid",gy,a),hy("bc",++this.N,a),a.unshift("ctid="+jg.ctid+"&t=s"),this.R(""+ky+a.join("&")))};var ny=Number('')||500,oy=Number('')||5E3,py=Number('20')||10,qy=Number('')||5E3;function ry(a){return a.performance&&a.performance.now()||Date.now()}
var sy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{im:function(){},jm:function(){},hm:function(){},onFailure:function(){}}:h;this.yo=f;this.C=g;this.N=h;this.ba=this.ka=this.heartbeatCount=this.wo=0;this.hh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=ry(this.C);this.mg=ry(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Ba()};e.prototype.getState=function(){return{state:this.state,
gg:Math.round(ry(this.C)-this.gg),mg:Math.round(ry(this.C)-this.mg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.mg=ry(this.C))};e.prototype.Gl=function(){return String(this.wo++)};e.prototype.Ba=function(){var f=this;this.heartbeatCount++;this.Sa({type:0,clientId:this.id,requestId:this.Gl(),maxDelay:this.ih()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ba++,g.isDead||f.ba>py){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.vo();var n,p;(p=(n=f.N).hm)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Kl();else{if(f.heartbeatCount>g.stats.heartbeatCount+py){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.hh){var u,v;(v=(u=f.N).jm)==null||v.call(u)}else{f.hh=!0;var w,y;(y=(w=f.N).im)==null||y.call(w)}f.ba=0;f.zo();f.Kl()}}})};e.prototype.ih=function(){return this.state===2?
oy:ny};e.prototype.Kl=function(){var f=this;this.C.setTimeout(function(){f.Ba()},Math.max(0,this.ih()-(ry(this.C)-this.ka)))};e.prototype.Co=function(f,g,h){var m=this;this.Sa({type:1,clientId:this.id,requestId:this.Gl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Sa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Lf(t,7)},(p=f.maxDelay)!=null?p:qy),r={request:f,zm:g,tm:m,Wp:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ka=ry(this.C);f.tm=!1;this.yo(f.request)};e.prototype.zo=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.tm&&this.sendRequest(h)}};e.prototype.vo=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Lf(this.H[g.value],this.R)};e.prototype.Lf=function(f,g){this.qb(f);var h=f.request;h.failure={failureType:g};f.zm(h)};e.prototype.qb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Wp)};e.prototype.Dp=function(f){this.ka=ry(this.C);var g=this.H[f.requestId];if(g)this.qb(g),g.zm(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var ty;
var uy=function(){ty||(ty=new my);return ty},ly=function(a){nn(pn(Pm.X.Oc),function(){Jc(a)})},vy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},wy=function(a){var b=a,c=Oj.Ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},xy=function(a){var b=un(qn.Z.zl);return b&&b[a]},yy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.To(a);x.setTimeout(function(){f.initialize()},1E3);B(function(){f.Np(a,b,e)})};k=yy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),gg:this.initTime,mg:Math.round(zb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Co(a,b,c)};k.getState=function(){return this.N.getState().state};k.Np=function(a,b,c){var d=x.location.origin,e=this,
f=Hc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?vy(h):"",p;F(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Hc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Dp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.To=function(a){var b=this,c=sy(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{im:function(){b.P=!0;b.H.H(c.getState(),c.stats)},jm:function(){},hm:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function zy(){var a=ig(fg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Ay(a,b){var c=Math.round(zb());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!zy()||F(168))return;nk()&&(a=""+d+mk()+"/_/service_worker");var e=wy(a);if(e===null||xy(e.origin))return;if(!uc()){uy().H(void 0,void 0,6);return}var f=new yy(e,!!a,c||Math.round(zb()),uy(),b);vn(qn.Z.zl)[e.origin]=f;}
var By=function(a,b,c,d){var e;if((e=xy(a))==null||!e.delegate){var f=uc()?16:6;uy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}xy(a).delegate(b,c,d);};
function Cy(a,b,c,d,e){var f=wy();if(f===null){d(uc()?16:6);return}var g,h=(g=xy(f.origin))==null?void 0:g.initTime,m=Math.round(zb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);By(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Dy(a,b,c,d){var e=wy(a);if(e===null){d("_is_sw=f"+(uc()?16:6)+"te");return}var f=b?1:0,g=Math.round(zb()),h,m=(h=xy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;F(169)&&(p=!0);By(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=xy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Ey(a){if(F(10)||nk()||Oj.N||al(a.D)||F(168))return;Ay(void 0,F(131));};var Fy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Gy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Hy(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=Object.assign({},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Iy(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function Jy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Ky(a){if(!Jy(a))return null;var b=Gy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Fy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var My=function(a,b){if(a)for(var c=Ly(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;V(b,f,c[f])}},Ly=function(a){var b={};b[K.m.uf]=a.architecture;b[K.m.vf]=a.bitness;a.fullVersionList&&(b[K.m.wf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[K.m.xf]=a.mobile?"1":"0";b[K.m.yf]=a.model;b[K.m.zf]=a.platform;b[K.m.Af]=a.platformVersion;b[K.m.Bf]=a.wow64?"1":"0";return b},Ny=function(a){var b=0,c=function(h,
m){try{a(h,m)}catch(n){}},d=x,e=Hy(d);if(e)c(e);else{var f=Iy(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var g=d.setTimeout(function(){c.hg||(c.hg=!0,N(106),c(null,Error("Timeout")))},b);f.then(function(h){c.hg||(c.hg=!0,N(104),d.clearTimeout(g),c(h))}).catch(function(h){c.hg||(c.hg=!0,N(105),d.clearTimeout(g),c(null,h))})}else c(null)}},Py=function(){var a=x;if(Jy(a)&&(Oy=zb(),!Iy(a))){var b=Ky(a);b&&(b.then(function(){N(95)}),b.catch(function(){N(96)}))}},Oy;function Qy(a){var b=a.location.href;if(a===a.top)return{url:b,Sp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Sp:c}};
var Ry=function(){return[K.m.U,K.m.V]},Sy=function(a){F(24)&&a.eventName===K.m.qa&&Rv(a,L.J.Ga)&&!S(a,R.A.fa)&&!a.D.isGtmEvent?rw(a.target,a.D):Rv(a,L.J.Lj)&&(rw(a.target,a.D),a.isAborted=!0)},Uy=function(a){var b;if(a.eventName!=="gtag.config"&&S(a,R.A.yl))switch(S(a,R.A.ia)){case L.J.Ja:b=97;Ty(a);break;case L.J.Ta:b=98;Ty(a);break;case L.J.W:b=99}!S(a,R.A.Ha)&&b&&N(b);S(a,R.A.Ha)===!0&&(a.isAborted=!0)},Vy=function(a){if(!S(a,R.A.fa)&&F(30)&&Rv(a,[L.J.W])){var b=jv();iv(b)&&(V(a,K.m.kd,"1"),U(a,
R.A.qg,!0))}},Wy=function(a){Rv(a,[L.J.W])&&a.D.eventMetadata[R.A.ud]&&V(a,K.m.al,!0)},Xy=function(a){var b=Q(Ry());switch(S(a,R.A.ia)){case L.J.Ta:case L.J.Ja:a.isAborted=!b||!!S(a,R.A.fa);break;case L.J.na:a.isAborted=!b;break;case L.J.W:S(a,R.A.fa)&&V(a,K.m.fa,!0)}},Yy=function(a,b){if((Oj.C||F(168))&&Q(Ry())&&!bw(a,"ccd_enable_cm",!1)){var c=function(m){var n=S(a,R.A.Xg);n?n.push(m):U(a,R.A.Xg,[m])};F(62)&&c(102696396);if(F(63)||F(168)){c(102696397);var d=S(a,R.A.ib);U(a,R.A.bh,!0);U(a,R.A.ke,
!0);if(dj(d)){c(102780931);U(a,R.A.Ci,!0);var e=b||ws(),f={},g={eventMetadata:(f[R.A.sd]=L.J.Ja,f[R.A.ib]=d,f[R.A.Jl]=e,f[R.A.ke]=!0,f[R.A.bh]=!0,f[R.A.Ci]=!0,f[R.A.Xg]=[102696397,102780931],f),noGtmEvent:!0},h=Gw(a.target.destinationId,a.eventName,a.D.C);Jw(h,a.D.eventId,g);U(a,R.A.ib);return e}}}},Zy=function(a){if(Rv(a,[L.J.W])){var b=S(a,R.A.ya),c=Cw(b),d=Yy(a,c),e=c||d;if(e&&!Iv(a,K.m.Xa)){var f=ws(Iv(a,K.m.nc));V(a,K.m.Xa,f);db("GTAG_EVENT_FEATURE_CHANNEL",12)}e&&(V(a,K.m.uc,e),U(a,R.A.xl,!0))}},
$y=function(a){Ey(a)},az=function(a){if(Rv(a,[L.J.W,L.J.na,L.J.Ta,L.J.Ja])&&S(a,R.A.Wc)&&Q(K.m.U)){var b=S(a,R.A.ia)===L.J.na,c=!F(4);if(!b||c){var d=S(a,R.A.ia)===L.J.W&&a.eventName!==K.m.Cb,e=S(a,R.A.ya);dt(e,d);Q(K.m.V)&&V(a,K.m.Pb,bt[et(e.prefix)])}}},bz=function(a){Rv(a,[L.J.W,L.J.Ta,L.J.Ja])&&hw(a)},cz=function(a){Rv(a,[L.J.W])&&U(a,R.A.se,!!S(a,R.A.yc)&&!Q(Ry()))},dz=function(a){Rv(a,[L.J.W])&&Qs(!1)._up==="1"&&V(a,K.m.Pg,!0)},ez=function(a){if(Rv(a,[L.J.W,L.J.na])){var b=Fv();b!==void 0&&
V(a,K.m.Cf,b||"error");var c=or();c&&V(a,K.m.jd,c);var d=nr();d&&V(a,K.m.nd,d)}},fz=function(a){if(Rv(a,[L.J.W,L.J.na])){var b=x;if(b.__gsaExp&&b.__gsaExp.id){var c=b.__gsaExp.id;if(jb(c))try{var d=Number(c());isNaN(d)||V(a,K.m.Ck,d)}catch(e){}}}},gz=function(a){Aw(a);},hz=function(a){F(47)&&Rv(a,L.J.W)&&(a.copyToHitData(K.m.Sh),a.copyToHitData(K.m.Th),a.copyToHitData(K.m.Rh))},iz=function(a){Rv(a,L.J.W)&&(a.copyToHitData(K.m.lf),
a.copyToHitData(K.m.Ze),a.copyToHitData(K.m.tf),a.copyToHitData(K.m.Jg),a.copyToHitData(K.m.Yd),a.copyToHitData(K.m.cf))},jz=function(a){if(Rv(a,[L.J.W,L.J.na,L.J.Ta,L.J.Ja])){var b=a.D;if(Rv(a,[L.J.W,L.J.na])){var c=O(b,K.m.Vb);c!==!0&&c!==!1||V(a,K.m.Vb,c)}vr(b)?V(a,K.m.xc,!1):(V(a,K.m.xc,!0),Rv(a,L.J.na)&&(a.isAborted=!0))}},kz=function(a){if(Rv(a,[L.J.W,L.J.na])){var b=S(a,R.A.ia)===L.J.W;b&&a.eventName!==K.m.kb||(a.copyToHitData(K.m.sa),b&&(a.copyToHitData(K.m.Eg),a.copyToHitData(K.m.Cg),a.copyToHitData(K.m.Dg),
a.copyToHitData(K.m.Bg),V(a,K.m.gk,a.eventName),F(113)&&(a.copyToHitData(K.m.he),a.copyToHitData(K.m.ee),a.copyToHitData(K.m.fe))))}},lz=function(a){var b=a.D;if(!F(6)){var c=b.getMergedValues(K.m.oa);V(a,K.m.Qg,Jb(kd(c)?c:{}))}var d=b.getMergedValues(K.m.oa,1,Eo(Fq.C[K.m.oa])),e=b.getMergedValues(K.m.oa,2);V(a,K.m.Ub,Jb(kd(d)?d:{},"."));V(a,K.m.Tb,Jb(kd(e)?e:{},"."))},mz=function(a){if(a!=null){var b=String(a).substring(0,512),c=b.indexOf("#");return c===-1?b:b.substring(0,c)}return""},nz=function(a){Rv(a,
L.J.W)&&Q(K.m.U)&&dw(a)},oz=function(a){if(a.eventName===K.m.Cb&&!a.D.isGtmEvent){if(!S(a,R.A.fa)&&Rv(a,L.J.W)){var b=O(a.D,K.m.Ic);if(typeof b!=="function")return;var c=String(O(a.D,K.m.qc)),d=Iv(a,c),e=O(a.D,c);c===K.m.lb||c===K.m.Pb?Qv({Km:c,callback:b,fm:e},S(a,R.A.ya),S(a,R.A.yc),ev):b(d||e)}a.isAborted=!0}},pz=function(a){if(!bw(a,"hasPreAutoPiiCcdRule",!1)&&Rv(a,L.J.W)&&Q(K.m.U)){var b=O(a.D,K.m.Yh)||{},c=String(Iv(a,K.m.nc)),d=b[c],e=Iv(a,K.m.Ye),f;if(!(f=Hk(d)))if(oo()){var g=Qw("AW-"+e);
f=!!g&&!!g.preAutoPii}else f=!1;if(f){var h=zb(),m=px({ze:!0,Ae:!0,Fh:!0});if(m.elements.length!==0){for(var n=[],p=0;p<m.elements.length;++p){var q=m.elements[p];n.push(q.querySelector+"*"+bx(q)+"*"+q.type)}V(a,K.m.ni,n.join("~"));var r=m.xj;r&&(V(a,K.m.oi,r.querySelector),V(a,K.m.mi,bx(r)));V(a,K.m.li,String(zb()-h));V(a,K.m.ri,m.status)}}}},qz=function(a){if(a.eventName===K.m.qa&&!S(a,R.A.fa)&&(U(a,R.A.co,!0),Rv(a,L.J.W)&&U(a,R.A.Ha,!0),Rv(a,L.J.na)&&(O(a.D,K.m.bd)===!1||O(a.D,K.m.pb)===!1)&&U(a,
R.A.Ha,!0),Rv(a,L.J.Ei))){var b=O(a.D,K.m.Pa)||{},c=O(a.D,K.m.Eb),d=S(a,R.A.Wc),e=S(a,R.A.hb),f=S(a,R.A.yc),g={we:d,Be:b,Ge:c,Ma:e,D:a.D,De:f,Jm:O(a.D,K.m.Qa)},h=S(a,R.A.ya);Lv(g,h);rw(a.target,a.D);var m={Si:!1,De:f,targetId:a.target.id,D:a.D,Rc:d?h:void 0,yh:d,Sl:Iv(a,K.m.Qg),bj:Iv(a,K.m.Ub),Xi:Iv(a,K.m.Tb),fj:Iv(a,K.m.Jc)};lw(m);a.isAborted=!0}},rz=function(a){Rv(a,[L.J.W,L.J.na])&&(a.D.isGtmEvent?S(a,R.A.ia)!==L.J.W&&a.eventName&&V(a,K.m.hd,a.eventName):V(a,K.m.hd,a.eventName),sb(a.D.C,function(b,
c){si[b.split(".")[0]]||V(a,b,c)}))},sz=function(a){if(!S(a,R.A.bh)){var b=!S(a,R.A.yl)&&Rv(a,[L.J.W,L.J.Ja]),c=!bw(a,"ccd_add_1p_data",!1)&&Rv(a,L.J.Ta);if((b||c)&&Q(K.m.U)){var d=S(a,R.A.ia)===L.J.W,e=a.D,f=void 0,g=O(e,K.m.eb);if(d){var h=O(e,K.m.Ag)===!0,m=O(e,K.m.Yh)||{},n=String(Iv(a,K.m.nc)),p=m[n];p&&db("GTAG_EVENT_FEATURE_CHANNEL",19);if(a.D.isGtmEvent&&p===void 0)return;if(h||p){var q;var r;p?r=Ek(p,g):(r=x.enhanced_conversion_data)&&db("GTAG_EVENT_FEATURE_CHANNEL",8);var t=(p||{}).enhanced_conversions_mode,
u;if(r){if(t==="manual")switch(r._tag_mode){case "CODE":u="c";break;case "AUTO":u="a";break;case "MANUAL":u="m";break;default:u="c"}else u=t==="automatic"?Hk(p)?"a":"m":"c";q={la:r,Im:u}}else q={la:r,Im:void 0};var v=q,w=v.Im;f=v.la;V(a,K.m.wc,w)}}U(a,R.A.ib,f)}}},tz=function(a){if(bw(a,"ccd_add_1p_data",!1)&&Q(Ry())){var b=a.D.H[K.m.Vg];if(Fk(b)){var c=O(a.D,K.m.eb);if(c===null)U(a,R.A.ue,null);else if(b.enable_code&&kd(c)&&U(a,R.A.ue,c),kd(b.selectors)){var d={};U(a,R.A.nh,Dk(b.selectors,d));F(60)&&
a.mergeHitDataForKey(K.m.rc,{ec_data_layer:Ak(d)})}}}},uz=function(a){U(a,R.A.Wc,O(a.D,K.m.Oa)!==!1);U(a,R.A.ya,Gv(a));U(a,R.A.yc,O(a.D,K.m.za)!=null&&O(a.D,K.m.za)!==!1);U(a,R.A.Hh,vr(a.D))},vz=function(a){if(Rv(a,[L.J.W,L.J.na])&&!F(189)&&F(34)){var b=function(d){return F(35)?(db("fdr",d),!0):!1};if(Q(K.m.U)||b(0))if(Q(K.m.V)||b(1))if(O(a.D,K.m.mb)!==!1||b(2))if(vr(a.D)||b(3))if(O(a.D,K.m.bd)!==!1||b(4)){var c;F(36)?c=a.eventName===K.m.qa?O(a.D,K.m.pb):void 0:c=O(a.D,K.m.pb);if(c!==!1||b(5))if(Ml()||
b(6))F(35)&&hb()?(V(a,K.m.nk,gb("fdr")),delete cb.fdr):(V(a,K.m.pk,"1"),U(a,R.A.jh,!0))}}},wz=function(a){Rv(a,[L.J.W])&&Q(K.m.V)&&(x._gtmpcm===!0||Hw()?V(a,K.m.dd,"2"):F(39)&&Ll("attribution-reporting")&&V(a,K.m.dd,"1"))},xz=function(a){if(!Jy(x))N(87);else if(Oy!==void 0){N(85);var b=Hy(x);b?My(b,a):N(86)}},yz=function(a){if(Rv(a,[L.J.W,L.J.na,L.J.Ga,L.J.Ta,L.J.Ja])&&Q(K.m.V)){a.copyToHitData(K.m.Qa);var b=un(qn.Z.Bl);if(b===void 0)tn(qn.Z.Cl,!0);else{var c=un(qn.Z.kh);V(a,K.m.rf,c+"."+b)}}},zz=
function(a){Rv(a,[L.J.W,L.J.na])&&(a.copyToHitData(K.m.Xa),a.copyToHitData(K.m.Fa),a.copyToHitData(K.m.Va))},Az=function(a){if(!S(a,R.A.fa)&&Rv(a,[L.J.W,L.J.na])){var b=Il(!1);V(a,K.m.Jc,b);var c=O(a.D,K.m.Aa);c||(c=b===1?x.top.location.href:x.location.href);V(a,K.m.Aa,mz(c));a.copyToHitData(K.m.Wa,z.referrer);V(a,K.m.Db,Jv());a.copyToHitData(K.m.zb);var d=Rw();V(a,K.m.Nc,d.width+"x"+d.height);var e=Kl(),f=Qy(e);f.url&&c!==f.url&&V(a,K.m.ji,mz(f.url))}},Bz=function(a){Rv(a,[L.J.W,L.J.na])},Cz=function(a){if(Rv(a,
[L.J.W,L.J.na,L.J.Ta,L.J.Ja])){var b=Iv(a,K.m.nc),c=O(a.D,K.m.Oh)===!0;c&&U(a,R.A.qo,!0);switch(S(a,R.A.ia)){case L.J.W:!c&&b&&Ty(a);(Gk()||Bc())&&U(a,R.A.me,!0);Gk()||Bc()||U(a,R.A.Bi,!0);break;case L.J.Ta:case L.J.Ja:!c&&b&&(a.isAborted=!0);break;case L.J.na:!c&&b||Ty(a)}Rv(a,[L.J.W,L.J.na])&&(S(a,R.A.me)?V(a,K.m.xi,"www.google.com"):V(a,K.m.xi,"www.googleadservices.com"))}},Dz=function(a){var b=a.target.ids[Hp[0]];if(b){V(a,K.m.Ye,b);var c=a.target.ids[Hp[1]];c&&V(a,K.m.nc,c)}else a.isAborted=
!0},Ty=function(a){S(a,R.A.Dl)||U(a,R.A.Ha,!1)};function Gz(a,b){var c=!!nk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?mk()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?F(90)&&no()?Ez():""+mk()+"/ag/g/c":Ez();case 16:return c?F(90)&&no()?Fz():""+mk()+"/ga/g/c":Fz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
mk()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?mk()+"/d/pagead/form-data":F(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Do+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?mk()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return(F(207)?c:c&&b.Ah)?mk()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?mk()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return(F(207)?c:c&&b.Ah)?mk()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?mk()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return F(205)?
"https://www.google.com/measurement/conversion/":c?mk()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return(F(207)?c:c&&b.Ah)?mk()+"/d/ccm/form-data":F(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:lc(a,"Unknown endpoint")}};function Hz(a){a=a===void 0?[]:a;return Pj(a).join("~")}function Iz(){if(!F(118))return"";var a,b;return(((a=Dm(sm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Jz(a,b){b&&sb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var Lz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Iv(a,g),m=Kz[g];m&&h!==void 0&&h!==""&&(!S(a,R.A.se)||g!==K.m.Zc&&g!==K.m.fd&&g!==K.m.Xd&&g!==K.m.Pe||(h="0"),d(m,h))}d("gtm",Mr({Ma:S(a,R.A.hb)}));wr()&&d("gcs",xr());d("gcd",Br(a.D));Er()&&d("dma_cps",Cr());d("dma",Dr());$q(hr())&&d("tcfd",Fr());Hz()&&d("tag_exp",Hz());Iz()&&d("ptag_exp",Iz());if(S(a,R.A.qg)){d("tft",
zb());var n=Yc();n!==void 0&&d("tfd",Math.round(n))}F(24)&&d("apve","1");(F(25)||F(26))&&d("apvf",Vc()?F(26)?"f":"sb":"nf");gn[Pm.X.Da]!==Om.Ia.oe||kn[Pm.X.Da].isConsentGranted()||(c.limited_ads="1");b(c)},Mz=function(a,b,c){var d=b.D;Yo({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Ya:{eventId:d.eventId,priorityId:d.priorityId},rh:{eventId:S(b,R.A.He),priorityId:S(b,R.A.Ie)}})},Nz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.D.eventId,
priorityId:b.D.priorityId};Mz(a,b,c);im(d,a,void 0,{Dh:!0,method:"GET"},function(){},function(){hm(d,a+"&img=1")})},Oz=function(a){var b=Bc()||zc()?"www.google.com":"www.googleadservices.com",c=[];sb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Pz=function(a){Lz(a,function(b){if(S(a,R.A.ia)===L.J.Ga){var c=[];a.target.destinationId&&c.push("tid="+a.target.destinationId);
sb(b,function(r,t){c.push(r+"="+t)});var d=Q([K.m.U,K.m.V])?45:46,e=Gz(d)+"?"+c.join("&");Mz(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(F(26)&&Vc()){im(g,e,void 0,{Dh:!0},function(){},function(){hm(g,e+"&img=1")});var h=Q([K.m.U,K.m.V]),m=Iv(a,K.m.kd)==="1",n=Iv(a,K.m.Qh)==="1";if(h&&m&&!n){var p=Oz(b),q=Bc()||zc()?58:57;Nz(p,a,q)}}else gm(g,e)||hm(g,e+"&img=1");if(jb(a.D.onSuccess))a.D.onSuccess()}})},Qz={},Kz=(Qz[K.m.fa]="gcu",
Qz[K.m.mc]="gclgb",Qz[K.m.lb]="gclaw",Qz[K.m.Ne]="gad_source",Qz[K.m.Oe]="gad_source_src",Qz[K.m.Zc]="gclid",Qz[K.m.fk]="gclsrc",Qz[K.m.Pe]="gbraid",Qz[K.m.Xd]="wbraid",Qz[K.m.Pb]="auid",Qz[K.m.hk]="rnd",Qz[K.m.Qh]="ncl",Qz[K.m.Uh]="gcldc",Qz[K.m.fd]="dclid",Qz[K.m.Tb]="edid",Qz[K.m.hd]="en",Qz[K.m.jd]="gdpr",Qz[K.m.Ub]="gdid",Qz[K.m.be]="_ng",Qz[K.m.ff]="gpp_sid",Qz[K.m.hf]="gpp",Qz[K.m.jf]="_tu",Qz[K.m.Dk]="gtm_up",Qz[K.m.Jc]="frm",Qz[K.m.kd]="lps",Qz[K.m.Qg]="did",Qz[K.m.Gk]="navt",Qz[K.m.Aa]=
"dl",Qz[K.m.Wa]="dr",Qz[K.m.Db]="dt",Qz[K.m.Nk]="scrsrc",Qz[K.m.rf]="ga_uid",Qz[K.m.nd]="gdpr_consent",Qz[K.m.ii]="u_tz",Qz[K.m.Qa]="uid",Qz[K.m.Cf]="us_privacy",Qz[K.m.xc]="npa",Qz);var Rz={O:{ro:0,Jj:1,sg:2,Pj:3,Jh:4,Nj:5,Oj:6,Qj:7,Kh:8,Tk:9,Sk:10,si:11,Uk:12,Wg:13,Xk:14,Mf:15,oo:16,te:17,Mi:18,Ni:19,Oi:20,Hl:21,Pi:22,Wm:23,Vm:24}};Rz.O[Rz.O.ro]="RESERVED_ZERO";Rz.O[Rz.O.Jj]="ADS_CONVERSION_HIT";Rz.O[Rz.O.sg]="CONTAINER_EXECUTE_START";Rz.O[Rz.O.Pj]="CONTAINER_SETUP_END";Rz.O[Rz.O.Jh]="CONTAINER_SETUP_START";Rz.O[Rz.O.Nj]="CONTAINER_BLOCKING_END";Rz.O[Rz.O.Oj]="CONTAINER_EXECUTE_END";Rz.O[Rz.O.Qj]="CONTAINER_YIELD_END";Rz.O[Rz.O.Kh]="CONTAINER_YIELD_START";Rz.O[Rz.O.Tk]="EVENT_EXECUTE_END";
Rz.O[Rz.O.Sk]="EVENT_EVALUATION_END";Rz.O[Rz.O.si]="EVENT_EVALUATION_START";Rz.O[Rz.O.Uk]="EVENT_SETUP_END";Rz.O[Rz.O.Wg]="EVENT_SETUP_START";Rz.O[Rz.O.Xk]="GA4_CONVERSION_HIT";Rz.O[Rz.O.Mf]="PAGE_LOAD";Rz.O[Rz.O.oo]="PAGEVIEW";Rz.O[Rz.O.te]="SNIPPET_LOAD";Rz.O[Rz.O.Mi]="TAG_CALLBACK_ERROR";Rz.O[Rz.O.Ni]="TAG_CALLBACK_FAILURE";Rz.O[Rz.O.Oi]="TAG_CALLBACK_SUCCESS";Rz.O[Rz.O.Hl]="TAG_EXECUTE_END";Rz.O[Rz.O.Pi]="TAG_EXECUTE_START";Rz.O[Rz.O.Wm]="CUSTOM_PERFORMANCE_START";Rz.O[Rz.O.Vm]="CUSTOM_PERFORMANCE_END";var Sz={};Sz.O=Rz.O;var Tz={hr:"L",so:"S",yr:"Y",Mq:"B",Wq:"E",ar:"I",vr:"TC",Zq:"HTC"},Uz={so:"S",Vq:"V",Pq:"E",ur:"tag"},Vz={},Wz=(Vz[Sz.O.Ni]="6",Vz[Sz.O.Oi]="5",Vz[Sz.O.Mi]="7",Vz);function Xz(){function a(c,d){var e=gb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Yz={},Zz={};var $z=[];var aA=!1;
function tA(a){}function uA(a){}
function vA(){}function wA(a){}
function xA(a){}function yA(a){}
function zA(){}function AA(a,b){}
function BA(a,b,c){}
function CA(){};var DA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function EA(a,b,c,d,e,f,g){var h=Object.assign({},DA);c&&(h.body=c,h.method="POST");Object.assign(h,e);x.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});FA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():F(128)&&(b+="&_z=retryFetch",c?gm(a,b,c):fm(a,b))})};var GA=function(a){this.P=a;this.C=""},HA=function(a,b){a.H=b;return a},IA=function(a,b){a.N=b;return a},FA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}JA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},KA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};JA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},JA=function(a,b){b&&(LA(b.send_pixel,b.options,a.P),LA(b.create_iframe,b.options,a.H),LA(b.fetch,b.options,a.N))};function MA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function LA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=kd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};
var NA=function(a,b){return S(a,R.A.Bi)&&(b===3||b===6)},OA=function(a){return new GA(function(b,c){var d;if(c.fallback_url){var e=c.fallback_url,f=c.fallback_url_method;d=function(){switch(f){case "send_pixel":hm(a,e);break;default:im(a,e)}}}hm(a,b,void 0,d)})},PA=function(a){if(a!==void 0)return Math.round(a/10)*10},QA=function(a){for(var b={},c=0;c<a.length;c++){var d=a[c],e=void 0;if(d.hasOwnProperty("google_business_vertical")){e=d.google_business_vertical;var f={};b[e]=b[e]||(f.google_business_vertical=
e,f)}else e="",b.hasOwnProperty(e)||(b[e]={});var g=b[e],h;for(h in d)h!=="google_business_vertical"&&(h in g||(g[h]=[]),g[h].push(d[h]))}return Object.keys(b).map(function(m){return b[m]})},RA=function(a){var b=Iv(a,K.m.sa);if(!b||!b.length)return[];for(var c=[],d=0;d<b.length;++d){var e=b[d];if(e){var f={};c.push((f.id=fi(e),f.origin=e.origin,f.destination=e.destination,f.start_date=e.start_date,f.end_date=e.end_date,f.location_id=e.location_id,f.google_business_vertical=e.google_business_vertical,
f))}}return c},fi=function(a){a.item_id!=null&&(a.id!=null?(N(138),a.id!==a.item_id&&N(148)):N(153));return F(20)?gi(a):a.id},TA=function(a){if(!a||typeof a!=="object"||typeof a.join==="function")return"";var b=[];sb(a,function(c,d){var e,f;if(Array.isArray(d)){for(var g=[],h=0;h<d.length;++h){var m=SA(d[h]);m!==void 0&&g.push(m)}f=g.length!==0?g.join(","):void 0}else f=SA(d);e=f;var n=SA(c);n&&e!==void 0&&b.push(n+"="+e)});return b.join(";")},SA=function(a){var b=typeof a;if(a!=null&&b!=="object"&&
b!=="function")return String(a).replace(/,/g,"\\,").replace(/;/g,"\\;").replace(/=/g,"\\=")},UA=function(a,b){var c=[],d=function(g,h){var m=Bg[g]===!0;h==null||!m&&h===""||(h===!0&&(h=1),h===!1&&(h=0),c.push(g+"="+encodeURIComponent(h)))},e=S(a,R.A.ia);if(e===L.J.W||e===L.J.na||e===L.J.Ef){var f=b.random||S(a,R.A.fb);d("random",f);delete b.random}sb(b,d);return c.join("&")},VA=function(a,b,c){if(!Hr()&&S(a,R.A.jh)){S(a,R.A.ia)===L.J.W&&(b.ct_cookie_present=0);var d=UA(a,b);return{zc:"https://td.doubleclick.net/td/rul/"+
c+"?"+d,format:4,Na:!1,endpoint:44}}},XA=function(a,b){var c=Q(WA)?54:55,d=Gz(c),e=UA(a,b);return{zc:d+"?"+e,format:5,Na:!0,endpoint:c}},YA=function(a,b,c){var d=!!S(a,R.A.ke),e=Gz(21,{Ah:d}),f=UA(a,b);return{zc:cl(e+"/"+c+"?"+f),format:1,Na:!0,endpoint:21}},ZA=function(a,b,c){var d=UA(a,b);return{zc:Gz(11)+"/"+c+"?"+d,format:1,Na:!0,endpoint:11}},aB=function(a,b,c){if(S(a,R.A.me)&&Q(WA))return $A(a,b,c,"&gcp=1&ct_cookie_present=1",2)},cB=function(a,b,c){if(S(a,R.A.xl)){var d=22;Q(WA)?S(a,R.A.me)&&
(d=23):d=60;var e=!!S(a,R.A.ke);S(a,R.A.bh)&&(b=Object.assign({},b),delete b.item);var f=UA(a,b),g=bB(a),h=Gz(d,{Ah:e})+"/"+c+"/?"+(""+f+g);e&&(h=cl(h));return{zc:h,format:2,Na:!0,endpoint:d}}},dB=function(a,b,c,d){for(var e=[],f=b.data||"",g=0;g<d.length;g++){var h=TA(d[g]);b.data=""+f+(f&&h?";":"")+h;e.push($A(a,b,c));var m=VA(a,b,c);m&&e.push(m);U(a,R.A.fb,S(a,R.A.fb)+1)}return e},fB=function(a,b,c){if(nk()&&F(148)&&Q(WA)){var d=eB(a).endpoint,e=S(a,R.A.fb)+1;b=Object.assign({},b,{random:e,adtest:"on",
exp_1p:"1"});var f=UA(a,b),g=bB(a),h;a:{switch(d){case 5:h=mk()+"/as/d/pagead/conversion";break a;case 6:h=mk()+"/gs/pagead/conversion";break a;case 8:h=mk()+"/g/d/pagead/1p-conversion";break a;default:lc(d,"Unknown endpoint")}h=void 0}return{zc:h+"/"+c+"/?"+f+g,format:3,Na:!0,endpoint:d}}},$A=function(a,b,c,d,e){d=d===void 0?"":d;var f=Gz(9),g=UA(a,b);return{zc:f+"/"+c+"/?"+g+d,format:e!=null?e:Hr()?2:3,Na:!0,endpoint:9}},gB=function(a,b,c){var d=eB(a).endpoint,e=Q(WA),f="&gcp=1&sscte=1&ct_cookie_present=1";
nk()&&F(148)&&Q(WA)&&(f="&exp_ph=1&gcp=1&sscte=1&ct_cookie_present=1",b=Object.assign({},b,{exp_1p:"1"}));var g=UA(a,b),h=bB(a),m=e?37:162,n={zc:Gz(d)+"/"+c+"/?"+g+h,format:F(m)?Hr()||!Vc()?2:e?6:5:Hr()?2:3,Na:!0,endpoint:d};Q(K.m.V)&&(n.attributes={attributionsrc:""});if(e&&S(a,R.A.Bi)){var p=F(175)?Gz(8):""+bl("https://www.google.com",!0,"")+"/pagead/1p-conversion";n.lp=p+"/"+c+"/"+("?"+g+f);n.Wf=8}return n},eB=function(a){var b="/pagead/conversion",c="https://www.googleadservices.com",d=5;Q(WA)?
S(a,R.A.me)&&(c="https://www.google.com",b="/pagead/1p-conversion",d=8):(c="https://pagead2.googlesyndication.com",d=6);return{Gr:c,Cr:b,endpoint:d}},bB=function(a){return S(a,R.A.me)?"&gcp=1&sscte=1&ct_cookie_present=1":""},hB=function(a,b){var c=S(a,R.A.ia),d=Iv(a,K.m.Ye),e=[],f=function(h){h&&e.push(h)};switch(c){case L.J.W:e.push(gB(a,b,d));f(fB(a,b,d));f(cB(a,b,d));f(aB(a,b,d));f(VA(a,b,d));break;case L.J.na:var g=QA(RA(a));g.length?e.push.apply(e,va(dB(a,b,d,g))):(e.push($A(a,b,d)),f(VA(a,b,
d)));break;case L.J.Ta:e.push(ZA(a,b,d));break;case L.J.Ja:e.push(YA(a,b,d));break;case L.J.Ef:e.push(XA(a,b))}return{Kp:e}},jB=function(a,b,c,d,e,f,g,h){var m=NA(c,b),n=Q(WA),p=S(c,R.A.ia);m||iB(a,c,e);uA(c.D.eventId);var q=function(){f&&(f(),m&&iB(a,c,e))},r={destinationId:c.target.destinationId,endpoint:e,priorityId:c.D.priorityId,eventId:c.D.eventId};switch(b){case 1:fm(r,a);f&&f();break;case 2:hm(r,a,q,g,h);break;case 3:var t=!1;try{t=lm(r,x,z,a,q,g,h)}catch(C){t=!1}t||jB(a,2,c,d,e,q,g,h);break;
case 4:var u="AW-"+Iv(c,K.m.Ye),v=Iv(c,K.m.nc);v&&(u=u+"/"+v);mm(r,a,u);break;case 5:var w=a;n||p!==L.J.W||(w=Yl(a,"fmt",8));im(r,w,void 0,void 0,f,g);break;case 6:var y=Yl(a,"fmt",7);il&&bm(r,2,y);var A={};"setAttributionReporting"in XMLHttpRequest.prototype&&(A={attributionReporting:kB});EA(r,y,void 0,OA(r),A,q,g)}},iB=function(a,b,c){var d=b.D;Yo({targetId:b.target.destinationId,request:{url:a,parameterEncoding:3,endpoint:c},Ya:{eventId:d.eventId,priorityId:d.priorityId},rh:{eventId:S(b,R.A.He),
priorityId:S(b,R.A.Ie)}})},lB=function(a,b){var c=!0;switch(a){case L.J.W:case L.J.Ja:c=!1;break;case L.J.Ta:c=!F(7)}return c?b.replace(/./g,"*"):b},mB=function(a){if(!Iv(a,K.m.Le)||!Iv(a,K.m.Me))return"";var b=Iv(a,K.m.Le).split("."),c=Iv(a,K.m.Me).split(".");if(!b.length||!c.length||b.length!==c.length)return"";for(var d=[],e=0;e<b.length;++e)d.push(b[e]+"_"+c[e]);return d.join(".")},pB=function(a,b,c){var d=cj(S(a,R.A.ib)),e=bj(d,c),f=e.Dj,g=e.ng,h=e.Za,m=e.cp,n=e.encryptionKeyString,p=[];nB(c)||
p.push("&em="+f);c===2&&p.push("&eme="+m);return{ng:g,Gq:p,Kr:d,Za:h,encryptionKeyString:n,Bq:function(q,r){return function(t){var u,v=r.zc;if(t){var w;w=S(a,R.A.hb);var y=Mr({Ma:w,Bm:t});v=v.replace(b.gtm,y)}u=v;if(c===1)oB(r,a,b,u,c,q)(sj(S(a,R.A.ib)));else{var A;var C=S(a,R.A.ib);A=c===0?qj(C,!1):c===2?qj(C,!0,!0):void 0;var E=oB(r,a,b,u,c,q);A?A.then(E):E(void 0)}}}}},oB=function(a,b,c,d,e,f){return function(g){if(!nB(e)){var h=(g==null?0:g.Kb)?g.Kb:nj({Tc:[]}).Kb;d+="&em="+encodeURIComponent(h)}jB(d,a.format,b,c,a.endpoint,a.Na?f:void 0,void 0,a.attributes)}},nB=function(a){return F(125)?!0:a!==2&&a!==3?!1:Oj.C&&F(19)||F(168)?!0:!1},rB=function(a,b,c){return function(d){var e=d.Kb;nB(d.Hb?2:0)||(b.em=e);if(d.Za&&d.time!==void 0){var f,g=PA(d.time);f=["t."+(g!=null?g:""),"l."+PA(e.length)].join("~");b._ht=f}d.Za&&qB(a,b,c);}},qB=function(a,b,c){if(a===L.J.Ja){var d=S(c,R.A.ya),e;if(!(e=S(c,R.A.Jl))){var f;f=d||{};var g;if(Q(K.m.U)){(g=Cw(f))||(g=ws());var h=et(f.prefix);ht(f,g);delete bt[h];delete ct[h];gt(h,f.path,f.domain);e=Cw(f)}else e=void 0}b.ecsid=e}},sB=function(a,b,c,d,e){if(a)try{rB(c,d,b)(a)}catch(f){}e(d)},tB=function(a,b,c,d,e){if(a)try{a.then(rB(c,d,b)).then(function(){e(d)});return}catch(f){}e(d)},uB=function(a){var b=bs(a);if(b&&b!==1)return b&
1023},vB=function(a,b,c){return a===void 0?!1:a>=b&&a<c},wB=function(a){var b=Xi.bp;return{Gj:F(208)||vB(a,512-b,512),No:vB(a,768-b,768),Oo:vB(a,1024-b,1024)}},xB=function(a){var b=Xi.ap;return{Gj:F(164)||vB(a,512-b,512),control:vB(a,1024-b,1024)}},AB=function(a){if(S(a,R.A.ia)===L.J.Ga)Pz(a);else{var b=F(22)?Bb(a.D.onFailure):void 0;yB(a,function(c,d){F(125)&&delete c.em;for(var e=hB(a,c).Kp,f=((d==null?void 0:d.Nr)||new zB(a)).H(e.filter(function(C){return C.Na}).length),g={},h=0;h<e.length;g={Zi:void 0,
Wf:void 0,Na:void 0,Ri:void 0,Wi:void 0},h++){var m=e[h],n=m.zc,p=m.format;g.Na=m.Na;g.Ri=m.attributes;g.Wi=m.endpoint;g.Zi=m.lp;g.Wf=m.Wf;var q=void 0,r=(q=d)==null?void 0:q.serviceWorker;if(r){var t=r.Bq(f,e[h]),u=r,v=u.ng,w=u.encryptionKeyString,y=""+n+u.Gq.join("");Cy(y,v,function(C){return function(E){iB(E.data,a,C.Wi);C.Na&&typeof f==="function"&&f()}}(g),t,w)}else{var A=b;g.Zi&&g.Wf&&(A=function(C){return function(){jB(C.Zi,5,a,c,C.Wf,C.Na?f:void 0,C.Na?b:void 0,C.Ri)}}(g));jB(n,p,a,c,g.Wi,
g.Na?f:void 0,g.Na?A:void 0,g.Ri)}}})}},kB={eventSourceEligible:!1,triggerEligible:!0},zB=function(a){this.C=1;this.onSuccess=a.D.onSuccess};zB.prototype.H=function(a){var b=this;return Kb(function(){b.N()},a||1)};zB.prototype.N=function(){this.C--;if(jb(this.onSuccess)&&this.C===0)this.onSuccess()};var WA=[K.m.U,K.m.V],yB=function(a,b){var c=S(a,R.A.ia),d={},e={},f=S(a,R.A.fb);c===L.J.W||c===L.J.na?(d.cv="11",d.fst=f,d.fmt=3,d.bg="ffffff",d.guid="ON",d.async="1",F(198)&&(d.en=a.eventName)):c===L.J.Ef&&
(d.cv="11",d.tid=a.target.destinationId,d.fst=f,d.fmt=6,d.en=a.eventName);if(c===L.J.W){var g=Xr()[Vr.Gb];g!=null&&g>0&&(d.gcl_ctr=g)}var h=Ku(["aw","dc"]);h!=null&&(d.gad_source=h);d.gtm=Mr({Ma:S(a,R.A.hb)});c!==L.J.na&&wr()&&(d.gcs=xr());d.gcd=Br(a.D);Er()&&(d.dma_cps=Cr());d.dma=Dr();$q(hr())&&(d.tcfd=Fr());var m=function(){var fc=(S(a,R.A.Xg)||[]).slice(0);return function(Sc){Sc!==void 0&&fc.push(Sc);if(Hz()||fc.length)d.tag_exp=Hz(fc)}}();m();Iz()&&(d.ptag_exp=Iz());gn[Pm.X.Da]!==Om.Ia.oe||kn[Pm.X.Da].isConsentGranted()||
(d.limited_ads="1");Iv(a,K.m.Nc)&&ci(Iv(a,K.m.Nc),d);if(Iv(a,K.m.zb)){var n=Iv(a,K.m.zb);n&&(n.length===2?di(d,"hl",n):n.length===5&&(di(d,"hl",n.substring(0,2)),di(d,"gl",n.substring(3,5))))}var p=S(a,R.A.se),q=function(fc,Sc){var If=Iv(a,Sc);If&&(d[fc]=p?Tu(If):If)};q("url",K.m.Aa);q("ref",K.m.Wa);q("top",K.m.ji);var r=mB(a);r&&(d.gclaw_src=r);for(var t=l(Object.keys(a.C)),u=t.next();!u.done;u=t.next()){var v=u.value,w=Iv(a,v);if(bi.hasOwnProperty(v)){var y=bi[v];y&&(d[y]=w)}else e[v]=w}Jz(d,Iv(a,
K.m.rd));var A=Iv(a,K.m.lf);A!==void 0&&A!==""&&(d.vdnc=String(A));var C=Iv(a,K.m.cf);C!==void 0&&(d.shf=C);var E=Iv(a,K.m.Yd);E!==void 0&&(d.delc=E);if(F(30)&&S(a,R.A.qg)){d.tft=zb();var G=Yc();G!==void 0&&(d.tfd=Math.round(G))}c!==L.J.Ef&&(d.data=TA(e));var I=Iv(a,K.m.sa);!I||c!==L.J.W&&c!==L.J.Ef||(d.iedeld=ji(I),d.item=ei(I));var M=Iv(a,K.m.rc);if(M&&typeof M==="object")for(var T=l(Object.keys(M)),ca=T.next();!ca.done;ca=T.next()){var P=ca.value;d["gap."+P]=M[P]}S(a,R.A.Ci)&&(d.aecs="1");if(c!==
L.J.W&&c!==L.J.Ta&&c!==L.J.Ja)b(d);else if(Q(K.m.V)&&Q(K.m.U)){var ha;a:switch(c){case L.J.Ta:ha=F(66);break a;case L.J.Ja:ha=!Oj.C&&F(68)||F(168)?!0:Oj.C;break a;default:ha=!1}ha&&U(a,R.A.ke,!0);var da=!!S(a,R.A.ke);if(S(a,R.A.ib)){var ka=uB(Iv(a,K.m.Pb)||"");if(c!==L.J.W){d.gtm=Mr({Ma:S(a,R.A.hb),Bm:3});var X=wB(ka),W=X.Gj,ta=X.No,ra=X.Oo;da||(W?m(104557470):ta?m(104557471):ra&&m(104557472));var na=pB(a,d,da?2:W?1:0);na.Za&&qB(c,d,a);b(d,{serviceWorker:na})}else{var Va=S(a,R.A.ib),Ya=xB(ka),rb=
Ya.Gj,ac=Ya.control;da||(rb?m(103308613):ac&&m(103308615));if(da||!rb){var Ib=qj(Va,da,void 0,void 0,ac);tB(Ib,a,c,d,b)}else sB(sj(Va),a,c,d,b)}}else b(d)}else d.ec_mode=void 0,b(d)};var BB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),CB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},DB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},EB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function FB(){var a=tk("gtm.allowlist")||tk("gtm.whitelist");a&&N(9);bk&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);BB.test(x.location&&x.location.hostname)&&(bk?N(116):(N(117),GB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Db(wb(a),CB),c=tk("gtm.blocklist")||tk("gtm.blacklist");c||(c=tk("tagTypeBlacklist"))&&N(3);c?N(8):c=[];BB.test(x.location&&x.location.hostname)&&(c=wb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
wb(c).indexOf("google")>=0&&N(2);var d=c&&Db(wb(c),DB),e={};return function(f){var g=f&&f[ff.Ra];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=jk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(bk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){N(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=pb(d,h||[]);t&&N(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:bk&&h.indexOf("cmpPartners")>=0?!HB():b&&b.indexOf("sandboxedScripts")!==-1?0:pb(d,EB))&&(u=!0);return e[g]=u}}function HB(){var a=ig(fg.C,jg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var GB=!1;GB=!0;function IB(a,b,c,d,e){if(!JB()&&!Im(a)){d.loadExperiments=Qj();rm(a,d,e);var f=KB(a),g=function(){tm().container[a]&&(tm().container[a].state=3);LB()},h={destinationId:a,endpoint:0};if(nk())jm(h,mk()+"/"+f,void 0,g);else{var m=Eb(a,"GTM-"),n=$k(),p=c?"/gtag/js":"/gtm.js",q=Zk(b,p+f);if(!q){var r=Sj.wg+p;n&&wc&&m&&(r=wc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=mw("https://","http://",r+f)}jm(h,q,void 0,g)}}}
function LB(){Km()||sb(Lm(),function(a,b){MB(a,b.transportUrl,b.context);N(92)})}
function MB(a,b,c,d){if(!JB()&&!Jm(a))if(c.loadExperiments||(c.loadExperiments=Qj()),Km()){var e;(e=tm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:sm()});tm().destination[a].state=0;um({ctid:a,isDestination:!0},d);N(91)}else{var f;(f=tm().destination)[a]!=null||(f[a]={context:c,state:1,parent:sm()});tm().destination[a].state=1;um({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(nk())jm(g,mk()+("/gtd"+KB(a,!0)));else{var h="/gtag/destination"+KB(a,!0),
m=Zk(b,h);m||(m=mw("https://","http://",Sj.wg+h));jm(g,m)}}}function KB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Vj!=="dataLayer"&&(c+="&l="+Vj);if(!Eb(a,"GTM-")||b)c=F(130)?c+(nk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Nr();$k()&&(c+="&sign="+Sj.Ji);var d=Oj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!F(191)&&Qj().join("~")&&(c+="&tag_exp="+Qj().join("~"));return c}
function JB(){if(Hr()){return!0}return!1};var NB=function(){this.H=0;this.C={}};NB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Fe:c};return d};NB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var PB=function(a,b){var c=[];sb(OB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Fe===void 0||b.indexOf(e.Fe)>=0)&&c.push(e.listener)});return c};function QB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:jg.ctid}};function RB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var TB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;SB(this,a,b)},UB=function(a,b,c,d){if(Xj.hasOwnProperty(b)||b==="__zone")return-1;var e={};kd(d)&&(e=ld(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},VB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},WB=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},SB=function(a,b,c){b!==void 0&&a.Sf(b);c&&x.setTimeout(function(){WB(a)},
Number(c))};TB.prototype.Sf=function(a){var b=this,c=Bb(function(){B(function(){a(jg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var XB=function(a){a.N++;return Bb(function(){a.H++;a.R&&a.H>=a.N&&WB(a)})},YB=function(a){a.R=!0;a.H>=a.N&&WB(a)};var ZB={};function $B(){return x[aC()]}
function aC(){return x.GoogleAnalyticsObject||"ga"}function dC(){var a=jg.ctid;}
function eC(a,b){return function(){var c=$B(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var kC=["es","1"],lC={},mC={};function nC(a,b){if(hl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";lC[a]=[["e",c],["eid",a]];xq(a)}}function oC(a){var b=a.eventId,c=a.Nd;if(!lC[b])return[];var d=[];mC[b]||d.push(kC);d.push.apply(d,va(lC[b]));c&&(mC[b]=!0);return d};var pC={},qC={},rC={};function sC(a,b,c,d){hl&&F(120)&&((d===void 0?0:d)?(rC[b]=rC[b]||0,++rC[b]):c!==void 0?(qC[a]=qC[a]||{},qC[a][b]=Math.round(c)):(pC[a]=pC[a]||{},pC[a][b]=(pC[a][b]||0)+1))}function tC(a){var b=a.eventId,c=a.Nd,d=pC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete pC[b];return e.length?[["md",e.join(".")]]:[]}
function uC(a){var b=a.eventId,c=a.Nd,d=qC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete qC[b];return e.length?[["mtd",e.join(".")]]:[]}function vC(){for(var a=[],b=l(Object.keys(rC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+rC[d])}return a.length?[["mec",a.join(".")]]:[]};var wC={},xC={};function yC(a,b,c){if(hl&&b){var d=dl(b);wC[a]=wC[a]||[];wC[a].push(c+d);var e=b[ff.Ra];if(!e)throw Error("Error: No function name given for function call.");var f=(Jf[e]?"1":"2")+d;xC[a]=xC[a]||[];xC[a].push(f);xq(a)}}function zC(a){var b=a.eventId,c=a.Nd,d=[],e=wC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=xC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete wC[b],delete xC[b]);return d};function AC(a,b,c){c=c===void 0?!1:c;BC().addRestriction(0,a,b,c)}function CC(a,b,c){c=c===void 0?!1:c;BC().addRestriction(1,a,b,c)}function DC(){var a=Am();return BC().getRestrictions(1,a)}var EC=function(){this.container={};this.C={}},FC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
EC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=FC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
EC.prototype.getRestrictions=function(a,b){var c=FC(this,b);if(a===0){var d,e;return[].concat(va((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),va((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(va((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),va((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
EC.prototype.getExternalRestrictions=function(a,b){var c=FC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};EC.prototype.removeExternalRestrictions=function(a){var b=FC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function BC(){return vp("r",function(){return new EC})};function GC(a,b,c,d){var e=Gf[a],f=HC(a,b,c,d);if(!f)return null;var g=Vf(e[ff.Al],c,[]);if(g&&g.length){var h=g[0];f=GC(h.index,{onSuccess:f,onFailure:h.Vl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function HC(a,b,c,d){function e(){function w(){ao(3);var M=zb()-I;yC(c.id,f,"7");VB(c.Pc,E,"exception",M);F(109)&&BA(c,f,Sz.O.Mi);G||(G=!0,h())}if(f[ff.ko])h();else{var y=Uf(f,c,[]),A=y[ff.Om];if(A!=null)for(var C=0;C<A.length;C++)if(!Q(A[C])){h();return}var E=UB(c.Pc,String(f[ff.Ra]),Number(f[ff.mh]),y[ff.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=zb()-I;yC(c.id,Gf[a],"5");VB(c.Pc,E,"success",M);F(109)&&BA(c,f,Sz.O.Oi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=zb()-
I;yC(c.id,Gf[a],"6");VB(c.Pc,E,"failure",M);F(109)&&BA(c,f,Sz.O.Ni);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);yC(c.id,f,"1");F(109)&&AA(c,f);var I=zb();try{Wf(y,{event:c,index:a,type:1})}catch(M){w(M)}F(109)&&BA(c,f,Sz.O.Hl)}}var f=Gf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Vf(f[ff.Il],c,[]);if(n&&n.length){var p=n[0],q=GC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Vl===
2?m:q}if(f[ff.ql]||f[ff.mo]){var r=f[ff.ql]?Hf:c.Eq,t=g,u=h;if(!r[a]){var v=IC(a,r,Bb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function IC(a,b,c){var d=[],e=[];b[a]=JC(d,e,c);return{onSuccess:function(){b[a]=KC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=LC;for(var f=0;f<e.length;f++)e[f]()}}}function JC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function KC(a){a()}function LC(a,b){b()};var OC=function(a,b){for(var c=[],d=0;d<Gf.length;d++)if(a[d]){var e=Gf[d];var f=XB(b.Pc);try{var g=GC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[ff.Ra];if(!h)throw Error("Error: No function name given for function call.");var m=Jf[h];c.push({Gm:d,priorityOverride:(m?m.priorityOverride||0:0)||RB(e[ff.Ra],1)||0,execute:g})}else MC(d,b),f()}catch(p){f()}}c.sort(NC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function PC(a,b){if(!OB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=PB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=XB(b);try{d[e](a,f)}catch(g){f()}}return!0}function NC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Gm,h=b.Gm;f=g>h?1:g<h?-1:0}return f}
function MC(a,b){if(hl){var c=function(d){var e=b.isBlocked(Gf[d])?"3":"4",f=Vf(Gf[d][ff.Al],b,[]);f&&f.length&&c(f[0].index);yC(b.id,Gf[d],e);var g=Vf(Gf[d][ff.Il],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var QC=!1,OB;function RC(){OB||(OB=new NB);return OB}
function SC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(F(109)){}if(d==="gtm.js"){if(QC)return!1;QC=!0}var e=!1,f=DC(),g=ld(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}nC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:TC(g,e),Eq:[],logMacroError:function(){N(6);ao(0)},cachedModelValues:UC(),Pc:new TB(function(){if(F(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};F(120)&&hl&&(n.reportMacroDiscrepancy=sC);F(109)&&xA(n.id);var p=ag(n);F(109)&&yA(n.id);e&&(p=VC(p));F(109)&&wA(b);var q=OC(p,n),r=PC(a,n.Pc);YB(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||dC();return WC(p,q)||r}function UC(){var a={};a.event=yk("event",1);a.ecommerce=yk("ecommerce",1);a.gtm=yk("gtm");a.eventModel=yk("eventModel");return a}
function TC(a,b){var c=FB();return function(d){if(c(d))return!0;var e=d&&d[ff.Ra];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Am();f=BC().getRestrictions(0,g);var h=a;b&&(h=ld(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=jk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function VC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Gf[c][ff.Ra]);if(Wj[d]||Gf[c][ff.no]!==void 0||RB(d,2))b[c]=!0}return b}function WC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Gf[c]&&!Xj[String(Gf[c][ff.Ra])])return!0;return!1};function XC(){RC().addListener("gtm.init",function(a,b){Oj.ba=!0;Ln();b()})};var YC=!1,ZC=0,$C=[];function aD(a){if(!YC){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){YC=!0;for(var e=0;e<$C.length;e++)B($C[e])}$C.push=function(){for(var f=za.apply(0,arguments),g=0;g<f.length;g++)B(f[g]);return 0}}}function bD(){if(!YC&&ZC<140){ZC++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");aD()}catch(c){x.setTimeout(bD,50)}}}
function cD(){var a=x;YC=!1;ZC=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")aD();else{Kc(z,"DOMContentLoaded",aD);Kc(z,"readystatechange",aD);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&bD()}Kc(a,"load",aD)}}function dD(a){YC?a():$C.push(a)};var eD={},fD={};function gD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={wj:void 0,cj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.wj=Fp(g,b),e.wj){var h=zm();nb(h,function(r){return function(t){return r.wj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=eD[g]||[];e.cj={};m.forEach(function(r){return function(t){r.cj[t]=!0}}(e));for(var n=Bm(),p=0;p<n.length;p++)if(e.cj[n[p]]){c=c.concat(zm());break}var q=fD[g]||[];q.length&&(c=c.concat(q))}}return{qj:c,Yp:d}}
function hD(a){sb(eD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function iD(a){sb(fD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var jD=!1,kD=!1;function lD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=ld(b,null),b[K.m.df]&&(d.eventCallback=b[K.m.df]),b[K.m.Lg]&&(d.eventTimeout=b[K.m.Lg]));return d}function mD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:yp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function nD(a,b){var c=a&&a[K.m.ld];c===void 0&&(c=tk(K.m.ld,2),c===void 0&&(c="default"));if(kb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?kb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=gD(d,b.isGtmEvent),f=e.qj,g=e.Yp;if(g.length)for(var h=oD(a),m=0;m<g.length;m++){var n=Fp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=tm().destination[q];r&&r.state===0||MB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{qj:Gp(f,b.isGtmEvent),
Eo:Gp(t,b.isGtmEvent)}}}var pD=void 0,qD=void 0;function rD(a,b,c){var d=ld(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&N(136);var e=ld(b,null);ld(c,e);Jw(Fw(Bm()[0],e),a.eventId,d)}function oD(a){for(var b=l([K.m.md,K.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Fq.C[d];if(e)return e}}
var sD={config:function(a,b){var c=mD(a,b);if(!(a.length<2)&&kb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!kd(a[2])||a.length>3)return;d=a[2]}var e=Fp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!xm.pe){var m=Dm(sm());if(Mm(m)){var n=m.parent,p=n.isDestination;h={aq:Dm(n),Up:p};break a}}h=void 0}var q=h;q&&(f=q.aq,g=q.Up);nC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?zm().indexOf(r)===-1:Bm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Lc]){var u=oD(d);if(t)MB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;pD?rD(b,v,pD):qD||(qD=ld(v,null))}else IB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(N(128),g&&N(130),b.inheritParentConfig)){var w;var y=d;qD?(rD(b,qD,y),w=!1):(!y[K.m.od]&&Zj&&pD||(pD=ld(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}il&&(Ap===1&&(Dn.mcc=!1),Ap=2);if(Zj&&!t&&!d[K.m.od]){var A=kD;kD=!0;if(A)return}jD||N(43);if(!b.noTargetGroup)if(t){iD(e.id);
var C=e.id,E=d[K.m.Og]||"default";E=String(E).split(",");for(var G=0;G<E.length;G++){var I=fD[E[G]]||[];fD[E[G]]=I;I.indexOf(C)<0&&I.push(C)}}else{hD(e.id);var M=e.id,T=d[K.m.Og]||"default";T=T.toString().split(",");for(var ca=0;ca<T.length;ca++){var P=eD[T[ca]]||[];eD[T[ca]]=P;P.indexOf(M)<0&&P.push(M)}}delete d[K.m.Og];var ha=b.eventMetadata||{};ha.hasOwnProperty(R.A.ud)||(ha[R.A.ud]=!b.fromContainerExecution);b.eventMetadata=ha;delete d[K.m.df];for(var da=t?[e.id]:zm(),ka=0;ka<da.length;ka++){var X=
d,W=da[ka],ta=ld(b,null),ra=Fp(W,ta.isGtmEvent);ra&&Fq.push("config",[X],ra,ta)}}}}},consent:function(a,b){if(a.length===3){N(39);var c=mD(a,b),d=a[1],e={},f=Eo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.rg?Array.isArray(h)?NaN:Number(h):g===K.m.fc?(Array.isArray(h)?h:[h]).map(Fo):Go(h)}b.fromContainerExecution||(e[K.m.V]&&N(139),e[K.m.La]&&N(140));d==="default"?hp(e):d==="update"?jp(e,c):d==="declare"&&b.fromContainerExecution&&gp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&kb(c)){var d=void 0;if(a.length>2){if(!kd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=lD(c,d),f=mD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=nD(d,b);if(m){var n=m.qj,p=m.Eo,q,r,t;if(F(108)){q=p.map(function(M){return M.id});r=p.map(function(M){return M.destinationId});t=n.map(function(M){return M.id});for(var u=l(zm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<
0&&t.push(w)}}else q=n.map(function(M){return M.id}),r=n.map(function(M){return M.destinationId}),t=q;nC(g,c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var C=A.value,E=ld(b,null),G=ld(d,null);delete G[K.m.df];var I=E.eventMetadata||{};I.hasOwnProperty(R.A.ud)||(I[R.A.ud]=!E.fromContainerExecution);I[R.A.Hi]=q.slice();I[R.A.Pf]=r.slice();E.eventMetadata=I;Gq(c,G,C,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.ld]=q.join(","):delete e.eventModel[K.m.ld];jD||N(43);b.noGtmEvent===void 0&&
b.eventMetadata&&b.eventMetadata[R.A.Fl]&&(b.noGtmEvent=!0);e.eventModel[K.m.Kc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){N(53);if(a.length===4&&kb(a[1])&&kb(a[2])&&jb(a[3])){var c=Fp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){jD||N(43);var f=oD();if(nb(zm(),function(h){return c.destinationId===h})){mD(a,b);var g={};ld((g[K.m.qc]=d,g[K.m.Ic]=e,g),null);Hq(d,function(h){B(function(){e(h)})},c.id,b)}else MB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},
js:function(a,b){if(a.length===2&&a[1].getTime){jD=!0;var c=mD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&kb(a[1])&&jb(a[2])){if(gg(a[1],a[2]),N(74),a[1]==="all"){N(75);var b=!1;try{b=a[2](jg.ctid,"unknown",{})}catch(c){}b||N(76)}}else N(73)},set:function(a,b){var c=void 0;a.length===2&&kd(a[1])?c=ld(a[1],null):a.length===3&&kb(a[1])&&(c={},kd(a[2])||Array.isArray(a[2])?
c[a[1]]=ld(a[2],null):c[a[1]]=a[2]);if(c){var d=mD(a,b),e=d.eventId,f=d.priorityId;ld(c,null);var g=ld(c,null);Fq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},tD={policy:!0};var vD=function(a){if(uD(a))return a;this.value=a};vD.prototype.getUntrustedMessageValue=function(){return this.value};var uD=function(a){return!a||id(a)!=="object"||kd(a)?!1:"getUntrustedMessageValue"in a};vD.prototype.getUntrustedMessageValue=vD.prototype.getUntrustedMessageValue;var wD=!1,xD=[];function yD(){if(!wD){wD=!0;for(var a=0;a<xD.length;a++)B(xD[a])}}function zD(a){wD?B(a):xD.push(a)};var AD=0,BD={},CD=[],DD=[],ED=!1,FD=!1;function GD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function HD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return ID(a)}function JD(a,b){if(!lb(b)||b<0)b=0;var c=up[Vj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function KD(a,b){var c=a._clear||b.overwriteModelFields;sb(a,function(e,f){e!=="_clear"&&(c&&wk(e),wk(e,f))});gk||(gk=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=yp(),a["gtm.uniqueEventId"]=d,wk("gtm.uniqueEventId",d));return SC(a)}function LD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(tb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function MD(){var a;if(DD.length)a=DD.shift();else if(CD.length)a=CD.shift();else return;var b;var c=a;if(ED||!LD(c.message))b=c;else{ED=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=yp(),f=yp(),c.message["gtm.uniqueEventId"]=yp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};CD.unshift(n,c);b=h}return b}
function ND(){for(var a=!1,b;!FD&&(b=MD());){FD=!0;delete qk.eventModel;sk();var c=b,d=c.message,e=c.messageContext;if(d==null)FD=!1;else{e.fromContainerExecution&&xk();try{if(jb(d))try{d.call(uk)}catch(u){}else if(Array.isArray(d)){if(kb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=tk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(tb(d))a:{if(d.length&&kb(d[0])){var p=sD[d[0]];if(p&&(!e.fromContainerExecution||!tD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=KD(n,e)||a)}}finally{e.fromContainerExecution&&sk(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=BD[String(q)]||[],t=0;t<r.length;t++)DD.push(OD(r[t]));r.length&&DD.sort(GD);delete BD[String(q)];q>AD&&(AD=q)}FD=!1}}}return!a}
function PD(){if(F(109)){var a=!Oj.ka;}var c=ND();if(F(109)){}try{var e=jg.ctid,f=x[Vj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Mw(a){if(AD<a.notBeforeEventId){var b=String(a.notBeforeEventId);BD[b]=BD[b]||[];BD[b].push(a)}else DD.push(OD(a)),DD.sort(GD),B(function(){FD||ND()})}function OD(a){return{message:a.message,messageContext:a.messageContext}}
function QD(){function a(f){var g={};if(uD(f)){var h=f;f=uD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=xc(Vj,[]),c=up[Vj]=up[Vj]||{};c.pruned===!0&&N(83);BD=Kw().get();Lw();dD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});zD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(up.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new vD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});CD.push.apply(CD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(N(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return ND()&&p};var e=b.slice(0).map(function(f){return a(f)});CD.push.apply(CD,e);if(!Oj.ka){if(F(109)){}B(PD)}}var ID=function(a){return x[Vj].push(a)};function RD(a){ID(a)};function SD(){var a,b=Tk(x.location.href);(a=b.hostname+b.pathname)&&Hn("dl",encodeURIComponent(a));var c;var d=jg.ctid;if(d){var e=xm.pe?1:0,f,g=Dm(sm());f=g&&g.context;c=d+";"+jg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Hn("tdp",h);var m=Il(!0);m!==void 0&&Hn("frm",String(m))};function TD(){(Ro()||il)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){N(179);var b=em(a.effectiveDirective);if(b){var c;var d=cm(b,a.blockedURI);c=d?am[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.ym){p.ym=!0;if(F(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Ro()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Ro()){var u=Xo("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Qo(u)}}}Nn(p.endpoint)}}dm(b,a.blockedURI)}}}}})};function UD(){var a;var b=Cm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Hn("pcid",e)};var VD=/^(https?:)?\/\//;
function WD(){var a=Em();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=$c())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(VD,"")===d.replace(VD,""))){b=g;break a}}N(146)}else N(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Hn("rtg",String(a.canonicalContainerId)),Hn("slo",String(p)),Hn("hlo",a.htmlLoadOrder||"-1"),
Hn("lst",String(a.loadScriptType||"0")))}else N(144)};function XD(){var a=[],b=Number('1')||0,c=Number('')||0;c||(c=b/100);var d=function(){var h=!1;return h}();a.push({Fm:195,Em:195,experimentId:104527906,controlId:104527907,controlId2:104898015,Ce:c,active:d,Vi:1});var e=Number('1')||
0,f=Number('')||0;f||(f=e/100);var g=function(){var h=!1;return h}();a.push({Fm:196,Em:196,experimentId:104528500,controlId:104528501,controlId2:104898016,Ce:f,active:g,Vi:0});return a};var YD={};function ZD(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Oj.R.H.add(Number(c.value))}function $D(){for(var a=l(XD()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Fm;ki[d]=c;if(c.Vi===1){var e=d,f=vn(qn.Z.po);oi(f,e);ZD(f)}else if(c.Vi===0){var g=YD;oi(g,d);ZD(g)}}};
function uE(){};var vE=function(){};vE.prototype.toString=function(){return"undefined"};var wE=new vE;function DE(a,b){function c(g){var h=Tk(g),m=Nk(h,"protocol"),n=Nk(h,"host",!0),p=Nk(h,"port"),q=Nk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function EE(a){return FE(a)?1:0}
function FE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=ld(a,{});ld({arg1:c[d],any_of:void 0},e);if(EE(e))return!0}return!1}switch(a["function"]){case "_cn":return Pg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Kg.length;g++){var h=Kg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Lg(b,c);case "_eq":return Qg(b,c);case "_ge":return Rg(b,c);case "_gt":return Tg(b,c);case "_lc":return Mg(b,c);case "_le":return Sg(b,
c);case "_lt":return Ug(b,c);case "_re":return Og(b,c,a.ignore_case);case "_sw":return Vg(b,c);case "_um":return DE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var GE=function(a,b,c,d){Wq.call(this);this.hh=b;this.Lf=c;this.qb=d;this.Sa=new Map;this.ih=0;this.ka=new Map;this.Ba=new Map;this.R=void 0;this.H=a};sa(GE,Wq);GE.prototype.N=function(){delete this.C;this.Sa.clear();this.ka.clear();this.Ba.clear();this.R&&(Sq(this.H,"message",this.R),delete this.R);delete this.H;delete this.qb;Wq.prototype.N.call(this)};
var HE=function(a){if(a.C)return a.C;a.Lf&&a.Lf(a.H)?a.C=a.H:a.C=Hl(a.H,a.hh);var b;return(b=a.C)!=null?b:null},JE=function(a,b,c){if(HE(a))if(a.C===a.H){var d=a.Sa.get(b);d&&d(a.C,c)}else{var e=a.ka.get(b);if(e&&e.pj){IE(a);var f=++a.ih;a.Ba.set(f,{Eh:e.Eh,Xo:e.dm(c),persistent:b==="addEventListener"});a.C.postMessage(e.pj(c,f),"*")}}},IE=function(a){a.R||(a.R=function(b){try{var c;c=a.qb?a.qb(b):void 0;if(c){var d=c.fq,e=a.Ba.get(d);if(e){e.persistent||a.Ba.delete(d);var f;(f=e.Eh)==null||f.call(e,
e.Xo,c.payload)}}}catch(g){}},Rq(a.H,"message",a.R))};var KE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},LE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},ME={dm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Eh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},NE={dm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Eh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function OE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,fq:b.__gppReturn.callId}}
var PE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;Wq.call(this);this.caller=new GE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},OE);this.caller.Sa.set("addEventListener",KE);this.caller.ka.set("addEventListener",ME);this.caller.Sa.set("removeEventListener",LE);this.caller.ka.set("removeEventListener",NE);this.timeoutMs=c!=null?c:500};sa(PE,Wq);PE.prototype.N=function(){this.caller.dispose();Wq.prototype.N.call(this)};
PE.prototype.addEventListener=function(a){var b=this,c=kl(function(){a(QE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);JE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(RE,!0);return}a(SE,!0)}}})};
PE.prototype.removeEventListener=function(a){JE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var SE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},QE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},RE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function TE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){sv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");sv.C=d}}function UE(){try{var a=new PE(x,{timeoutMs:-1});HE(a.caller)&&a.addEventListener(TE)}catch(b){}};function VE(){var a=[["cv",Si(1)],["rv",Tj],["tc",Gf.filter(function(b){return b}).length]];Uj&&a.push(["x",Uj]);lk()&&a.push(["tag_exp",lk()]);return a};var WE={};function Vi(a){WE[a]=(WE[a]||0)+1}function XE(){for(var a=[],b=l(Object.keys(WE)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+WE[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var YE={},ZE={};function $E(a){var b=a.eventId,c=a.Nd,d=[],e=YE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=ZE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete YE[b],delete ZE[b]);return d};function aF(){return!1}function bF(){var a={};return function(b,c,d){}};function cF(){var a=dF;return function(b,c,d){var e=d&&d.event;eF(c);var f=Ah(b)?void 0:1,g=new Ua;sb(c,function(r,t){var u=Bd(t,void 0,f);u===void 0&&t!==void 0&&N(44);g.set(r,u)});a.Nb(Zf());var h={Ol:ng(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Sf:e!==void 0?function(r){e.Pc.Sf(r)}:void 0,Jb:function(){return b},log:function(){},kp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},oq:!!RB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(aF()){var m=bF(),n,p;h.wb={Ej:[],Tf:{},ac:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Ch:Sh()};h.log=function(r){var t=za.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=Xe(a,h,[b,g]);a.Nb();q instanceof Ca&&(q.type==="return"?q=q.data:q=void 0);return Ad(q,void 0,f)}}function eF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;jb(b)&&(a.gtmOnSuccess=function(){B(b)});jb(c)&&(a.gtmOnFailure=function(){B(c)})};function fF(a){}fF.M="internal.addAdsClickIds";function gF(a,b){var c=this;}gF.publicName="addConsentListener";var hF=!1;function iF(a){for(var b=0;b<a.length;++b)if(hF)try{a[b]()}catch(c){N(77)}else a[b]()}function jF(a,b,c){var d=this,e;return e}jF.M="internal.addDataLayerEventListener";function kF(a,b,c){}kF.publicName="addDocumentEventListener";function lF(a,b,c,d){}lF.publicName="addElementEventListener";function mF(a){return a.K.sb()};function nF(a){}nF.publicName="addEventCallback";
function CF(a){}CF.M="internal.addFormAbandonmentListener";function DF(a,b,c,d){}
DF.M="internal.addFormData";var EF={},FF=[],GF={},HF=0,IF=0;
function PF(a,b){}PF.M="internal.addFormInteractionListener";
function WF(a,b){}WF.M="internal.addFormSubmitListener";
function aG(a){}aG.M="internal.addGaSendListener";function bG(a){if(!a)return{};var b=a.kp;return QB(b.type,b.index,b.name)}function cG(a){return a?{originatingEntity:bG(a)}:{}};function kG(a){var b=up.zones;return b?b.getIsAllowedFn(Bm(),a):function(){return!0}}function lG(){var a=up.zones;a&&a.unregisterChild(Bm())}
function mG(){CC(Am(),function(a){var b=up.zones;return b?b.isActive(Bm(),a.originalEventData["gtm.uniqueEventId"]):!0});AC(Am(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return kG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var nG=function(a,b){this.tagId=a;this.ve=b};
function oG(a,b){var c=this;return a}oG.M="internal.loadGoogleTag";function pG(a){return new sd("",function(b){var c=this.evaluate(b);if(c instanceof sd)return new sd("",function(){var d=za.apply(0,arguments),e=this,f=ld(mF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.rb();h.Ld(f);return c.Lb.apply(c,[h].concat(va(g)))})})};function qG(a,b,c){var d=this;}qG.M="internal.addGoogleTagRestriction";var rG={},sG=[];
function zG(a,b){}
zG.M="internal.addHistoryChangeListener";function AG(a,b,c){}AG.publicName="addWindowEventListener";function BG(a,b){return!0}BG.publicName="aliasInWindow";function CG(a,b,c){}CG.M="internal.appendRemoteConfigParameter";function DG(a){var b;return b}
DG.publicName="callInWindow";function EG(a){}EG.publicName="callLater";function FG(a){}FG.M="callOnDomReady";function GG(a){}GG.M="callOnWindowLoad";function HG(a,b){var c;return c}HG.M="internal.computeGtmParameter";function IG(a,b){var c=this;}IG.M="internal.consentScheduleFirstTry";function JG(a,b){var c=this;}JG.M="internal.consentScheduleRetry";function KG(a){var b;return b}KG.M="internal.copyFromCrossContainerData";function LG(a,b){var c;if(!lh(a)||!qh(b)&&b!==null&&!gh(b))throw H(this.getName(),["string","number|undefined"],arguments);J(this,"read_data_layer",a);c=(b||2)!==2?tk(a,1):vk(a,[x,z]);var d=Bd(c,this.K,Ah(mF(this).Jb())?2:1);d===void 0&&c!==void 0&&N(45);return d}LG.publicName="copyFromDataLayer";
function MG(a){var b=void 0;return b}MG.M="internal.copyFromDataLayerCache";function NG(a){var b;return b}NG.publicName="copyFromWindow";function OG(a){var b=void 0;return Bd(b,this.K,1)}OG.M="internal.copyKeyFromWindow";var PG=function(a){return a===Pm.X.Da&&gn[a]===Om.Ia.oe&&!Q(K.m.U)};var QG=function(){return"0"},RG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];F(102)&&b.push("gbraid");return Uk(a,b,"0")};var SG={},TG={},UG={},VG={},WG={},XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH=(qH[K.m.Qa]=(SG[2]=[PG],SG),qH[K.m.rf]=(TG[2]=[PG],TG),qH[K.m.ef]=(UG[2]=[PG],UG),qH[K.m.li]=(VG[2]=[PG],VG),qH[K.m.mi]=(WG[2]=[PG],WG),qH[K.m.ni]=(XG[2]=[PG],XG),qH[K.m.oi]=(YG[2]=[PG],YG),qH[K.m.ri]=(ZG[2]=[PG],ZG),qH[K.m.wc]=($G[2]=[PG],$G),qH[K.m.uf]=(aH[2]=[PG],aH),qH[K.m.vf]=(bH[2]=[PG],bH),qH[K.m.wf]=(cH[2]=[PG],cH),qH[K.m.xf]=(dH[2]=
[PG],dH),qH[K.m.yf]=(eH[2]=[PG],eH),qH[K.m.zf]=(fH[2]=[PG],fH),qH[K.m.Af]=(gH[2]=[PG],gH),qH[K.m.Bf]=(hH[2]=[PG],hH),qH[K.m.lb]=(iH[1]=[PG],iH),qH[K.m.Zc]=(jH[1]=[PG],jH),qH[K.m.fd]=(kH[1]=[PG],kH),qH[K.m.Xd]=(lH[1]=[PG],lH),qH[K.m.Pe]=(mH[1]=[function(a){return F(102)&&PG(a)}],mH),qH[K.m.gd]=(nH[1]=[PG],nH),qH[K.m.Aa]=(oH[1]=[PG],oH),qH[K.m.Wa]=(pH[1]=[PG],pH),qH),sH={},tH=(sH[K.m.lb]=QG,sH[K.m.Zc]=QG,sH[K.m.fd]=QG,sH[K.m.Xd]=QG,sH[K.m.Pe]=QG,sH[K.m.gd]=function(a){if(!kd(a))return{};var b=ld(a,
null);delete b.match_id;return b},sH[K.m.Aa]=RG,sH[K.m.Wa]=RG,sH),uH={},vH={},wH=(vH[R.A.ib]=(uH[2]=[PG],uH),vH),xH={};var yH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};yH.prototype.getValue=function(a){a=a===void 0?Pm.X.Fb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};yH.prototype.H=function(){return id(this.C)==="array"||kd(this.C)?ld(this.C,null):this.C};
var zH=function(){},AH=function(a,b){this.conditions=a;this.C=b},BH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new yH(c,e,g,a.C[b]||zH)},CH,DH;var EH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;U(this,g,d[g])}},Iv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,S(a,R.A.Qf))},V=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(CH!=null||(CH=new AH(rH,tH)),e=BH(CH,b,c));d[b]=e};
EH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return V(this,a,b),!0;if(!kd(c))return!1;V(this,a,Object.assign(c,b));return!0};var FH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
EH.prototype.copyToHitData=function(a,b,c){var d=O(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&kb(d)&&F(92))try{d=c(d)}catch(e){}d!==void 0&&V(this,a,d)};
var S=function(a,b){var c=a.metadata[b];if(b===R.A.Qf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,S(a,R.A.Qf))},U=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(DH!=null||(DH=new AH(wH,xH)),e=BH(DH,b,c));d[b]=e},GH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},bw=function(a,b,c){var d=Qw(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function HH(a,b){var c;return c}HH.M="internal.copyPreHit";function IH(a,b){var c=null;return Bd(c,this.K,2)}IH.publicName="createArgumentsQueue";function JH(a){return Bd(function(c){var d=$B();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
$B(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}JH.M="internal.createGaCommandQueue";function KH(a){return Bd(function(){if(!jb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Ah(mF(this).Jb())?2:1)}KH.publicName="createQueue";function LH(a,b){var c=null;if(!lh(a)||!mh(b))throw H(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new xd(new RegExp(a,d))}catch(e){}return c}LH.M="internal.createRegex";function MH(a){}MH.M="internal.declareConsentState";function NH(a){var b="";return b}NH.M="internal.decodeUrlHtmlEntities";function OH(a,b,c){var d;return d}OH.M="internal.decorateUrlWithGaCookies";function PH(){}PH.M="internal.deferCustomEvents";function QH(a){var b;return b}QH.M="internal.detectUserProvidedData";
function VH(a,b){return f}VH.M="internal.enableAutoEventOnClick";
function cI(a,b){return p}cI.M="internal.enableAutoEventOnElementVisibility";function dI(){}dI.M="internal.enableAutoEventOnError";var eI={},fI=[],gI={},hI=0,iI=0;
function oI(a,b){var c=this;return d}oI.M="internal.enableAutoEventOnFormInteraction";
function tI(a,b){var c=this;return f}tI.M="internal.enableAutoEventOnFormSubmit";
function yI(){var a=this;}yI.M="internal.enableAutoEventOnGaSend";var zI={},AI=[];
function HI(a,b){var c=this;return f}HI.M="internal.enableAutoEventOnHistoryChange";var II=["http://","https://","javascript:","file://"];
function MI(a,b){var c=this;return h}MI.M="internal.enableAutoEventOnLinkClick";var NI,OI;
function ZI(a,b){var c=this;return d}ZI.M="internal.enableAutoEventOnScroll";function $I(a){return function(){if(a.limit&&a.sj>=a.limit)a.zh&&x.clearInterval(a.zh);else{a.sj++;var b=zb();ID({event:a.eventName,"gtm.timerId":a.zh,"gtm.timerEventNumber":a.sj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Dm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Dm,"gtm.triggers":a.Jq})}}}
function aJ(a,b){
return f}aJ.M="internal.enableAutoEventOnTimer";var nc=xa(["data-gtm-yt-inspected-"]),cJ=["www.youtube.com","www.youtube-nocookie.com"],dJ,eJ=!1;
function oJ(a,b){var c=this;return e}oJ.M="internal.enableAutoEventOnYouTubeActivity";eJ=!1;function pJ(a,b){if(!lh(a)||!fh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?Ad(b):{},d=a,e=!1;return e}pJ.M="internal.evaluateBooleanExpression";var qJ;function rJ(a){var b=!1;return b}rJ.M="internal.evaluateMatchingRules";
var sJ=function(a){switch(a){case L.J.Ga:return[aw,Yv,Wv,Vv,cw,Sy,Kv,yz,lz,$v,$y,gz,Zv];case L.J.Lj:return[aw,Yv,Vv,cw,Sy];case L.J.W:return[aw,Sv,Yv,Vv,cw,uz,Dz,rz,Cz,Bz,Az,zz,yz,lz,kz,iz,hz,fz,Wy,Vy,jz,$y,qz,ez,dz,bz,tz,pz,Wv,Tv,$v,oz,az,xz,gz,sz,Uy,Zy,nz,cz,vz,wz,Xy,Zv];case L.J.Ei:return[aw,Sv,Yv,Vv,cw,uz,Dz,lz,Uv,$y,qz,tz,Tv,Wv,$v,oz,xz,gz,sz,Uy,Xy,Zv];case L.J.na:return[aw,Sv,Yv,Vv,cw,uz,Dz,rz,Cz,Bz,Az,zz,yz,lz,kz,fz,jz,$y,qz,ez,tz,Tv,Wv,$v,oz,az,xz,gz,sz,Uy,vz,Xy,Zv];case L.J.Ta:return[aw,
Sv,Yv,Vv,cw,uz,Dz,Cz,yz,lz,jz,$y,Uv,qz,bz,tz,Tv,Wv,$v,oz,az,xz,gz,sz,Uy,Xy,Zv];case L.J.Ja:return[aw,Sv,Yv,Vv,cw,uz,Dz,Cz,yz,lz,jz,$y,Uv,qz,bz,tz,Tv,Wv,$v,oz,az,xz,gz,sz,Uy,Xy,Zv];default:return[aw,Sv,Yv,Vv,cw,uz,Dz,rz,Cz,Bz,Az,zz,yz,lz,kz,iz,hz,fz,Wy,Vy,jz,$y,qz,ez,dz,bz,tz,pz,Tv,Wv,$v,oz,az,xz,gz,sz,Uy,Zy,nz,cz,vz,wz,Xy,Zv]}},tJ=function(a){for(var b=sJ(S(a,R.A.ia)),c=0;c<b.length&&(b[c](a),!a.isAborted);c++);},uJ=function(a,b,c,d){var e=new EH(b,c,d);U(e,R.A.ia,a);U(e,R.A.Ha,!0);U(e,R.A.fb,zb());
U(e,R.A.Dl,d.eventMetadata[R.A.Ha]);return e},vJ=function(a,b,c,d){function e(t,u){for(var v=l(h),w=v.next();!w.done;w=v.next()){var y=w.value;y.isAborted=!1;U(y,R.A.Ha,!0);U(y,R.A.fa,!0);U(y,R.A.fb,zb());U(y,R.A.He,t);U(y,R.A.Ie,u)}}function f(t){for(var u={},v=0;v<h.length;u={jb:void 0},v++)if(u.jb=h[v],!t||t(S(u.jb,R.A.ia)))if(!S(u.jb,R.A.fa)||S(u.jb,R.A.ia)===L.J.Ga||Q(q))tJ(h[v]),S(u.jb,R.A.Ha)||u.jb.isAborted||(AB(u.jb),S(u.jb,R.A.ia)===L.J.Ga&&(Mv(u.jb,function(){f(function(w){return w===L.J.Ga})}),
Iv(u.jb,K.m.rf)===void 0&&r===void 0&&(r=wn(qn.Z.kh,function(w){return function(){Q(K.m.V)&&(U(w.jb,R.A.Rf,!0),U(w.jb,R.A.fa,!1),V(w.jb,K.m.fa),f(function(y){return y===L.J.Ga}),U(w.jb,R.A.Rf,!1),xn(qn.Z.kh,r),r=void 0)}}(u)))))}var g=d.isGtmEvent&&a===""?{id:"",prefix:"",destinationId:"",ids:[]}:Fp(a,d.isGtmEvent);if(g){var h=[];if(d.eventMetadata[R.A.sd]){var m=d.eventMetadata[R.A.sd];Array.isArray(m)||(m=[m]);for(var n=0;n<m.length;n++){var p=uJ(m[n],g,b,d);U(p,R.A.Ha,!1);h.push(p)}}else b===K.m.qa&&
(F(24)?h.push(uJ(L.J.Ga,g,b,d)):h.push(uJ(L.J.Ei,g,b,d))),h.push(uJ(L.J.W,g,b,d)),h.push(uJ(L.J.Ta,g,b,d)),h.push(uJ(L.J.Ja,g,b,d)),h.push(uJ(L.J.na,g,b,d));var q=[K.m.U,K.m.V],r=void 0;np(function(){f();var t=F(29)&&!Q([K.m.La]);if(!Q(q)||t){var u=q;t&&(u=[].concat(va(u),[K.m.La]));mp(function(v){var w,y,A;w=v.consentEventId;y=v.consentPriorityId;A=v.consentTypes;e(w,y);A&&A.length===1&&A[0]===K.m.La?f(function(C){return C===L.J.na}):f()},u)}},q)}};function aK(){return pr(7)&&pr(9)&&pr(10)};function WK(a,b,c,d){}WK.M="internal.executeEventProcessor";function XK(a){var b;return Bd(b,this.K,1)}XK.M="internal.executeJavascriptString";function YK(a){var b;return b};function ZK(a){var b="";return b}ZK.M="internal.generateClientId";function $K(a){var b={};return Bd(b)}$K.M="internal.getAdsCookieWritingOptions";function aL(a,b){var c=!1;return c}aL.M="internal.getAllowAdPersonalization";function bL(){var a;return a}bL.M="internal.getAndResetEventUsage";function cL(a,b){b=b===void 0?!0:b;var c;return c}cL.M="internal.getAuid";var dL=null;
function eL(){var a=new Ua;J(this,"read_container_data"),F(49)&&dL?a=dL:(a.set("containerId",'AW-2121225687'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",og),a.set("previewMode",pg.Hm),a.set("environmentMode",pg.fp),a.set("firstPartyServing",nk()||Oj.N),a.set("containerUrl",wc),a.Ua(),F(49)&&(dL=a));return a}
eL.publicName="getContainerVersion";function fL(a,b){b=b===void 0?!0:b;var c;return c}fL.publicName="getCookieValues";function gL(){var a="";return a}gL.M="internal.getCorePlatformServicesParam";function hL(){return jo()}hL.M="internal.getCountryCode";function iL(){var a=[];return Bd(a)}iL.M="internal.getDestinationIds";function jL(a){var b=new Ua;return b}jL.M="internal.getDeveloperIds";function kL(a){var b;return b}kL.M="internal.getEcsidCookieValue";function lL(a,b){var c=null;return c}lL.M="internal.getElementAttribute";function mL(a){var b=null;return b}mL.M="internal.getElementById";function nL(a){var b="";return b}nL.M="internal.getElementInnerText";function oL(a,b){var c=null;return Bd(c)}oL.M="internal.getElementProperty";function pL(a){var b;return b}pL.M="internal.getElementValue";function qL(a){var b=0;return b}qL.M="internal.getElementVisibilityRatio";function rL(a){var b=null;return b}rL.M="internal.getElementsByCssSelector";
function sL(a){var b;if(!lh(a))throw H(this.getName(),["string"],arguments);J(this,"read_event_data",a);var c;a:{var d=a,e=mF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",A=l(n),C=A.next();!C.done;C=
A.next()){var E=C.value;E===m?(w.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Bd(c,this.K,1);return b}sL.M="internal.getEventData";var tL={};tL.enableDecodeUri=F(92);tL.enableGaAdsConversions=F(122);tL.enableGaAdsConversionsClientId=F(121);tL.enableOverrideAdsCps=F(170);tL.enableUrlDecodeEventUsage=F(139);function uL(){return Bd(tL)}uL.M="internal.getFlags";function vL(){var a;return a}vL.M="internal.getGsaExperimentId";function wL(){return new xd(wE)}wL.M="internal.getHtmlId";function xL(a){var b;return b}xL.M="internal.getIframingState";function yL(a,b){var c={};return Bd(c)}yL.M="internal.getLinkerValueFromLocation";function zL(){var a=new Ua;if(arguments.length!==0)throw H(this.getName(),[],arguments);var b=Fv();b!==void 0&&a.set(K.m.Cf,b||"error");var c=or();c&&a.set(K.m.jd,c);var d=nr();d&&a.set(K.m.nd,d);var e=sv.gppString;e&&a.set(K.m.hf,e);var f=sv.C;f&&a.set(K.m.ff,f);return a}zL.M="internal.getPrivacyStrings";function AL(a,b){var c;return c}AL.M="internal.getProductSettingsParameter";function BL(a,b){var c;return c}BL.publicName="getQueryParameters";function CL(a,b){var c;return c}CL.publicName="getReferrerQueryParameters";function DL(a){var b="";return b}DL.publicName="getReferrerUrl";function EL(){return ko()}EL.M="internal.getRegionCode";function FL(a,b){var c;return c}FL.M="internal.getRemoteConfigParameter";function GL(){var a=new Ua;a.set("width",0);a.set("height",0);return a}GL.M="internal.getScreenDimensions";function HL(){var a="";return a}HL.M="internal.getTopSameDomainUrl";function IL(){var a="";return a}IL.M="internal.getTopWindowUrl";function JL(a){var b="";return b}JL.publicName="getUrl";function KL(){J(this,"get_user_agent");return tc.userAgent}KL.M="internal.getUserAgent";function LL(){var a;return a?Bd(Ly(a)):a}LL.M="internal.getUserAgentClientHints";function TL(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function UL(){var a=TL();a.hid=a.hid||ob();return a.hid}function VL(a,b){var c=TL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function sM(a){(cy(a)||nk())&&V(a,K.m.Qk,ko()||jo());!cy(a)&&nk()&&V(a,K.m.bl,"::")}function tM(a){if(nk()&&!cy(a)&&(no()||V(a,K.m.Ek,!0),F(78))){Wv(a);Xv(a,Cp.Df.Tm,Ho(O(a.D,K.m.cb)));var b=Cp.Df.Um;var c=O(a.D,K.m.Hc);Xv(a,b,c===!0?1:c===!1?0:void 0);Xv(a,Cp.Df.Sm,Ho(O(a.D,K.m.yb)));Xv(a,Cp.Df.Qm,us(Go(O(a.D,K.m.nb)),Go(O(a.D,K.m.Sb))))}};var OM={AW:qn.Z.Mm,G:qn.Z.Yn,DC:qn.Z.Wn};function PM(a){var b=cj(a);return""+bs(b.map(function(c){return c.value}).join("!"))}function QM(a){var b=Fp(a);return b&&OM[b.prefix]}function RM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};var vN=window,wN=document,xN=function(a){var b=vN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||wN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&vN["ga-disable-"+a]===!0)return!0;try{var c=vN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(wN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return wN.getElementById("__gaOptOutExtension")?!0:!1};
function JN(a){sb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Wb]||{};sb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function rO(a,b){}function sO(a,b){var c=function(){};return c}
function tO(a,b,c){};var uO=sO;function wO(a,b,c){var d=this;}wO.M="internal.gtagConfig";
function yO(a,b){}
yO.publicName="gtagSet";function zO(){var a={};return a};function AO(a){}AO.M="internal.initializeServiceWorker";function BO(a,b){}BO.publicName="injectHiddenIframe";var CO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function DO(a,b,c,d,e){}DO.M="internal.injectHtml";var HO={};
function JO(a,b,c,d){}var KO={dl:1,id:1},LO={};
function MO(a,b,c,d){}F(160)?MO.publicName="injectScript":JO.publicName="injectScript";MO.M="internal.injectScript";function NO(){return oo()}NO.M="internal.isAutoPiiEligible";function OO(a){var b=!0;return b}OO.publicName="isConsentGranted";function PO(a){var b=!1;return b}PO.M="internal.isDebugMode";function QO(){return mo()}QO.M="internal.isDmaRegion";function RO(a){var b=!1;return b}RO.M="internal.isEntityInfrastructure";function SO(a){var b=!1;if(!qh(a))throw H(this.getName(),["number"],[a]);b=F(a);return b}SO.M="internal.isFeatureEnabled";function TO(){var a=!1;return a}TO.M="internal.isFpfe";function UO(){var a=!1;return a}UO.M="internal.isGcpConversion";function VO(){var a=!1;return a}VO.M="internal.isLandingPage";function WO(){var a=!1;return a}WO.M="internal.isOgt";function XO(){var a;return a}XO.M="internal.isSafariPcmEligibleBrowser";function YO(){var a=Nh(function(b){mF(this).log("error",b)});a.publicName="JSON";return a};function ZO(a){var b=void 0;return Bd(b)}ZO.M="internal.legacyParseUrl";function $O(){return!1}
var aP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function bP(){}bP.publicName="logToConsole";function cP(a,b){}cP.M="internal.mergeRemoteConfig";function dP(a,b,c){c=c===void 0?!0:c;var d=[];return Bd(d)}dP.M="internal.parseCookieValuesFromString";function eP(a){var b=void 0;return b}eP.publicName="parseUrl";function fP(a){}fP.M="internal.processAsNewEvent";function gP(a,b,c){var d;return d}gP.M="internal.pushToDataLayer";function hP(a){var b=za.apply(1,arguments),c=!1;return c}hP.publicName="queryPermission";function iP(a){var b=this;}iP.M="internal.queueAdsTransmission";function jP(a,b){var c=void 0;return c}jP.publicName="readAnalyticsStorage";function kP(){var a="";return a}kP.publicName="readCharacterSet";function lP(){return Vj}lP.M="internal.readDataLayerName";function mP(){var a="";return a}mP.publicName="readTitle";function nP(a,b){var c=this;if(!lh(a)||!hh(b))throw H(this.getName(),["string","function"],arguments);Bw(a,function(d){b.invoke(c.K,Bd(d,c.K,1))});}nP.M="internal.registerCcdCallback";function oP(a,b){return!0}oP.M="internal.registerDestination";var pP=["config","event","get","set"];function qP(a,b,c){}qP.M="internal.registerGtagCommandListener";function rP(a,b){var c=!1;return c}rP.M="internal.removeDataLayerEventListener";function sP(a,b){}
sP.M="internal.removeFormData";function tP(){}tP.publicName="resetDataLayer";function uP(a,b,c){var d=void 0;return d}uP.M="internal.scrubUrlParams";function vP(a){}vP.M="internal.sendAdsHit";function wP(a,b,c,d){}wP.M="internal.sendGtagEvent";function xP(a,b,c){}xP.publicName="sendPixel";function yP(a,b){}yP.M="internal.setAnchorHref";function zP(a){}zP.M="internal.setContainerConsentDefaults";function AP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}AP.publicName="setCookie";function BP(a){}BP.M="internal.setCorePlatformServices";function CP(a,b){}CP.M="internal.setDataLayerValue";function DP(a){}DP.publicName="setDefaultConsentState";function EP(a,b){}EP.M="internal.setDelegatedConsentType";function FP(a,b){}FP.M="internal.setFormAction";function GP(a,b,c){c=c===void 0?!1:c;}GP.M="internal.setInCrossContainerData";function HP(a,b,c){return!1}HP.publicName="setInWindow";function IP(a,b,c){}IP.M="internal.setProductSettingsParameter";function JP(a,b,c){}JP.M="internal.setRemoteConfigParameter";function KP(a,b){}KP.M="internal.setTransmissionMode";function LP(a,b,c,d){var e=this;}LP.publicName="sha256";function MP(a,b,c){}
MP.M="internal.sortRemoteConfigParameters";function NP(a){}NP.M="internal.storeAdsBraidLabels";function OP(a,b){var c=void 0;return c}OP.M="internal.subscribeToCrossContainerData";var PP={},QP={};PP.getItem=function(a){var b=null;return b};PP.setItem=function(a,b){};
PP.removeItem=function(a){};PP.clear=function(){};PP.publicName="templateStorage";function RP(a,b){var c=!1;if(!kh(a)||!lh(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof RegExp))return!1;c=d.test(b);return c}RP.M="internal.testRegex";function SP(a){var b;return b};function TP(a,b){var c;return c}TP.M="internal.unsubscribeFromCrossContainerData";function UP(a){}UP.publicName="updateConsentState";function VP(a){var b=!1;return b}VP.M="internal.userDataNeedsEncryption";var WP;function XP(a,b,c){WP=WP||new Yh;WP.add(a,b,c)}function YP(a,b){var c=WP=WP||new Yh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=jb(b)?th(a,b):uh(a,b)}
function ZP(){return function(a){var b;var c=WP;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.sb();if(e){var f=!1,g=e.Jb();if(g){Ah(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function $P(){var a=function(c){return void YP(c.M,c)},b=function(c){return void XP(c.publicName,c)};b(gF);b(nF);b(BG);b(DG);b(EG);b(LG);b(NG);b(IH);b(YO());b(KH);b(eL);b(fL);b(BL);b(CL);b(DL);b(JL);b(yO);b(BO);b(OO);b(bP);b(eP);b(hP);b(kP);b(mP);b(xP);b(AP);b(DP);b(HP);b(LP);b(PP);b(UP);XP("Math",yh());XP("Object",Wh);XP("TestHelper",$h());XP("assertApi",vh);XP("assertThat",wh);XP("decodeUri",Bh);XP("decodeUriComponent",Ch);XP("encodeUri",Dh);XP("encodeUriComponent",Eh);XP("fail",Jh);XP("generateRandom",
Kh);XP("getTimestamp",Lh);XP("getTimestampMillis",Lh);XP("getType",Mh);XP("makeInteger",Oh);XP("makeNumber",Ph);XP("makeString",Qh);XP("makeTableMap",Rh);XP("mock",Uh);XP("mockObject",Vh);XP("fromBase64",YK,!("atob"in x));XP("localStorage",aP,!$O());XP("toBase64",SP,!("btoa"in x));a(fF);a(jF);a(DF);a(PF);a(WF);a(aG);a(qG);a(zG);a(CG);a(FG);a(GG);a(HG);a(IG);a(JG);a(KG);a(MG);a(OG);a(HH);a(JH);a(LH);a(MH);a(NH);a(OH);a(PH);a(QH);a(VH);a(cI);a(dI);a(oI);a(tI);a(yI);a(HI);a(MI);a(ZI);a(aJ);a(oJ);a(pJ);
a(rJ);a(WK);a(XK);a(ZK);a($K);a(aL);a(bL);a(cL);a(hL);a(iL);a(jL);a(kL);a(lL);a(mL);a(nL);a(oL);a(pL);a(qL);a(rL);a(sL);a(uL);a(vL);a(wL);a(xL);a(yL);a(zL);a(AL);a(EL);a(FL);a(GL);a(HL);a(IL);a(LL);a(wO);a(AO);a(DO);a(MO);a(NO);a(PO);a(QO);a(RO);a(SO);a(TO);a(UO);a(VO);a(WO);a(XO);a(ZO);a(oG);a(cP);a(dP);a(fP);a(gP);a(iP);a(lP);a(nP);a(oP);a(qP);a(rP);a(sP);a(uP);a(vP);a(wP);a(yP);a(zP);a(BP);a(CP);a(EP);a(FP);a(GP);a(IP);a(JP);a(KP);a(MP);a(NP);a(OP);a(RP);a(TP);a(VP);YP("internal.IframingStateSchema",
zO());
F(104)&&a(gL);F(160)?b(MO):b(JO);F(177)&&b(jP);return ZP()};var dF;
function aQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;dF=new Ve;bQ();Cf=cF();var e=dF,f=$P(),g=new td("require",f);g.Ua();e.C.C.set("require",g);Qa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Yf(n,d[m]);try{dF.execute(n),F(120)&&hl&&n[0]===50&&h.push(n[1])}catch(r){}}F(120)&&(Qf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");jk[q]=["sandboxedScripts"]}cQ(b)}function bQ(){dF.Vc(function(a,b,c){up.SANDBOXED_JS_SEMAPHORE=up.SANDBOXED_JS_SEMAPHORE||0;up.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{up.SANDBOXED_JS_SEMAPHORE--}})}function cQ(a){a&&sb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");jk[e]=jk[e]||[];jk[e].push(b)}})};function dQ(a){Jw(Dw("developer_id."+a,!0),0,{})};var eQ=Array.isArray;function fQ(a,b){return ld(a,b||null)}function Y(a){return window.encodeURIComponent(a)}function gQ(a,b,c){Jc(a,b,c)}
function hQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Nk(Tk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function iQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function jQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=iQ(b,"parameter","parameterValue");e&&(c=fQ(e,c))}return c}function kQ(a,b,c){if(Hr()){b&&B(b)}else return Fc(a,b,c,void 0)}function lQ(){return x.location.href}function mQ(a,b){return tk(a,b||2)}function nQ(a,b){x[a]=b}function oQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}function pQ(a,b){if(Hr()){b&&B(b)}else Hc(a,b)}

var qQ={};var Z={securityGroups:{}};
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=mQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.F="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v["5"]=!0;
Z.securityGroups.rep=["google"],Z.__rep=function(a){var b=Fp(a.vtp_containerId,!0);if(b){var c,d;switch(b.prefix){case "AW":c=vJ;d=Pm.X.Da;break;case "DC":c=MJ;d=Pm.X.Da;break;case "GF":c=RJ;d=Pm.X.Fb;break;case "HA":c=XJ;d=Pm.X.Fb;break;case "UA":c=uK;d=Pm.X.Fb;break;case "MC":c=uO(b,a.vtp_gtmEventId);d=Pm.X.Fc;break;default:B(a.vtp_gtmOnFailure);return}c?(B(a.vtp_gtmOnSuccess),F(185)?Eq(a.vtp_containerId,c,d,a.vtp_remoteConfig):(Eq(a.vtp_containerId,c,d),a.vtp_remoteConfig&&Kq(a.vtp_containerId,
a.vtp_remoteConfig||{}))):B(a.vtp_gtmOnFailure)}else B(a.vtp_gtmOnFailure)},Z.__rep.F="rep",Z.__rep.isVendorTemplate=!0,Z.__rep.priorityOverride=0,Z.__rep.isInfrastructure=!1,Z.__rep["5"]=!1;
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!kb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Jg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.F="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer["5"]=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!kb(g))throw e(f,{},"Keys must be strings.");if(c!=="any"){try{if(Jg(g,
d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},T:a}})}();





Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},T:function(){return{}}}},Z.__read_container_data.F="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data["5"]=!1;










Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Gw(String(b.streamId),d,c);Jw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get["5"]=!1;



var xp={dataLayer:uk,callback:function(a){ik.hasOwnProperty(a)&&jb(ik[a])&&ik[a]();delete ik[a]},bootstrap:0};
function rQ(){wp();Gm();LB();Cb(jk,Z.securityGroups);var a=Dm(sm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;Wo(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||N(142);Pf={Po:dg}}var sQ=!1;
function go(){try{if(sQ||!Nm()){Rj();Oj.P=Ri(18,"");
Oj.qb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Oj.Sa="ad_storage|analytics_storage|ad_user_data";Oj.Ba="5770";Oj.Ba="5770";if(F(109)){}Ia[7]=!0;var a=vp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});cp(a);tp();UE();ir();zp();if(Hm()){lG();BC().removeExternalRestrictions(Am());}else{Py();Nf();Jf=Z;Kf=EE;fg=new mg;aQ();rQ();Ir();eo||(co=io());
qp();QD();cD();wD=!1;z.readyState==="complete"?yD():Kc(x,"load",yD);XC();hl&&(mq(Aq),x.setInterval(zq,864E5),mq(VE),mq(oC),mq(Xz),mq(Dq),mq($E),mq(zC),F(120)&&(mq(tC),mq(uC),mq(vC)),WE={},mq(XE),Ui());il&&(Rn(),Tp(),SD(),WD(),UD(),Hn("bt",String(Oj.C?2:Oj.N?1:0)),Hn("ct",String(Oj.C?0:Oj.N?1:Hr()?2:3)),TD());uE();ao(1);mG();$D();hk=zb();xp.bootstrap=hk;Oj.ka&&PD();F(109)&&vA();F(134)&&(typeof x.name==="string"&&Eb(x.name,"web-pixel-sandbox-CUSTOM")&&ad()?dQ("dMDg0Yz"):x.Shopify&&(dQ("dN2ZkMj"),ad()&&dQ("dNTU0Yz")))}}}catch(b){ao(4),wq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Jo(n)&&(m=h.Wk)}function c(){m&&wc?g(m):a()}if(!x[Ri(37,"__TAGGY_INSTALLED")]){var d=!1;if(z.referrer){var e=Tk(z.referrer);d=Pk(e,"host")===Ri(38,"cct.google")}if(!d){var f=es(Ri(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Ri(37,"__TAGGY_INSTALLED")]=!0,Fc(Ri(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";bk&&(v="OGT",w="GTAG");
var y=Ri(23,"google.tagmanager.debugui2.queue"),A=x[y];A||(A=[],x[y]=A,Fc("https://"+Sj.wg+"/debug/bootstrap?id="+jg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Mr()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:wc,containerProduct:v,debug:!1,id:jg.ctid,targetRef:{ctid:jg.ctid,isDestination:ym()},aliases:Bm(),destinations:zm()}};C.data.resume=function(){a()};Sj.Pm&&(C.data.initialPublish=!0);A.push(C)},h={bo:1,Zk:2,vl:3,Wj:4,Wk:5};h[h.bo]="GTM_DEBUG_LEGACY_PARAM";h[h.Zk]="GTM_DEBUG_PARAM";h[h.vl]="REFERRER";
h[h.Wj]="COOKIE";h[h.Wk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Nk(x.location,"query",!1,void 0,"gtm_debug");Jo(p)&&(m=h.Zk);if(!m&&z.referrer){var q=Tk(z.referrer);Pk(q,"host")===Ri(24,"tagassistant.google.com")&&(m=h.vl)}if(!m){var r=es("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Wj)}m||b();if(!m&&Io(n)){var t=!1;Kc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){F(83)&&sQ&&!io()["0"]?fo():go()});

})()

