!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[<PERSON>yla<PERSON>Button_Default]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[St<PERSON><PERSON>Button_Default]"]=t(require("react")):e["rb_wixui.thunderbolt[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Default]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={27232:function(e,t,a){var n=a(82016);e.exports=n.create("StylableButton2545352419",{classes:{root:"StylableButton2545352419__root",label:"StylableButton2545352419__label",link:"StylableButton2545352419__link",container:"StylableButton2545352419__container",icon:"StylableButton2545352419__icon"},vars:{},stVars:{},keyframes:{},layers:{}},"",-1,e.id)},82016:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createRenderable=t.create=void 0;t.create=function(e,t,a,n,r,o){const l={namespace:e,classes:t.classes,keyframes:t.keyframes,layers:t.layers,vars:t.vars,stVars:t.stVars,cssStates:function(e){const t=[];for(const a in e){const n=s(a,e[a]);n&&t.push(n)}return t.join(" ")},style:i,st:i,$id:r,$depth:n,$css:a};function s(t,a){if(!1===a||null==a||a!=a)return"";if(!0===a)return function(t){return`${e}--${t}`}(t);return function(t,a){return`${e}---${t}-${a.length}-${a.replace(/\s/gm,"_")}`}(t,a.toString())}function i(){const e=[];for(let t=0;t<arguments.length;t++){const a=arguments[t];if(a)if("string"==typeof a)e[e.length]=a;else if(1===t)for(const t in a){const n=s(t,a[t]);n&&(e[e.length]=n)}}return e.join(" ")}return o&&o.register(l),l},t.createRenderable=function(e,t,a){return{$css:e,$depth:t,$id:a,$theme:!0}}},5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},a={};function n(e){var r=a[e];if(void 0!==r)return r.exports;var o=a[e]={id:e,exports:{}};return t[e](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return function(){"use strict";n.r(r),n.d(r,{components:function(){return ne}});var e=n(448),t=n.n(e),a=n(5329),o=n.n(a);const l="wixui-",s=(e,...t)=>{const a=[];return e&&a.push(`${l}${e}`),t.forEach((e=>{e&&(a.push(`${l}${e}`),a.push(e))})),a.join(" ")},i=13,c=27;function u(e){return t=>{t.keyCode===e&&(t.preventDefault(),t.stopPropagation(),t.currentTarget.click())}}const d=u(32),p=u(i),b=e=>{p(e),d(e)},m=(u(c),["aria-id","aria-metadata","aria-type"]),f=(e,t)=>Object.entries(e).reduce(((e,[a,n])=>(t.includes(a)||(e[a]=n),e)),{}),v=e=>{const t=(e=>{const{role:t,tabIndex:a,tabindex:n,screenReader:r,lang:o,ariaAttributes:l={}}=e,s=Object.entries(l).reduce(((e,[t,a])=>({...e,[`aria-${t}`.toLowerCase()]:a})),{});return{role:t,tabIndex:a??n,screenReader:r,ariaAttributes:f(s,m),lang:o}})(e);return{...t.ariaAttributes,tabIndex:t.tabIndex,screenReader:t.screenReader,lang:t.lang,role:t.role}},g=({reportBiOnClick:e,onClick:t})=>(0,a.useCallback)((a=>{e?.(a),t?.(a)}),[e,t]),C=e=>Object.entries(e).reduce(((e,[t,a])=>(t.includes("data-")&&(e[t]=a),e)),{});const y=(e,t)=>e?{"data-comp":t,"data-aid":t}:{},k={root:"linkElement"};var h=a.forwardRef(((e,t)=>{const{href:n,role:r,target:o,rel:l,className:s="",children:i,linkPopupId:c,anchorDataId:u,anchorCompId:m,tabIndex:f,dataTestId:v=k.root,title:g,onClick:y,onDoubleClick:h,onMouseEnter:M,onMouseLeave:x,onFocus:w,onFocusCapture:E,onBlurCapture:B,"aria-live":A,"aria-disabled":D,"aria-label":S,"aria-labelledby":N,"aria-pressed":O,"aria-expanded":I,"aria-describedby":_,"aria-haspopup":R,"aria-current":L,dataPreview:T,dataPart:F}=e,P=void 0!==e.activateByKey?e.activateByKey:(e=>e?"SpaceOrEnter":"Space")(c);let j;switch(P){case"Enter":j=p;break;case"Space":j=d;break;case"SpaceOrEnter":j=b;break;default:j=void 0}return void 0!==n||c?a.createElement("a",{...C(e),"data-testid":v,"data-popupid":c,"data-anchor":u,"data-anchor-comp-id":m,"data-preview":T,"data-part":F,href:n||void 0,target:o,role:c?"button":r,rel:l,className:s,onKeyDown:j,"aria-live":A,"aria-disabled":D,"aria-label":S,"aria-labelledby":N,"aria-pressed":O,"aria-expanded":I,"aria-haspopup":R,"aria-describedby":_,"aria-current":L,title:g,onClick:y,onMouseEnter:M,onMouseLeave:x,onDoubleClick:h,onFocus:w,onFocusCapture:E,onBlurCapture:B,ref:t,tabIndex:c?0:f},i):a.createElement("div",{...C(e),"data-testid":v,"data-preview":T,"data-part":F,className:s,tabIndex:f,"aria-label":S,"aria-labelledby":N,"aria-haspopup":R,"aria-disabled":D,"aria-expanded":I,title:g,role:r,onClick:y,onDoubleClick:h,onMouseEnter:M,onMouseLeave:x,ref:t},i)}));const M=e=>Boolean(e&&(e.href||e.linkPopupId));let x=function(e){return e.HOVER="hover",e}({});const w="buttonContent",E="stylablebutton-label",B="stylablebutton-icon",A=(Object.keys({width:{type:"maxContent"}}),{left:"flex-start",right:"flex-end",center:"center","space-between":"space-between"}),D={start:"flex-start",end:"flex-end",center:"center",justify:"space-between"},S={"flex-start":"left","flex-end":"right",center:"center","space-between":"space-between"},N={"flex-start":"start","flex-end":"end",center:"center","space-between":"justify"};var O={root:"button",buttonLabel:"button__label",buttonIcon:"button__icon"};var I=e=>{const{label:t,icon:a,classNames:n}=e;return o().createElement("span",{className:n.container},t&&o().createElement("span",{className:n.label,"data-testid":E},t),a&&o().createElement("span",{className:n.icon,"aria-hidden":"true","data-testid":B},a))};const _=e=>a.createElement("span",{dangerouslySetInnerHTML:{__html:e||""}}),R=(e,t)=>e?e.replace(/(id="|url\(#|href="#)([^"]+)(?=[")])/g,((e,a,n)=>""+a+(t+n))):e,L=(e,t)=>["has",t,...e.split("has").slice(1)].join("");function T(e){let{hover:t={},disabled:a={},...n}=e;return{...n,...Object.fromEntries([...Object.entries(t).map((e=>{let[t,a]=e;return[L(t,"Hover"),a]})),...Object.entries(a).map((e=>{let[t,a]=e;return[L(t,"Disabled"),a]}))])}}const F={animatedSvg:"animatedSvg",animatedTagPath:"animatedPath",animateTagForward:"animateForward",animateTagBackward:"animateBackward"};let P=function(e){return e.FORWARD="Forward",e.BACKWARD="Backward",e}({});const j={[P.FORWARD]:"data-animated-end-path",[P.BACKWARD]:"data-animated-start-path"};var $=(0,a.forwardRef)(((e,t)=>{let{svgContent:n,reducedMotion:r=!1}=e;const l=(0,a.useRef)(null);(0,a.useImperativeHandle)(t,(()=>({runAnimationForward:i,runAnimationBackward:c})));const s=(0,a.useCallback)((e=>{if(!l.current)return;if(!r){const t="animateTag"+e;l.current.querySelectorAll("[data-animate-id="+F[t]+"]").forEach((e=>e.beginElement()))}l.current.querySelectorAll("path[data-animate-id="+F.animatedTagPath+"]").forEach((t=>{const a=t.getAttribute("d"),n=t.getAttribute(j[e]);if(n){const r=e===P.FORWARD?P.BACKWARD:P.FORWARD;t.getAttribute(j[r])||t.setAttribute(j[r],a||""),t.setAttribute("d",n)}}))}),[r]),i=(0,a.useCallback)((()=>{s(P.FORWARD)}),[s]),c=(0,a.useCallback)((()=>{s(P.BACKWARD)}),[s]);return o().createElement("div",{ref:l,dangerouslySetInnerHTML:{__html:n}})}));const W=(e,n)=>{const{id:r,link:o,type:l="button",svgString:s,isIconAnimated:i=!1,svgAnimatedIcon:c,label:u,isDisabled:b,className:m,isQaMode:f,fullNameCompType:k,reportBiOnClick:E,a11y:B,corvid:A,onDblClick:D,onMouseEnter:S,onMouseLeave:N,onFocus:L,onBlur:T,ariaAttributes:F,onClick:P,preventLinkNavigation:j,classNames:W,compPreviewState:H,reducedMotion:K,lang:V,direction:U}=e,q=M(o),G=j&&q,Q=!b&&P||G,{iconSvgString:J,iconCollapsed:Z,iconAnimationTriggers:z=[x.HOVER]}=A||{},X=e.semanticClassNames||O,Y=a.useRef(null),ee=(0,a.useRef)(!1),te=(0,a.useRef)(null);a.useImperativeHandle(n,(()=>({focus:()=>{var e;null==(e=Y.current)||e.focus()},blur:()=>{var e;null==(e=Y.current)||e.blur()},animateIconForward:()=>{ae()},animateIconBackward:()=>{ne()}})));const ae=(0,a.useCallback)((()=>{var e;!b&&i&&(null==(e=te.current)||e.runAnimationForward())}),[b,i]),ne=(0,a.useCallback)((()=>{var e;!b&&i&&(null==(e=te.current)||e.runAnimationBackward())}),[b,i]),re=a.useMemo((()=>{var e,t;return v({ariaAttributes:{...F,...B,label:null!=(e=null!=(t=null==F?void 0:F.label)?t:B.label)?e:u},tabindex:null==B?void 0:B.tabindex})}),[B,u,F]),oe=(0,a.useCallback)((e=>{z.includes(x.HOVER)&&ae(),null==S||S(e)}),[z,ae,S]),le=(0,a.useCallback)((e=>{z.includes(x.HOVER)&&ne(),null==N||N(e)}),[z,ne,N]),se=g({reportBiOnClick:E,onClick:Q?e=>{G&&e.preventDefault(),!b&&(null==P||P(e))}:void 0}),ie=a.useMemo((()=>((e,t,a)=>{let{onClick:n,onDblClick:r,onMouseEnter:o,onMouseLeave:l,onFocus:s,onBlur:i}=e;return{onClick:n,onMouseEnter:o,onMouseLeave:l,onKeyDown:t?d:p,onDoubleClick:!a&&r?r:void 0,onFocus:!a&&s?s:void 0,onBlur:!a&&i?i:void 0}})({onClick:se,onDblClick:D,onMouseLeave:le,onMouseEnter:oe,onFocus:L,onBlur:T},q,b)),[se,D,le,oe,L,T,q,b]),ce=W.root;ee.current=!1;const ue=(0,a.useMemo)((()=>{if(!Z&&null!==J){if(J)return ee.current=!0,_(R(J,r));if(s)return _(R(s,r))}return null}),[Z,J,r,s]),de=(0,a.useMemo)((()=>i&&c?a.createElement($,{svgContent:c,reducedMotion:K,ref:te}):null),[i,K,c]),pe=(0,a.useMemo)((()=>i?de:ue),[i,de,ue]),be=n=>a.createElement("div",t()({id:r,className:m},y(f,k),C(e),{"data-semantic-classname":X.root,lang:V},U&&{dir:U}),a.createElement("button",t()({type:l,disabled:b,className:ce,"data-testid":w},re,ie,{ref:Y,role:n,"data-preview":H}),a.createElement(I,{label:u,icon:pe,override:ee.current,semanticClassNames:X,classNames:W})));return b&&q?be("link"):q?(()=>{const{onFocus:n,onBlur:l,...s}=ie;return a.createElement("div",t()({id:r,className:m},s,C(e),y(f,k),{"data-semantic-classname":X.root},U&&{dir:U}),a.createElement(h,t()({},o,re,{href:b?void 0:o.href,className:W.link,onFocusCapture:n,onBlurCapture:l,ref:Y,dataPreview:H}),a.createElement(I,{label:u,icon:pe,semanticClassNames:X,classNames:W})))})():be()};var H=a.forwardRef(W),K=n(27232);var V=e=>{const{label:t,icon:a,override:n,semanticClassNames:r}=e;return o().createElement("span",{className:K.classes.container},t&&o().createElement("span",{className:(0,K.st)(K.classes.label,s(r.buttonLabel)),"data-testid":E},t),a&&o().createElement("span",{className:(0,K.st)(K.classes.icon,{override:!!n},s(r.buttonIcon)),"aria-hidden":"true","data-testid":B},a))};const U=(e,n)=>{const{id:r,link:o,type:l="button",svgString:i,label:c,isDisabled:u,className:b,stylableButtonClassName:m,customClassNames:f=[],isQaMode:k,fullNameCompType:x,reportBiOnClick:E,a11y:B,corvid:A,isMaxContent:D=!1,isWrapText:S=!1,onDblClick:N,onMouseEnter:I,onMouseLeave:L,onFocus:F,onBlur:P,ariaAttributes:j,onClick:$,preventLinkNavigation:W,lang:H}=e,U=M(o),q=W&&U,G=!u&&$||q,Q=e.semanticClassNames||O,J=a.useRef(null);a.useImperativeHandle(n,(()=>({focus:()=>{var e;null==(e=J.current)||e.focus()},blur:()=>{var e;null==(e=J.current)||e.blur()}})));const Z=a.useMemo((()=>{var e,t;return v({ariaAttributes:{...j,...B,label:null!=(e=null!=(t=null==j?void 0:j.label)?t:B.label)?e:c},tabindex:null==B?void 0:B.tabindex})}),[B,c,j]),z=g({reportBiOnClick:E,onClick:G?e=>{q&&e.preventDefault(),!u&&(null==$||$(e))}:void 0}),X=a.useMemo((()=>((e,t,a)=>{let{onClick:n,onDblClick:r,onMouseEnter:o,onMouseLeave:l,onFocus:s,onBlur:i}=e;return{onClick:n,onMouseEnter:o,onMouseLeave:l,onKeyDown:t?d:p,onDoubleClick:!a&&r?r:void 0,onFocus:!a&&s?s:void 0,onBlur:!a&&i?i:void 0}})({onClick:z,onDblClick:N,onMouseLeave:L,onMouseEnter:I,onFocus:F,onBlur:P},U,u)),[z,N,L,I,F,P,U,u]),{iconSvgString:Y,iconCollapsed:ee,...te}=A||{},ae=(0,K.st)(K.classes.root,{error:!1,disabled:u,isMaxContent:D,isWrapText:S,...T(te)},m,s(Q.root,...f));let ne=null,re=!1;ee||null===Y||(Y?(ne=_(R(Y,r)),re=!0):i&&(ne=_(R(i,r))));const oe=n=>a.createElement("div",t()({id:r,className:b},y(k,x),C(e),{"data-semantic-classname":Q.root}),a.createElement("button",t()({type:l,disabled:u,className:ae,"data-testid":w},Z,X,{ref:J,role:n}),a.createElement(V,{label:c,icon:ne,override:re,semanticClassNames:Q})));return u&&U?oe("link"):U?(()=>{const{onFocus:n,onBlur:l,...s}=X;return a.createElement("div",t()({id:r,className:b},s,C(e),y(k,x),{"data-semantic-classname":Q.root,lang:H}),a.createElement(h,t()({},o,Z,{href:u?void 0:o.href,className:(0,K.st)(ae,K.classes.link),onFocusCapture:n,onBlurCapture:l,ref:J}),a.createElement(V,{label:c,icon:ne,semanticClassNames:Q})))})():oe()};var q=a.forwardRef(U);const G=(e,a)=>{const{isDisabled:n,stylableButtonClassName:r,customClassNames:l=[],corvid:i,isMaxContent:c=!1,isWrapText:u=!1,isUdpExperimentOn:d}=e;if(!d)return o().createElement(q,t()({},e,{ref:a}));const{iconSvgString:p,iconCollapsed:b,...m}=i||{},f=e.semanticClassNames||O,v=(0,K.st)(K.classes.root,{error:!1,disabled:n,isMaxContent:c,isWrapText:u,...T(m)},r,s(f.root,...l)),g=(0,K.st)(v,K.classes.link),C=(0,K.st)(K.classes.label,s(f.buttonLabel));let y=!1;b||null===p||p&&(y=!0);const k=(0,K.st)(K.classes.icon,{override:y},s(f.buttonIcon)),h={...e,classNames:{root:v,link:g,label:C,icon:k,container:(0,K.st)(K.classes.container)}};return o().createElement(H,t()({},h,{ref:a}))};var Q=o().forwardRef(G);const J=e=>e.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)),Z=e=>"linkPopupId"in e,z=(e,t)=>{if(Z(e))return e.linkPopupId;{const{pagesMap:a,mainPageId:n}=t||{};if(!a)return;const r=new URL(e.href??"");let o=Object.values(a).find((({pageUriSEO:e})=>!!e&&r.pathname?.includes(e)));return o||(o=n?a[n]:void 0),o?.pageId}},X=e=>{if(void 0!==e)return null===e?"None":e.type},Y=(e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return e.anchorDataId;case"DocumentLink":return e.docInfo?.name;case"PageLink":const a=z(e,t);return a&&t?.pagesMap?.[a]?.title;default:return e.href}},ee=(e,t,a)=>{const{link:n,value:r,details:o,actionName:l,elementType:s,trackClicksAnalytics:i,pagesMetadata:c,...u}=t;if(!i)return;const d=c&&{...c,pagesMap:window.viewerModel?.siteFeaturesConfigs?.router?.pagesMap},p=((e,t)=>{if(!e?.type)return;const{type:a}=e;switch(a){case"AnchorLink":return(e=>"anchorDataId"in e&&("SCROLL_TO_TOP"===e.anchorDataId||"SCROLL_TO_BOTTOM"===e.anchorDataId))(e)?void 0:{id:e.anchorDataId};case"DocumentLink":return{id:e.docInfo?.docId};case"PageLink":return{id:z(e,t),isLightbox:Z(e)};default:return}})(n,d),b=o||p?JSON.stringify({...p,...o}):void 0;e({src:76,evid:1113,...{...u,bl:navigator.language,url:window.location.href,details:b,elementType:s??"Unknown",actionName:l??X(n),value:r??Y(n,d)}},{endpoint:"pa",...a})};var te;!function(e){e.Text="Text",e.Menu="Menu",e.Image="Image",e.Input="Input",e.Login="Login",e.Button="Button",e.Social="Social",e.Gallery="Gallery",e.Community="Community",e.Decorative="Decorative",e.MenuAndSearch="MenuAndSearch",e.MenuAndAnchor="MenuAndAnchor"}(te||(te={}));var ae;const ne={StylableButton_Default:{component:Q,controller:(ae=e=>{let{mapperProps:t,stateValues:a}=e;const{trackClicksAnalytics:n,compId:r,language:o,mainPageId:l,...s}=t,{reportBi:i,reducedMotion:c}=a;return{...s,reportBiOnClick:e=>{const{fullNameCompType:t,label:a,link:c,isDisabled:u}=s;ee(i,{link:c,language:o,trackClicksAnalytics:n,elementTitle:null!=a?a:"",elementType:t,pagesMetadata:{mainPageId:l},elementGroup:te.Button,details:{isDisabled:null!=u&&u},element_id:null!=r?r:e.currentTarget.id})},reducedMotion:c}},{useComponentProps:(e,t,a)=>{const n=(e=>({...e,updateStyles:t=>{const a=Object.entries(t).reduce(((e,[t,a])=>{return{...e,[(n=t,n.startsWith("--")?t:J(t))]:void 0===a?null:a};var n}),{});e.updateStyles(a)}}))(a);return ae({mapperProps:e,stateValues:t,controllerUtils:n})}})}}}(),r}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[StylableButton_Default].e9a3f61d.bundle.min.js.map